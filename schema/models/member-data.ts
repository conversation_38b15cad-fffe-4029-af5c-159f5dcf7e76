import { z } from 'zod'

export const MemberData = z.object({
    id: z.number(),

    /**
     * First name
     *
     * example: Max
     */
    firstname: z.string(),

    /**
     * Last name
     *
     * example: Musterfrau
     */
    lastname: z.string(),

    /**
     * Personnel number
     *
     * example: 2015010803
     */
    personnelNumber: z.string(),

    /**
     * Leading organisation
     */
    leadingOrganisation: z.any(),

    /**
     * Prefix
     *
     * example: Dr.
     */
    prefix: z.string().optional(),

    /**
     * Suffix
     *
     * example: B. A.
     */
    suffix: z.string().optional(),

    /**
     * Additional first name
     *
     * Example: U. or Ulf
     */
    additionalFirstname: z.string().optional(),

    /**
     * Birthname
     *
     * example: Mustermann
     */
    birthname: z.string().optional(),

    /**
     * Place of birth
     *
     * Example: Köln
     */
    placeOfBirth: z.string().optional(),

    /**
     * Gender
     *
     * - M: male
     * - W: female
     * - D: divers
     * - UNDEFINED
     */
    gender: z.union([z.literal('M'), z.literal('W'), z.literal('D'), z.literal('UNDEFINED')]),

    /**
     * Nationality
     *
     * https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/members/MemberAttribute.yaml
     */
    nationality: z.any().optional(),

    /**
     * Letter salutation
     */
    letterSalution: z.any().optional(),

    /**
     * Salutation
     *
     * sic!
     */
    salution: z.any().optional(),

    /**
     * Address
     */
    address: z.any().optional(),

    /**
     * Birthday
     */
    birthday: z.coerce.date().optional(),

    /**
     * Age
     */
    age: z.number().optional(),

    /**
     * Entry date
     */

    entryDate: z.coerce.date(),

    /**
     * Avatar
     */
    avatar: z.string().nullable().default(null)
})

export const BasicMemberData = MemberData.pick({
    id: true,
    firstname: true,
    lastname: true,
    personnelNumber: true,
    leadingOrganisation: true,
    avatar: true
})
