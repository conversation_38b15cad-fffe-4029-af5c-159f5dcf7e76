import { z, ZodUnion } from 'zod'

import { BasicMemberData } from './member-data'

import { TechnicArticle, ExternalTechnicArticle } from './technic-article'

import { ExternalPerson } from './external-person'

const ResourceCommons = z.object({
    id: z.number().or(z.undefined()),

    type: z.union([z.literal('PERSONAL'), z.literal('TECHNIC')]),

    external: z.boolean(),

    avatar: z.string().nullable().default(null)
})

export const MemberResource = ResourceCommons.merge(
    z.object({
        external: z.literal(false),

        type: z.literal('PERSONAL'),

        member: BasicMemberData,

        article: z.null(),

        externalPerson: z.null(),

        externalArticle: z.null()
    })
)

export const ArticleResource = ResourceCommons.merge(
    z.object({
        external: z.literal(false),

        type: z.literal('TECHNIC'),

        article: TechnicArticle,

        member: z.null(),

        externalPerson: z.null(),

        externalArticle: z.null()
    })
)

export const ExternalPersonResource = ResourceCommons.merge(
    z.object({
        external: z.literal(true),

        type: z.literal('PERSONAL'),

        externalPerson: ExternalPerson,

        member: z.null(),

        article: z.null(),

        externalArticle: z.null()
    })
)

export const ExternalArticleResource = ResourceCommons.merge(
    z.object({
        external: z.literal(true),

        type: z.literal('TECHNIC'),

        externalArticle: ExternalTechnicArticle,

        member: z.null(),

        article: z.null(),

        externalPerson: z.null()
    })
)

export const Resource = z.union([MemberResource, ArticleResource, ExternalPersonResource, ExternalArticleResource]).describe('Hallo Welt')
