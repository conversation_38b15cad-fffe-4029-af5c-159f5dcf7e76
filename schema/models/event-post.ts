import { z } from 'zod'

/**
 *
 */
export const EventPost = z.object({
    id: z.number(),

    description: z.string(),

    eventPostResourceType: z.union([z.literal('PERSONAL'), z.literal('TECHNIC'), z.literal('ROOM'), z.literal('GROUP')]),

    dateFrom: z.coerce.date().optional(),

    dateUpTo: z.coerce.date().optional(),

    required: z.boolean().optional(),

    extendedPermission: z.boolean().optional(),

    remark: z.string().nullable().optional(),

    desiredValue: z.number().optional(),

    currentValue: z.number().optional(),

    age: z.number().nullable().optional(),

    driverLicenseCriteriaList: z.number().array(),

    qualifications: z.number().array(),

    technicArticleTypes: z.number().array(),

    technicArticleUnitTypes: z.number().array(),

    technicArticleUnitCategories: z.number().array()
})

/**
 * That's extracted from the actual response which looks like that:
 *
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/drkserver.api.openapi.full.yaml#/event/getEventResourceSettingConflictsByEventId
 *
 * ````
 * type EventPostConflicts = {
 *   eventPostId: number;
 *   eventPostConflicts: EventPostConflict[]
 * }[]
 * ```
 */
export const EventPostConflict = z.object({
    /**
     * The resource setting that triggered the conflict
     */
    resourceSettingId: z.number(),

    /**
     * Reasons of this conflict
     *
     * E.g. "Einsatzqualifikationen > Qualifikation: Fachhelfer CBRN"
     */
    conflictValues: z.array(
        z.object({
            field: z.string(),
            value: z.string()
        })
    )
})
