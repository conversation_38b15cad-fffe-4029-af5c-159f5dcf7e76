import { z } from 'zod'

export const ExternalPerson = z.object({
    id: z.number().optional(),

    firstname: z.string(),

    lastname: z.string(),

    telephone: z.string().nullable().default(null),

    mail: z.string().nullable().default(null),

    availableFrom: z.coerce.date().nullable().default(null),

    availableTo: z.coerce.date().nullable().default(null),

    driverLicenses: z.string().nullable().default(null),

    remark: z.string().nullable().default(null),

    operationQualifications: z.object({}).passthrough().array()
})
