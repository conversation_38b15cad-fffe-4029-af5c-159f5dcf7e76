import { z } from 'zod'

const TechnicCategory = z.object({
    id: z.number(),

    name: z.string()
})

const TechnicArticleCommons = z.object({
    /**
     * The ID
     */
    id: z.number().optional(),

    /**
     * Modul
     */
    type: TechnicCategory.nullable().default(null),

    /**
     * Art
     */
    unitType: TechnicCategory.nullable().default(null),

    /**
     * Typ
     */
    unitCategory: TechnicCategory.nullable().default(null)
})

export const TechnicArticle = TechnicArticleCommons.merge(
    z.object({
        identification: z.string(),

        number: z.string().optional(),

        warehouse: z.any().optional(),

        avatar: z.string().nullable().default('null')
    })
)

export const ExternalTechnicArticle = TechnicArticleCommons.merge(
    z.object({
        name: z.string(),

        functional: z.boolean(),

        availableFrom: z.coerce.date().optional(),

        availableTo: z.coerce.date().optional(),

        remark: z.string().optional(),
    })
)
