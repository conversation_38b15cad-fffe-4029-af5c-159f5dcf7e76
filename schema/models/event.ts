import { z } from 'zod'
import { CodeEntry } from './code-entry'

const CodeEntriesArray = z.array(CodeEntry);

export const InternalPublicationType = z.object({
    id: z.number().optional(),
    remark: z.string(),
    statusAtDrk: CodeEntriesArray,
    typesOfMembership: CodeEntriesArray,
    membershipEntries: CodeEntriesArray,
    functions: CodeEntriesArray,
    membershipGroups: CodeEntriesArray,
    membershipGroupDescriptions: CodeEntriesArray,
    operations: CodeEntriesArray,
    operationDescriptions: CodeEntriesArray,
    operationQualifications: CodeEntriesArray,
})

export const EventCancelReason = z.object({
    reasonOfCancellation: CodeEntry,
    remarkOfCancellation: z.string().optional()
})
