import { z } from 'zod'

import { CodeEntry } from './code-entry'

export const DriverLicense = z.object({
    id: z.number(),

    /**
     *  Last license control
     */
    lastLicenseControl: z.any(),

    /**
     * Next license control
     *
     * Not in use yet
     */
    nextLicenseControl: z.any(),

    /**
     * Type of licenses that are containen in `acquiredLicenseClasses`
     */
    type: z.union([z.literal('Strasse'), z.string()]),

    /**
     * Acquired license classes
     *
     * This is the list of licenses we'll display
     */
    acquiredLicenseClasses: z.array(
        z.object({
            id: z.number(),

            /**
             * Not in use
             */
            validityFrom: z.any(),

            /**
             * Not in use
             */
            validityTo: z.any(),

            /**
             * The actual license is a code entry (of list VALUELIST_USERDRIVERSLICENCE)
             */
            licenseClass: CodeEntry,

            /**
             * Extensions and restrictions for the license class as code entries (of type VALUELIST_LICENCEEXTENSIONSRESTRICTIONS)
             */
            extensionsAndRestrictions: z.array(CodeEntry)
        })
    ),

    /**
     * Not in use yet
     */
    driversLicenseExtensionsAndRestrictions: z.array(z.any()),

    /**
     * Not in use yet
     *
     */
    lastControl: z.any()
})
