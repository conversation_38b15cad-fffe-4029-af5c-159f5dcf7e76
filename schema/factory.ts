import { z } from 'zod'

import * as technicArticleSchemes from './models/technic-article'
import * as resourceSchemes from './models/resource'
import * as eventPostSchemes from './models/event-post'
import * as codeEntrySchemes from './models/code-entry'
import * as eventSchemes from './models/event'

const Schemes = {
    ...resourceSchemes,
    ...technicArticleSchemes,
    ...eventPostSchemes,
    ...codeEntrySchemes,
    ...eventSchemes
} as const

// This could be the replacement for the model layer in `/modules/api`
type ApiModel<T extends keyof typeof Schemes> = z.infer<(typeof Schemes)[T]>

export type ApiSchema<T extends keyof typeof Schemes> = z.infer<(typeof Schemes)[T]>
