version: "3"

services:
  drkserver-em-light:
    image: gitlab-docker.mogic.com/{{.CI_PROJECT_NAMESPACE}}/{{.CI_PROJECT_NAME}}:{{.CI_COMMIT_REF_SLUG}}
    links:
      - redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.drk-{{.CI_COMMIT_REF_SLUG}}.rule=Host(`{{.CI_COMMIT_REF_SLUG}}.drkserver.mogic-server.de`)"
      - "traefik.http.routers.drk-{{.CI_COMMIT_REF_SLUG}}.entrypoints=web"
      - "traefik.http.middlewares.drk-{{.CI_COMMIT_REF_SLUG}}-redirect.redirectscheme.scheme=https"
      - "traefik.http.middlewares.drk-{{.CI_COMMIT_REF_SLUG}}-redirect.redirectscheme.permanent=true"
      - "traefik.http.routers.drk-{{.CI_COMMIT_REF_SLUG}}.middlewares=drk-{{.CI_COMMIT_REF_SLUG}}-redirect"
      - "traefik.http.routers.drk-{{.CI_COMMIT_REF_SLUG}}-secure.rule=Host(`{{.CI_COMMIT_REF_SLUG}}.drkserver.mogic-server.de`)"
      - "traefik.http.routers.drk-{{.CI_COMMIT_REF_SLUG}}-secure.entrypoints=websecure"
      - "traefik.http.routers.drk-{{.CI_COMMIT_REF_SLUG}}-secure.tls=true"
      - "traefik.http.routers.drk-{{.CI_COMMIT_REF_SLUG}}-secure.tls.certresolver=letsencryptresolver"
      - "traefik.http.routers.drk-{{.CI_COMMIT_REF_SLUG}}-secure.middlewares=drk-{{.CI_COMMIT_REF_SLUG}}-auth,secHeaders@file"
      - "traefik.http.middlewares.drk-{{.CI_COMMIT_REF_SLUG}}-auth.basicauth.users=drkserver:$$apr1$$p.2P4fym$$rSF46ylAY8WNet6S1zuJo."
    logging:
      driver: "gelf"
      options:
        gelf-address: "udp://logs.mogic.com:12201"
  redis:
    image: redis:latest

networks:
  default:
    external:
      name: traefik
