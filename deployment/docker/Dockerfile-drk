FROM node:20 AS builder

WORKDIR /build

COPY . .

ENV PYTHONUNBUFFERED=1

RUN yarn install --prefer-offline --frozen-lockfile --non-interactive --production=false \
    && yarn build

FROM node:20-alpine

WORKDIR /app

COPY --from=0 /build/.output /app/.output
COPY --from=0 /build/package.json /app/

COPY deployment/docker/conf /etc/mogic/baseconf/

RUN apk add supervisor py-pip git\
    && pip install --break-system-packages git+https://github.com/coderanger/supervisor-stdout\
    && pip install --break-system-packages setuptools\
    && pip install --break-system-packages wheel\
    && chmod +x /etc/mogic/baseconf/start.sh\
    && mkdir -p /etc/supervisor/conf.d/\
    && mkdir -p /var/log/supervisor/\
    && ln -sf /etc/mogic/baseconf/supervisor/supervisord.conf /etc/supervisor/\
    && ln -s /etc/mogic/baseconf/supervisor/conf.d/* /etc/supervisor/conf.d/

ENV HOST=0.0.0.0
EXPOSE 3000

CMD ["/etc/mogic/baseconf/start.sh"]
