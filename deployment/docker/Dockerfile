FROM node:20-alpine

WORKDIR /app

ENV PYTHONUNBUFFERED=1

COPY deployment/docker/conf /etc/mogic/baseconf/

RUN apk add supervisor py-pip git\
    && pip install --break-system-packages git+https://github.com/coderanger/supervisor-stdout\
    && pip install --break-system-packages setuptools\
    && pip install --break-system-packages wheel\
    && chmod +x /etc/mogic/baseconf/start.sh\
    && mkdir -p /etc/supervisor/conf.d/\
    && mkdir -p /var/log/supervisor/\
    && ln -sf /etc/mogic/baseconf/supervisor/supervisord.conf /etc/supervisor/\
    && ln -s /etc/mogic/baseconf/supervisor/conf.d/* /etc/supervisor/conf.d/

COPY .output ./.output
COPY package.json ./package.json

ENV HOST 0.0.0.0
EXPOSE 3000

CMD ["/etc/mogic/baseconf/start.sh"]
