<script lang="ts" setup>
    definePageMeta({
        middleware: ['requires-auth', 'redirects']
    })

    const { $sentryCaptureException } = useNuxtApp()

    const {
        public: { baseUrl }
    } = useRuntimeConfig()

    const full = useRuntimeConfig()

    const sentryIssue = useState('sentryIssue', () => 'The issue')

    if (process.server) {
        try {
            // noinspection ExceptionCaughtLocallyJS
            throw new Error('Forced exception: This comes from Nuxt')
        } catch (error) {
            const issue = $sentryCaptureException(error)
            console.log(issue)
            sentryIssue.value = issue
        }
    }
    const error = function () {
        throw Error('Client error')
    }
</script>

<template>
    <div>
        <div>Base Url: {{ baseUrl }}</div>

        <div>
            <button @click="error">Throw Error</button>
        </div>

        <dl>
            <dt>Server generated Issue</dt>
            <dd>{{ sentryIssue }}</dd>
        </dl>
    </div>
</template>
