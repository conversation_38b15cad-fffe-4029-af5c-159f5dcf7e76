<template>
    <div class="flex h-full flex-1 flex-col gap-y-4">
        <div class="flex flex-none items-end justify-between">
            <h2 class="flex-1 text-xl">Pagination</h2>
            <div class="flex gap-x-4">
                <UiCheckInput v-model="shouldScroll" label="Scroll into view"> Be<PERSON> nach oben scrollen? </UiCheckInput>
                <input type="text" class="form-input" v-model.number="total" />
            </div>
        </div>
        <div class="flex-1">
            <ul ref="list" class="grid grid-cols-1 gap-y-4 overflow-scroll">
                <li v-for="item in chunk" :key="item.id" class="rounded-sm border px-4 py-2">
                    {{ item.name }}
                </li>
            </ul>
        </div>

        <div class="inset-x flex flex-none justify-between p-4">
            <UiPaginationSlider v-model="currentPage" :page-count="pageCount" class="flex-1" />
            <div class="flex gap-x-4">
                <UiRadioInput v-model="currentPageSize" :value="5" label="5" />
                <UiRadioInput v-model="currentPageSize" :value="10" label="10" />
                <UiRadioInput v-model="currentPageSize" :value="20" label="20" />
                <UiRadioInput v-model="currentPageSize" :value="50" label="50" />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
    import { range } from 'lodash-es'

    definePageMeta({
        title: 'Demo / Pagination',
        middleware: ['requires-auth', 'redirects']
    })

    const total = ref(62)

    const items = computed(() => {
        return range(0, total.value).map((no) => ({
            id: `item-${no}`,
            name: `Eintrag ${no + 1}`
        }))
    })

    const shouldScroll = ref(true)
    const list = ref<HTMLElement>()

    const { chunk, currentPage, currentPageSize, pageCount } = usePagination(items, 10)

    watch(
        () => currentPage.value,
        () => {
            if (shouldScroll.value) {
                list.value.scrollIntoView({ block: 'start', behavior: 'smooth' })
            }
        }
    )
</script>

<style scoped>
    .list-enter-active {
        transition: all 0.4s ease;
    }
    /* .list-leave-active {

        } */
    .list-enter-from {
        opacity: 0;
        transform: translateX(18px);
    }

    /* .list-leave-to {

        } */
</style>
