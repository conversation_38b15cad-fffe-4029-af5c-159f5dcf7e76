<template>
    <div>
        <div class="mb-4 grid w-full max-w-4xl grid-cols-2 gap-4 rounded border p-4">
            <UiFormField label="Simple date">
                <UiDateInput v-model="simpleDate1" /><br />
                <span class="text-xs">{{ $formatDateTime(simpleDate1, { timeStyle: 'long' }) || 'leer' }}</span>
            </UiFormField>

            <UiFormField label="Simple date (is end date)">
                <UiDateInput v-model="simpleDate2" :is-end-date="true" /><br />
                <span class="text-xs">{{ $formatDateTime(simpleDate2, { timeStyle: 'long' }) || 'leer' }}</span>
            </UiFormField>

            <UiFormField label="Simple date-time">
                <UiDateTimePicker v-model="simpleDateTime1" /><br />
                <span class="text-xs">{{ $formatDateTime(simpleDateTime1, { timeStyle: 'long' }) || 'leer' }}</span>
            </UiFormField>

            <UiFormField label="Simple date-time (is end date)">
                <UiDateTimePicker v-model="simpleDateTime2" :is-end-date="true" /><br />
                <span class="text-xs">{{ $formatDateTime(simpleDateTime2, { timeStyle: 'long' }) || 'leer' }}</span>
            </UiFormField>
        </div>

        <div class="mb-4 grid w-full max-w-4xl grid-cols-1 gap-4 rounded border p-4">
            <UiIntervalInput
                label="Interval – As styled ui component"
                v-model:start="startDate"
                v-model:end="endDate"
                :boundaries="{ start: new Date('2023-05-01'), end: new Date('2023-05-07') }" />

            <UiIntervalInput
                v-model:start="startDate"
                v-model:end="endDate"
                :boundaries="{ start: new Date('2023-06-01'), end: new Date('2023-06-07') }">
                <template #default="{ startInputAttrs, endInputAttrs }">
                    <div class="flex items-center gap-2">
                        <span class="text-xs">As headless component (witth boundaries):</span>
                        <UiDateTimePicker v-bind="startInputAttrs" @update:model-value="(value) => (startDate = value)" />
                        /
                        <UiDateTimePicker v-bind="endInputAttrs" @update:model-value="(value) => (endDate = value)" />
                    </div>
                </template>
            </UiIntervalInput>

            <UiIntervalInput label="Interval – without boundaries" v-model:start="startDate" v-model:end="endDate" />

            <UiFormField label="Date range with headless composition">
                <UiIntervalInput v-model:start="startDate2" v-model:end="endDate2">
                    <template #default="{ startInputAttrs, endInputAttrs }">
                        <div class="flex items-center gap-2">
                            <UiDateInput v-bind="startInputAttrs" @update:model-value="(value) => (startDate2 = value)" />
                            /
                            <UiDateInput v-bind="endInputAttrs" @update:model-value="(value) => (endDate2 = value)" />
                        </div>
                        <div class="text-sm">
                            {{ $formatDateRange(startDate2, endDate2) }}
                        </div>
                    </template>
                </UiIntervalInput>
            </UiFormField>
        </div>

        <div class="mt-6 grid w-full max-w-4xl grid-cols-2 gap-4 rounded border p-4">
            <UiDropUpload label="File Upload" />

            <UiComboxInput label="Combobox multiple" v-model="multiple" :options="values" display-summary />

            <UiComboxInput label="Combobox single" v-model="single" :options="values" />

            <UiComboxInput label="Combobox datalist" v-model="single" :options="values" :datalist="true" />

            <UiIntegerInput label="Integer Input" v-model="number" />

            <UiListboxInput label="Listbox" v-model="single" :options="values" :datalist="true" />

            <UiRadioInput label="Apple" v-model="single" :value="values[0]"> Use this radio to select apples </UiRadioInput>

            <UiRadioInput label="Porsche" v-model="single" :value="values[1]"> Use this radio to select porsche </UiRadioInput>

            <UiCheckInput label="Checkbox example" v-model="checkbox" :value="checkbox" />

            <UiFormField label="Simple text">
                <input type="text" class="form-input box-border w-full" />
            </UiFormField>

            <UiFormField label="Outline-Button">
                <button class="form-button button-outline">Outline Button<span>⬌</span></button>
            </UiFormField>

            <UiFormField label="Contained-Button">
                <button class="form-button button-contained"><span>+</span>Contained Button</button>
            </UiFormField>

            <UiFormField label="Text-Button">
                <button class="form-button"><span>+</span> Text Button</button>
            </UiFormField>

            <UiFormField label="Select">
                <select class="form-select w-full" v-model="single" placeholder="Bitte wählen">
                    <option v-for="option in values" :value="option" :key="option.id">{{ option.label }}</option>
                </select>
            </UiFormField>
        </div>

        <div class="mt-6 flex max-w-4xl flex-col flex-wrap gap-4 rounded border p-4">
            <h2>Button sizes</h2>
            <div class="flex flex-wrap items-center gap-4">
                <button class="form-button button-contained button-xs">Button XS</button>
                <button class="form-button button-contained button-sm">Button SM</button>
                <button class="form-button button-contained">Button Base</button>
                <button class="form-button button-contained button-lg">Button LG</button>
                <button class="form-button button-contained button-xl">Button XL</button>
            </div>

            <h2>Button contained</h2>
            <div class="flex flex-wrap items-center gap-4">
                <button class="form-button button-contained button-xs">Button XS</button>
                <button class="form-button button-contained button-sm">Button SM</button>
                <button class="form-button button-contained">Button Base</button>
                <button class="form-button button-contained button-lg">Button LG</button>
                <button class="form-button button-contained button-xl">Button XL</button>
            </div>

            <h2>Button contained (disabled)</h2>
            <div class="flex flex-wrap items-center gap-4">
                <button class="form-button button-contained button-xs" disabled>Button XS</button>
                <button class="form-button button-contained button-sm" disabled>Button SM</button>
                <button class="form-button button-contained" disabled>Button Base</button>
                <button class="form-button button-contained button-lg" disabled>Button LG</button>
                <button class="form-button button-contained button-xl" disabled>Button XL</button>
            </div>

            <h2>Button secondary</h2>
            <div class="flex flex-wrap items-center gap-4">
                <button class="form-button button-secondary button-xs">Button XS</button>
                <button class="form-button button-secondary button-sm">Button SM</button>
                <button class="form-button button-secondary">Button Base</button>
                <button class="form-button button-secondary button-lg">Button LG</button>
                <button class="form-button button-secondary button-xl">Button XL</button>
            </div>

            <h2>Button secondary (disabled)</h2>
            <div class="flex flex-wrap items-center gap-4">
                <button class="form-button button-secondary button-xs" disabled>Button XS</button>
                <button class="form-button button-secondary button-sm" disabled>Button SM</button>
                <button class="form-button button-secondary" disabled>Button Base</button>
                <button class="form-button button-secondary button-lg" disabled>Button LG</button>
                <button class="form-button button-secondary button-xl" disabled>Button XL</button>
            </div>

            <h2>Button contained-secondary</h2>
            <div class="flex flex-wrap items-center gap-4">
                <button class="form-button button-contained-secondary button-xs">Button XS</button>
                <button class="form-button button-contained-secondary button-sm">Button SM</button>
                <button class="form-button button-contained-secondary">Button Base</button>
                <button class="form-button button-contained-secondary button-lg">Button LG</button>
                <button class="form-button button-contained-secondary button-xl">Button XL</button>
            </div>

            <h2>Button contained-secondary (disabled)</h2>
            <div class="flex flex-wrap items-center gap-4">
                <button class="form-button button-contained-secondary button-xs" disabled>Button XS</button>
                <button class="form-button button-contained-secondary button-sm" disabled>Button SM</button>
                <button class="form-button button-contained-secondary" disabled>Button Base</button>
                <button class="form-button button-contained-secondary button-lg" disabled>Button LG</button>
                <button class="form-button button-contained-secondary button-xl" disabled>Button XL</button>
            </div>

            <h2>Button outline</h2>
            <div class="flex flex-wrap items-center gap-4">
                <button class="form-button button-outline button-xs">Button XS</button>
                <button class="form-button button-outline button-sm">Button SM</button>
                <button class="form-button button-outline">Button Base</button>
                <button class="form-button button-outline button-lg">Button LG</button>
                <button class="form-button button-outline button-xl">Button XL</button>
            </div>

            <h2>Button outline (disabled)</h2>
            <div class="flex flex-wrap items-center gap-4">
                <button class="form-button button-outline button-xs" disabled>Button XS</button>
                <button class="form-button button-outline button-sm" disabled>Button SM</button>
                <button class="form-button button-outline" disabled>Button Base</button>
                <button class="form-button button-outline button-lg" disabled>Button LG</button>
                <button class="form-button button-outline button-xl" disabled>Button XL</button>
            </div>
        </div>

        <div class="mt-4 grid w-full max-w-4xl grid-cols-2 gap-4 rounded border p-4">
            <div class="font-light">Font-weight 300</div>
            <div class="font-light italic">Font-weight 300</div>
            <div class="font-normal">Font-weight 400</div>
            <div class="font-normal italic">Font-weight 400</div>
            <div class="font-medium">Font-weight 500</div>
            <div class="font-medium italic">Font-weight 500</div>
            <div class="font-semibold">Font-weight 600</div>
            <div class="font-semibold italic">Font-weight 600</div>
            <div class="font-bold">Font-weight 700</div>
            <div class="font-bold italic">Font-weight 700</div>
            <div class="font-extrabold">Font-weight 800</div>
            <div class="font-extrabold italic">Font-weight 800</div>
        </div>
    </div>
</template>

<script setup lang="ts">
    definePageMeta({
        title: 'Forms',
        middleware: ['requires-auth', 'redirects']
    })

    type Interval = { start: Date; end: Date }

    const simpleDate1 = ref<Date>(null)

    const simpleDate2 = ref<Date>(null)

    const simpleDateTime1 = ref<Date>(null)

    const simpleDateTime2 = ref<Date>(null)

    const startDate = ref<Date>(null)
    const endDate = ref<Date>(null)

    const startDate2 = ref<Date>(null)
    const endDate2 = ref<Date>(null)

    const checkbox = ref<boolean>(false)

    const single = ref<Option>({ id: 1, label: 'Apples' })

    const multiple = ref<Option[]>([{ id: 2, label: 'Apples' }])

    const interval = ref<Interval>(null)

    const lorem = ref('lorem ipsum dolor sit amet')

    const number = ref(0)

    const values = [
        {
            id: 1,
            label: 'Apples'
        },
        {
            id: 2,
            label: 'Porsche'
        },
        {
            id: 3,
            label: 'Tennis'
        }
    ]
</script>
