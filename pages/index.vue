<template>
    <div class="flex justify-center">
        <div class="mt-16 grid w-full max-w-xl grid-cols-2 gap-4">
            <div
                v-for="link in links"
                :key="link.title"
                @click="goto(link.route)"
                class="hover:bg-grey-50 flex cursor-pointer items-center rounded-lg border p-4 transition duration-150 ease-in-out focus:outline-none focus-visible:ring focus-visible:ring-orange-500 focus-visible:ring-opacity-50">
                <div class="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-md bg-red-100 p-2">
                    <UiIcon :name="link.icon as UiIconName" class="h-auto w-full text-red-500" />
                </div>
                <div class="ml-4">
                    <p class="text-grey-900 block text-left text-sm font-medium">
                        {{ link.title }}
                    </p>
                    <p v-if="link.description" class="text-grey-500 text-sm">
                        {{ link.description }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
    import { UiIconName } from '~~/modules/ui'

    definePageMeta({
        middleware: ['requires-auth', 'redirects'],
        title: 'Überblick'
    })

    const links = useSolutionLinks()

    const router = useRouter()

    function goto(route: any) {
        if (route.href) {
            window.open(route.href, '_blank')
        } else {
            router.push(route)
        }
    }
</script>
