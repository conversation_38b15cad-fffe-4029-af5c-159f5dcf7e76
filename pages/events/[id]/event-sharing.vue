<script setup lang="ts">
    import { PropType } from 'vue'

    definePageMeta({
        pageTransition: false
    })

    const props = defineProps({
        event: Object as PropType<ApiModel<'Event'>>
    })

    /**
     * Don't allow navigation to event sharing tab when needed
     */
    const { canShareEvent } = useEventPermissions(props.event)

</script>
<template>
    <template v-if="canShareEvent">
        <LazyEventSharing :event="event" />
    </template>
    <div v-else>Nicht verfügbar</div>
</template>
