<script setup lang="ts">
    import type { EventPermissionType } from 'composables/event-permissions'

    import { camelCase } from 'lodash-es'

    const { event } = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const { title, description } = useEventDescription(event)

    const { hasPermission = () => false, ...abilities } = inject(EventPermissionsKey) || {}

    const permissions: Record<string, Array<EventPermissionType>> = {
        'General': ['print', 'create-report', 'export-to-calendar', 'duplicate'],
        'Event Data': ['read-data', 'manage-data', 'read-documents', 'manage-documents'],
        'Signing Up': ['modify-signing-up'],
        'Planning': ['see-planning', 'read-event-posts', 'manage-event-posts', 'read-resources', 'manage-resources'],
        'Lifecycle': ['publish', 'cancel', 'reactivate', 'close', 'reopen', 'delete'],
        'Registrations': ['read-registrators', 'read-registrations', 'manage-registrations', 'manage-registrators']
    }
</script>
<template>
    <div>
        <h3 class="mb-4 flex gap-x-2 text-xl">
            <span class="underline" v-text="title" />
            <span v-text="description" />
            <span class="flex items-center rounded-md border p-1 text-xs" v-text="event.status" />
        </h3>

        <div class="mb-4 bg-yellow-50 p-4">
            <h4 class="mb-2 text-sm font-semibold text-yellow-800">My permissions on this event:</h4>
            <pre class="text-xs text-yellow-900" v-text="event.detailedPermission"></pre>
        </div>

        <p class="mb-4">If I'm the user; then the user...</p>

        <table class="mb-4 w-full table-auto border-collapse border-slate-400 text-left" v-for="(values, label) in permissions" :key="label">
            <thead>
                <tr>
                    <th colspan="3">{{ label }}</th>
                </tr>
            </thead>
            <tbody>
                <tr class="border-b" v-for="permission in values" :key="permission">
                    <td class="w-4/6 px-4 py-2 text-sm text-slate-500">
                        <span class="border-b border-red-200">hasPermission</span>(<code class="text-xs">'{{ permission }}'</code>) and
                        <code class="text-xs">{{ camelCase(`can ${permission}`) }}</code>
                    </td>
                    <td class="w-1/6 text-center text-xs font-bold uppercase">
                        {{ hasPermission(permission) ?? '?' }}
                    </td>
                    <td class="w-1/6 text-center text-xs font-bold uppercase">
                        {{ abilities[camelCase(`can ${permission}`)] ?? '?' }}
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</template>
