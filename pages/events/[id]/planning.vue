<template>
    <div class="w-full" :class="{ 'flex flex-col gap-8 xl:gap-0 xl:flex-row justify-evenly' : $route.meta.focusMode}">
        <div :class="{'border border-gray-100 p-2 sm:p-5 basis-[58%]' : $route.meta.focusMode}">
            <div ref="dropzoneRef" class="dropzone">
                <p class="max-w-sm text-center">
                    Ziehe die Ressource hier in dieses Feld. Der drkserver erstellt dann automatisch eine besetzte Planstelle. Sie steht unten in deiner Liste. Du kannst sie anschließend umbenennen.
                </p>
            </div>
            <div class="hideWhenDrag">
                <div class="mb-4 flex flex-wrap sm:flex-[unset] items-center sm:justify-between gap-y-2 sm:gap-y-0 gap-x-2">
                    <button v-if="$route.meta.focusMode && canManageResources" class="form-button button-contained" @click="closeManageResources">Zurück zum Ereignis</button>
                    <input
                        class="search-input" :class="{'mr-auto' : !$route.meta.focusMode}"
                        v-model="eventPostFilter.descriptionContains"
                        :disabled="!eventPosts.length"
                        placeholder="Planung durchsuchen" />
                    <button class="form-button button-contained button-sm" v-if="!$route.meta.focusMode && canManageEventPosts" @click="createEventPosts">
                        Planstellen hinzufügen
                    </button>
                    <button class="form-button button-contained button-sm" v-if="!$route.meta.focusMode && canManageResources" @click="openManageResources">
                        Ressourcen hinzufügen
                    </button>
                </div>
                <div class="mb-8 flex flex-wrap items-center gap-x-2" v-if="eventPosts.length !== 0">
                    <template v-for="{ name, count, set, active } in typeFacets">
                        <button class="chip-button text-xs" @click="set" :aria-active="active">{{ name }} ({{ count }})</button>
                    </template>
                    <UiSwitch v-model="eventPostFilter.isIncomplete" label="Nur unvollständig besetzte" class="text-xs" />
                    <UiMenu button-size="sm" class="ml-0 md:ml-auto">
                        <template #button>
                            <UiIcon :name="eventPostSorter.icon" class="h-auto w-3" />
                            {{ eventPostSorter.label }}
                        </template>
                        <UiMenuItem v-for="{ key, label, set } in eventPostSorter.alternatives" :key="key" @click="set">
                            {{ label }}
                        </UiMenuItem>
                    </UiMenu>
                </div>
            </div>
            <UiWarning
                v-if="eventPosts.length == 0 && !canManageEventPosts"
                title="Platz für Planstellen"
                message="Wer ein Ereignis plant, kann hier Planstellen ergänzen. Planstellen sind Platzhalter für Ressourcen wie dich oder Teilnehmende deiner Gliederung. Es kann sein, dass es für dieses Ereignis keine Planstellen gibt oder sie noch nicht freigeschaltet sind. Du kannst hier also nichts weiter machen."
                class="w-full" />
            <UiWarning
                v-else-if="eventPosts.length == 0"
                title="Platz für Planstellen"
                message="Du legst Planstellen an, indem du auf „Planstellen hinzufügen“ klickst."
                class="w-full" />
            <UiScrollarea class="xl:overflow-auto scroll-shadows h-fit xl:h-[65vh] hidden xl:flex">
                <TransitionGroup
                    enter-active-class="transition"
                    enter-from-class="translate-y-1 opacity-0"
                    enter-to-class="translate-y-0 opacity-100"
                    name="list" tag="div" class="flex w-full pr-3 flex-col gap-3 gap-3">
                        <div v-for="eventPost in sortedEventPosts" :key="eventPost._uuid">
                            <EventPlanningEventPostItem :event-post="eventPost" />
                        </div>
                </TransitionGroup>
            </UiScrollarea>
                <TransitionGroup
                    enter-active-class="transition"
                    enter-from-class="translate-y-1 opacity-0"
                    enter-to-class="translate-y-0 opacity-100"
                    name="list" tag="div" class="xl:hidden relative flex flex-col gap-3 sm:pr-3">
                        <div v-for="eventPost in sortedEventPosts" :key="eventPost._uuid">
                            <EventPlanningEventPostItem :event-post="eventPost"/>
                        </div>
                </TransitionGroup>
        </div>
        <div class="basis-[40%]" :class="{ 'hidden' : !$route.meta.focusMode }">
            <EventPlanningManageResourcesComposer :controller="manageResourcesDialog" />

            <EventPlanningCreateEventPostMultipleDialog :controller="dialog.createMultipleEventPosts" />

            <EventPlanningEditEventPostDialog :controller="dialog.editEventPost" />

            <EventPlanningEditResourceSettingDialog :controller="dialog.editResourceSetting" />

            <EventPlanningSelectSigningUpDialog :controller="dialog.selectSigningUp" />

            <UiConfirmDialog title="Planstelle löschen" :controller="dialog.removeEventPost" confirm-with="Löschen" cancel-with="Abbrechen">
                Du bist dabei, diese Planstelle zu entfernen. Das kannst du nicht rückgängig machen.
            </UiConfirmDialog>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { omit } from 'lodash-es'
    import { Interval as LuxonInterval } from 'luxon'
    import type { manageResourcesDialogType } from '~/composables/event-resources-dialog';

    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const { canManageEventPosts, canManageResources, } = inject(EventPermissionsKey)

    const { push: notify } = useNotifications()

    const runWithNotification = useRunWithNotification()

    const eventPlanning = useEventPlanning(toRef(props, 'event'))
    provide(EventPlanningKey, eventPlanning)
    const { commit, eventPosts, resourceSettingsByEventPostId } = eventPlanning

    const { visibleEventPosts, eventPostFilter, typeFacets } = useEventPostFilter(eventPosts)

    const { sortedEventPosts, eventPostSorter } = useEventPostSorter(visibleEventPosts)

    const { $resourceName } = useNuxtApp()
    /**
     * Setup of controllers
     */
    const dialog = {
        createMultipleEventPosts: useDialogController('createMultipleEventPosts'),
        editEventPost: useDialogController('editEventPost'),
        removeEventPost: useDialogController('confirmation'),
        selectSigningUp: useDialogController('selectSigningUp'),
        editResourceSetting: useDialogController('editResourceSetting'),
        viewEventPostItem: useDialogController('viewEventPostItem')
    }

    const { manageResourcesDialog, openManageResources, closeManageResources } = inject<manageResourcesDialogType>('resourcesDialog')

    const dropzoneRef = useDropzone({
        async onDrop(signingUp) {

            const eventPostProperties = useEventPostProperties(signingUp.resource, props.event.dateFrom, props.event.dateUpTo)

            const eventPost = await runWithNotification(
                () =>
                    commit(
                        'createEventPost',
                        useModelFactory('EventPost').create({
                            ...eventPostProperties
                        })
                    ),
                {
                    pending: `Es wird eine neue Planstelle für ${name} angelegt.`,
                    error: `Du wolltest eine neue Planstelle für ${name} amlegen. Das hat nicht geklappt.`
                }
            )

            if (eventPost instanceof Error) {
                return
            }

            createResourceSetting(eventPost, signingUp)
        }
    })

    async function createEventPosts() {
        const { data: eventPosts, isCanceled } = await dialog.createMultipleEventPosts.reveal()

        if (!isCanceled) {
            runWithNotification(
                () => {
                    return Promise.all(
                        // There is a note here: https://tanstack.com/query/v4/docs/vue/guides/mutations#consecutive-mutations
                        // But this works!
                        eventPosts.map((eventPost) => commit('createEventPost', eventPost))
                    )
                },
                {
                    pending: eventPosts.length > 1 ? `Deine Planstellen werden gerade erstellt.` : `Deine Planstelle wird gerade erstellt.`,
                    success: eventPosts.length > 1 ? `Das hat geklappt: Du hast ${eventPosts.length} Planstellen hinzugefügt.` : null,
                    error:
                        eventPosts.length > 1
                            ? `Du wolltest mehrere Planstellen hinzufügen. Das hat nicht geklappt.`
                            : `Du wolltest eine Planstelle hinzufügen. Das hat nicht geklappt.`
                }
            )
        }
    }

    /**
     * Remove event post
     *
     * Used in:
     *
     * – 3-dot menu in event-post-item.vue
     *
     */
    async function removeEventPost(eventPost: ApiModel<'EventPost'>) {
        const { isCanceled } = await dialog.removeEventPost.reveal()
        if (!isCanceled) {
            runWithNotification(
                () => {
                    return commit('removeEventPost', eventPost)
                },
                {
                    pending: `Die Planstelle "${eventPost.description} wird entfernt.`,
                    error: `Du wolltest die Planstelle "${eventPost.description}" löschen. Das hat nicht geklappt.`
                }
            )
        }
    }

    async function duplicateEventPost(eventPost: ApiModel<'EventPost'>) {
        runWithNotification(
            () => {
                const duplicate = useModelFactory('EventPost').create(
                    // Build a new event post
                    omit(eventPost, '_uuid', 'id')
                )
                return commit('createEventPost', duplicate)
            },
            {
                pending: `Die Planstelle "${eventPost.description} wird gerade verdoppelt.`,
                error: `Du wolltest die Planstelle "${eventPost.description}" verdoppeln. Das hat nicht geklappt.`,
                success: `Das hat geklappt: Du hast die Planstelle "${eventPost.description}" verdoppelt.`
            }
        )
    }

    async function editEventPost(eventPost: ApiModel<'EventPost'>) {
        const { data: updatedEventPost, isCanceled } = await dialog.editEventPost.reveal(eventPost)

        if (!isCanceled) {
            runWithNotification(
                () => {
                    return commit('updateEventPost', updatedEventPost)
                },
                {
                    pending: `Die Planstelle "${eventPost.description} wird gespeichert.`,
                    error: `Du wolltest die Planstelle "${eventPost.description}" bearbeiten. Das hat nicht geklappt.`
                    // success: `Das hat geklappt: Du hast die Planstelle "${eventPost.description}" verdoppelt.`
                }
            )
        }
    }

    async function editResourceSetting(resourceSetting: ApiModel<'ResourceSetting'>) {
        const { isCanceled, data } = await dialog.editResourceSetting.reveal(resourceSetting)

        if (!isCanceled) {
            const name = $resourceName(resourceSetting.eventSigningUp.resource)

            runWithNotification(
                () => {
                    return commit(
                        'updateResourceSetting',
                        useModelFactory('ResourceSetting').create({
                            ...resourceSetting,
                            periods: data as ApiModel<'Period'>[]
                        })
                    )
                },
                {
                    pending: `Die veränderte Planstellenzuordnung wird gespeichert.`,
                    error: `Du wolltest ändern, von wann bis wann ${name} am Ereignis teilnimmt. Das hat nicht geklappt.`
                    // success: `Das hat geklappt: Du hast den Zeitraum geändert, an dem ${name.value} am Ereignis teilnimmt.`
                }
            )
        }
    }

    async function removeResourceSetting(resourceSetting: ApiModel<'ResourceSetting'>) {
        const name = $resourceName(resourceSetting.eventSigningUp.resource)

        runWithNotification(
            () => {
                return commit('removeResourceSetting', resourceSetting)
            },
            {
                pending: `Die Planstelle ${resourceSetting.eventPost.description} wird aktualisiert.`,
                error: `Du wolltest ${name} von der Planstelle ${resourceSetting.eventPost.description} entfernen. Das hat nicht geklappt.`
                // success: `Erfolgreiche Entfernung von der Planstelle ${resourceSetting.eventPost.description}`
            }
        )
    }

    /**
     * Remove SigningUp From Resource Settings
     *
     * a.k.a "Von allen Planstellen entfernen"
     *
     * Be careful: It will not work with "eventSigningUp" models because they have no attribute `assignedResourceSettings`
     */
    async function removeFromResourceSettings(signingUp: ApiModel<'SigningUp'>) {
        const resourceSettings = signingUp.assignedResourceSettings || []
        const name = $resourceName(signingUp.resource)

        runWithNotification(
            () => {
                return Promise.all(
                    // There is a note here: https://tanstack.com/query/v4/docs/vue/guides/mutations#consecutive-mutations
                    // But this works!
                    resourceSettings.map((resourceSetting) => commit('removeResourceSetting', resourceSetting))
                )
            },
            {
                pending: `${name} wird von allen Planstellen entfernt.`,
                error: `Du wolltest ${name} von allen Planstellen entfernen. Das hat nicht geklappt.`,
                success: `Das hat geklappt: Du hast ${name} von allen Planstellen dieses Ereignisses entfernt.`
            }
        )
    }

    async function createResourceSetting(eventPost: ApiModel<'EventPost'>, signingUp: ApiModel<'SigningUp'>) {
        const existingResourceSettings = resourceSettingsByEventPostId(eventPost.id)

        const { $dependentTypes: data } = useNuxtApp()
        const updatedEventPost = ref<ApiModel<'EventPost'>>(null)

        if (signingUp.resource.type === 'TECHNIC') {

            // If event post do not have yet assing modul value then fill it with according resource data
            if(!eventPost.technicArticleTypes[0]) {

                // check if resource is external
                const resourceType = signingUp.resource.external ? signingUp.resource.externalArticle.type : signingUp.resource.article.type;
                updatedEventPost.value = useModelFactory('EventPost').create({...eventPost, technicArticleTypes: [data.value.filter(type => type.name === resourceType.name)[0].id]})
                runWithNotification(
                   () => {
                       return commit('updateEventPost', updatedEventPost.value)
                   },
                   {
                       pending: `Die Planstelle "${eventPost.description} wird gespeichert.`,
                       error: `Du wolltest die Planstelle "${eventPost.description}" bearbeiten. Das hat nicht geklappt.`
                       // success: `Das hat geklappt: Du hast die Planstelle "${eventPost.description}" verdoppelt.`
                   }
               )
            }

            runWithNotification(
                () => {
                    return Promise.all(
                        // Remove all resource settings from this event post
                        existingResourceSettings.value.map((resourceSetting) => {
                            return commit('removeResourceSetting', resourceSetting)
                        })
                    )
                },
                {
                    pending: `Die aktuelle Besetzung wird von der Planstelle ${eventPost.description} entfernt.`,
                    error: `Du wolltest alle alten Besetzungen von der Planstelle ${eventPost.description} entfernen. Das hat nicht geklappt.`
                }
            )
        }

        const occupiedPeriods = existingResourceSettings.value.reduce(
            (collector, { periods }) => {
                periods.forEach(({ start, end }) => {
                    collector.push(LuxonInterval.fromDateTimes(start, end))
                })
                return collector
            },
            // The collection is empty at first
            [] as LuxonInterval[]
        )

        const eventPostInterval = LuxonInterval.fromDateTimes(eventPost.dateFrom, eventPost.dateUpTo)

        const freePeriods = eventPostInterval.difference(...occupiedPeriods).map(({ start, end }) => {
            return useModelFactory('Period').create({
                start: start.toJSDate(),
                end: end.toJSDate()
            })
        })

        if (freePeriods.length < 1) {
            notify({
                type: 'error',
                content: `Auf jede Planstelle kannst du genau eine Ressource setzen. Ausnahme: Die beiden Ressourcen überschneiden sich zeitlich nicht.`
            })

            return
        }

        runWithNotification(
            () => {
                return commit(
                    'createResourceSetting',
                    useModelFactory('ResourceSetting').create({
                        eventPost: eventPost,
                        eventSigningUp: signingUp,
                        periods: freePeriods
                    })
                )
            },
            {
                pending: `Die aktuelle Besetzung wird auf der Planstelle ${eventPost.description} gespeichert.`,
                error: `Du wolltest die Planstelle ${eventPost.description} besetzen. Das hat nicht geklappt.`
            }
        )
    }

    async function selectSigningUp(eventPost: ApiModel<'EventPost'>) {
        const { data: signingUp, isCanceled } = await dialog.selectSigningUp.reveal(eventPost)

        if (!isCanceled) {
            createResourceSetting(eventPost, signingUp)
        }
    }

    provide(EventPlanningActions, {
        createEventPosts,
        editEventPost,
        removeEventPost,
        duplicateEventPost,

        createResourceSetting,
        editResourceSetting,
        removeResourceSetting,

        removeFromResourceSettings,
        selectSigningUp
    })
</script>

<style lang="pcss" scoped>

    .hide-y-scroll::-webkit-scrollbar{
        display: none;
    }

    .search-input {
        @apply form-input py-[.25rem] text-sm leading-6 basis-full sm:basis-[unset];
        @apply pl-[1.5rem] bg-no-repeat icon-search-sky-500 bg-[length:1rem_1rem] bg-[.25rem_center]
    }
    .chip-button {
        @apply text-grey-700 bg-grey-100 hover:bg-gray-200 inline-flex cursor-default items-center whitespace-nowrap rounded-full py-[.5em] px-[1em];
        @apply cursor-pointer select-none;
        @apply aria-[active=true]:bg-grey-500  aria-[active=true]:hover:bg-grey-600 aria-[active=true]:text-white
    }

    .list-move,  .list-enter-active, .list-leave-active {
        transition: all 0.3s ease;
    }

    .list-enter-from, .list-leave-to {
        opacity: 0;
        transform: translateX(16px);
    }

    /* ensure leaving items are taken out of layout flow so that moving
        animations can be calculated correctly. */
    .list-leave-active {
        position: absolute;
        display: none;
    }

    .dropzone {
        @apply mb-4 flex h-[122px] items-center justify-center border-2 border-dashed border-red-100 p-5 transition-colors;

        &[data-ui-dropzone-state~=idle] {
            @apply hidden
        }

        &[data-ui-dropzone-state~=disabled] {
            @apply opacity-40
        }

        &[data-ui-dropzone-state~=active] {
            @apply border-red-300
        }
    }
    /** This is important because otherwise "ondragenter" does not work sufficient */
    :where([data-ui-dropzone-state~=enabled]) > * {
        pointer-events: none;
    }

    :where([data-ui-dropzone-state~=enabled]) + .hideWhenDrag {
       display: none;
    }
</style>
