<template>
    <template v-if="canReadRegistrations || canReadRegistrators">
        <EventRegistratorPanel v-if="canReadRegistrators" :event="event" class="mb-12 border-b pb-8" />
        <EventRegistrationPanel v-if="canReadRegistrations" :event="event" />
    </template>
    <div v-else>Nicht verfügbar</div>
</template>

<script setup lang="ts">
    defineProps<{
        event: ApiModel<'Event'>
    }>()

    /**
     * Don't allow navigation to registrations tab when needed
     */
    const { canReadRegistrations, canReadRegistrators } = inject(EventPermissionsKey)

</script>
