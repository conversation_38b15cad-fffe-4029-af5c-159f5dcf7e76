<script setup lang="ts">

    definePageMeta({
        middleware: ['requires-auth', 'redirects']
    })

    const route = useRoute()

    /**
     * Fetch event model by id parameter
     */
    const eventId = parseInt(route.params.id as string)
    const eventQueries = useEventQueries(eventId)
    provideEventQueries(eventId, eventQueries)

    const { data: event, isSuccess, isLoading } = eventQueries.details()

    /**
     * Provide event permissions to all children
     */
    const eventUser = useEventPermissions(event)
    provide(EventPermissionsKey, eventUser)

    /**
     * The available tabs – each has a guard (show tab when guard == true)
     */
    const tabs = [
        {
            title: 'Ereignis-Infos',
            guard: eventUser.canReadData,
            route: {
                name: 'events-id-details'
            }
        },
        {
            title: 'Planstellen',
            guard: eventUser.canSeePlanning,
            route: {
                name: 'events-id-planning'
            }
        },
        {
            title: '<PERSON>ile<PERSON>',
            guard: eventUser.canShareEvent,
            route: {
                name: 'events-id-event-sharing'
            }
        },
        {
            title: 'Registrieren',
            guard: eventUser.canReadRegistrations,
            route: {
                name: 'events-id-registrations'
            }
        },
        // {
        //     title: 'Permissions',
        //     guard: eventUser.canReadData,
        //     route: {
        //         name: 'events-id-permissions'
        //     }
        // }
    ]

    const { dialog: memberDataDialog, reveal: openMemberDataDialog } = useMemberDataDialog()

    provide('viewMemberData', {
        openMemberDataDialog
    })

    const { manageResourcesDialog, openManageResources, closeManageResources, goToManageResources } = useResourceManageDialog()

    provide('resourcesDialog', {
        manageResourcesDialog,
        openManageResources,
        closeManageResources,
        goToManageResources
    })
</script>

<template>
    <div v-if="isSuccess" class="relative flex flex-1 w-full">
        <ProvideEventActions :event="event">
            <div
                :data-focus="$route.meta.focusMode"
                class="flex flex-col flex-1 w-full transition-all gap-x-8 gap-y-8 xl:flex-row xl:gap-y-0">
                <div class="flex flex-col flex-none gap-y-2 xl:w-1/4 xl:px-2" :class="{ 'hidden': $route.meta.focusMode }">
                    <div>
                        <NuxtLink to="/events" class="inline-flex form-button button-sm">
                            <IconChevronDoubleLeft class="flex-none h-5 align-bottom" />
                            Zurück zur Ereignis-Liste
                        </NuxtLink>
                    </div>
                    <div class="flex flex-col flex-none p-4 border border-1 border-grey-100 xl:border-grey-50 gap-y-2 sm:mb-2 xl:mb-0 xl:shadow-md">
                        <EventSummary :event="event" />
                    </div>
                </div>
                <div class="flex flex-col flex-1 px-0">
                    <nav
                        :data-focus="$route.meta.focusMode"
                        class="flex flex-wrap justify-around md:gap-0 md:justify-center flex-none close gap-y-2 xl:flex-nowrap xl:justify-start">
                        <template v-for="tab in tabs" :key="tab.route.name">
                            <NuxtLink
                                v-if="tab.guard.value"
                                :to="tab.route"
                                role="button"
                                active-class="!font-medium !border-b-2 !border-b-sky-500"
                                class="!mr-1 w-1/3 whitespace-nowrap border-b border-b-sky-200 py-2 text-center text-sm font-normal text-sky-500 hover:border-b-sky-500 sm:w-1/5 sm:basis-[unset] xl:flex-1 xl:px-4">
                                {{ tab.title }}
                            </NuxtLink>
                        </template>
                    </nav>
                    <div :class="{ 'border-grey-100 mt-6 flex-1 border bg-opacity-5 px-2 py-8 sm:px-4': !$route.meta.focusMode }">
                        <div class="relative">
                            <NuxtPage :event="event" />
                        </div>
                    </div>
                </div>
            </div>
        </ProvideEventActions>
        <EventPlanningMemberMasterdataDialog :controller="memberDataDialog.viewMemberData" :event="event" />
    </div>
    <div v-else-if="isLoading" class="relative flex flex-1">
        <UiLoader :is-loading="true" />
    </div>
    <div v-else class="relative flex flex-1">
        <div class="flex flex-col items-center flex-grow p-5 text-center text-softred-900 bg-softred-50 h-fit">
            <h3 class="text-lg">Ladehemmung</h3>
            <p>Beim Anzeigen der Ereignisse läuft gerade etwas schief. Bitte lade die Seite neu oder melde dich einmal ab und wieder an.</p>
        </div>
    </div>
</template>

<style lang="pcss" scoped>
    .open {
         @apply data-[focus=true]:block;
     }

    .close {
         @apply data-[focus=true]:hidden;
    }

     .width {
         @apply data-[focus=true]:w-full;
    }

    ::-webkit-scrollbar {
        width: 6px;
        flex-grow: 0;
        margin: 0 4px 51px 6px;
        mix-blend-mode: multiply;
        background: #9a9a97;
    }

    ::-webkit-scrollbar-track {
        background-color: white;
    }

    ::-webkit-scrollbar-thumb {
        background: #9a9a97;
        border-radius: 4px;
    }

    .summary {
        max-height: calc(100vh - 10rem);
    }
</style>
