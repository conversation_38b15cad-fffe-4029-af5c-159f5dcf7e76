<script lang="ts" setup></script>

<template>
    <div class="bg-grey-50 flex min-h-screen flex-col transition-all">
        <LayoutHeader class="" />
        <main class="relative mx-auto flex w-full max-w-7xl flex-1 flex-col lg:overflow-hidden bg-white pt-6 pb-4 px-2 sm:px-6 lg:px-8">
            <NuxtPage class="transition-all" />
        </main>

        <ClientOnly>
            <Notifications />
            <Composers />
        </ClientOnly>
    </div>
</template>

<style lang="pcss">
    .page-enter-active,
    .page-leave-active {
      @apply transform transition-all duration-150 ease-in-out;
    }

    /* .page-enter-active {
      @apply z-10
    }

    .page-leave-active {
      @apply z-0
    } */

    .page-enter-from,
    .page-leave-to {
      @apply top-0 left-0 w-full absolute scale-75 opacity-0;
      /* @apply translate-x-full; */
    }


    /**
     * Slide components in and out
     */
    .slide-enter-active,
    .slide-leave-active {
        @apply transform transition ease-in-out duration-500 sm:duration-700;
    }

    .slide-enter-to {
        @apply translate-x-0;
    }


    .slide-enter-from {
       @apply translate-x-full
    }


    .slide-leave-to {
        @apply translate-x-full;
    }


    .slide-leave-from {
        @apply translate-x-0;
    }
</style>
