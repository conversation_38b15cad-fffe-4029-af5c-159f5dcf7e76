@import 'overlayscrollbars/overlayscrollbars.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
    /* Action button: used in Event-Post-Popover */
    .action-button {
        @apply flex cursor-pointer items-center gap-2 p-[0.5em] text-left leading-tight transition-colors backdrop:text-left hover:bg-blue-50 active:bg-blue-100;
    }

    .action-button-icon {
        @apply h-[1.25em] w-auto text-sky-500;
    }
}

/* On mobile, especially iOS inputs can be very small so fix this min height*/
@media screen and (max-width: 767px) {
    input, select, textarea {
        font-size: 16px !important;
    }
    .form-input {
        min-height: 40px !important;
    }
}



@font-face {
    font-family: 'Open Sans';
    src: url('/fonts/OpenSans-VariableFont-300-800.woff2') format('woff2');
    font-weight: 300 800;
    font-display: swap;
    font-style: normal;
}

@font-face {
    font-family: 'Open Sans';
    src: url('/fonts/OpenSans-Italic-VariableFont-300-800.woff2') format('woff2');
    font-weight: 300 800;
    font-display: swap;
    font-style: italic;
}
