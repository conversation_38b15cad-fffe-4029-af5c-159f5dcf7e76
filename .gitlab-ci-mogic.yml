stages:
  - environment
  - preparation
  - docker
  - deploy
  - sbom

# Variables
variables:
  PRODUCTIONBRANCH: main
  STAGINGBRANCH: stage
  STABLEBRANCH: stable
  TESTINGBRANCH: develop

.add_deploy_key: &add_deploy_key
  - "which vault || wget --no-verbose https://releases.hashicorp.com/vault/1.1.0/vault_1.1.0_linux_amd64.zip && unzip vault_1.1.0_linux_amd64.zip && mv vault /usr/local/bin/"
  - "which ssh-agent || ( apt-get update -y && apt-get install openssh-client git -y )"
  - eval $(ssh-agent -s)
  - DEPLOY_KEY=$(vault kv get -field=ssh-key-private devops/SSH/Mogic-General-Refresh)
  - echo "$DEPLOY_KEY" | tr -d '\r' | ssh-add - > /dev/null
  - mkdir -p ~/.ssh
  - chmod 700 ~/.ssh
  - ssh-keyscan gitlab.mogic.com >> ~/.ssh/known_hosts
  - chmod 644 ~/.ssh/known_hosts
  - if [ "$CI_COMMIT_REF_SLUG" == "$PRODUCTIONBRANCH" ];
    then export ENVIRONMENT='production'; export VAULTPATH_KV2="projects_prod/data/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME"; export VAULTPATH_KV1="projects_prod/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME";
    elif [ "$CI_COMMIT_REF_SLUG" == "$STAGINGBRANCH" ]; then export ENVIRONMENT='staging'; export VAULTPATH_KV2="projects/data/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/staging"; export VAULTPATH_KV1="projects/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/staging";
    elif [ "$CI_COMMIT_REF_SLUG" == "$STABLEBRANCH" ]; then export ENVIRONMENT='stable'; export VAULTPATH_KV2="projects/data/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/stable"; export VAULTPATH_KV1="projects/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/stable";
    else export ENVIRONMENT='testing'; export VAULTPATH_KV2="projects/data/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/testing"; VAULTPATH_KV1="projects/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/testing"; fi

environment:
  stage: environment
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  script:
    - gitlabci-templating -inputTemplate .env.example -vaultPath $VAULTPATH_KV2 -envTemplate ./deployment/templates/env-template -outputFile .env
  artifacts:
    paths:
      - ./.env
    expire_in: 1 days
    when: always
  only:
    - branches

yarn-build:
  stage: preparation
  image: node:20
  script:
    - yarn install --prefer-offline --frozen-lockfile --non-interactive --production=false
    - yarn build
  artifacts:
    paths:
      - ./node_modules
      - ./.output
      - ./.nuxt
    expire_in: 1 days
    when: always
  dependencies:
    - environment
  cache:
    paths:
      - node_modules
  only:
    - branches

docker:
  stage: docker
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  script:
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN gitlab-docker.mogic.com
    - docker build -f deployment/docker/Dockerfile -t gitlab-docker.mogic.com/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:${CI_COMMIT_REF_SLUG} .
    - docker push gitlab-docker.mogic.com/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:${CI_COMMIT_REF_SLUG}
  dependencies:
    - yarn-build
    - environment
  only:
    - branches

deploy:
  stage: deploy
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  script:
    - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
    - gitlabci-templating -inputTemplate deployment/docker/docker-compose-template -outputFile $CI_PROJECT_DIR/docker-compose.yml
    - ssh root@$TARGET_HOST "mkdir -p /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME"
    - rsync -havz --delete -e 'ssh -p 22' $CI_PROJECT_DIR/docker-compose.yml "root@$TARGET_HOST:/opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME"
    - ssh root@$TARGET_HOST "docker login -u gitlab-ci-token -p $CI_JOB_TOKEN gitlab-docker.mogic.com"
    - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose stop drkserver-em-light" || true
    - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose rm -f drkserver-em-light" || true
    - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose pull"
    - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose up -d"
  environment:
    name: $CI_COMMIT_REF_NAME
    url: https://$CI_COMMIT_REF_SLUG.drkserver.mogic-server.de
  dependencies: []
  only:
    - branches

stop_environment:
  stage: deploy
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  variables:
    GIT_STRATEGY: none
  script:
    - TARGET_HOST=$(vault kv get -field=TARGET_HOST $VAULTPATH_KV1)
    - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
    # stop container, remove image, remove folder.
    - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose down --rmi all -v --remove-orphans"
    - ssh root@$TARGET_HOST "cd /opt/ && rm -rf $CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME"
  environment:
    name: $CI_COMMIT_REF_NAME
    action: stop
  when: manual
  only:
    - branches
  except:
    variables:
      - $CI_COMMIT_REF_NAME == $TESTINGBRANCH
      - $CI_COMMIT_REF_NAME == $PRODUCTIONBRANCH

generate-sbom:
  stage: sbom
  image: node:20
  variables:
    REDIS_URL: redis:6379
  before_script: *add_deploy_key
  script:
    - npm install --global @cyclonedx/cyclonedx-npm
    - yarn install --prefer-offline --frozen-lockfile --non-interactive --production=false
    - cyclonedx-npm --ignore-npm-errors --output-format XML --output-file bom.xml
    - DEPTRACK_URL=$(vault kv get -field="deptrack_api_url" devops/Dependencytrack)
    - DEPTRACK_API_KEY=$(vault kv get -field="deptrack_api_key" devops/Dependencytrack)
    - 'curl -X POST $DEPTRACK_URL -H "Content-Type: multipart/form-data" -H "X-API-Key: $DEPTRACK_API_KEY" -F "autoCreate=true" -F "projectName=$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-node" -F "projectVersion=$CI_COMMIT_TAG" -F bom=@bom.xml'
  dependencies: []
  only:
    - tags
