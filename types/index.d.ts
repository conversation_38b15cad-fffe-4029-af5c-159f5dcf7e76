declare module '#app' {
    interface _NuxtApp {
        $isAuthenticated?: boolean
    }

    interface nuxtApp {
        $isAuthenticated?: boolean
    }
}

declare module '@vue/runtime-core' {
    interface ComponentCustomProperties {
        $isAuthenticated: boolean
    }
}

declare global {
    type QueryVariablesType = { query: string; limit: number }
    type Option = {
        id: number
        label: string
        value?: string
        disabled?: boolean
    }

    type ExtendedOption<T> = Option & {
        item: T
    }
}

export { }
