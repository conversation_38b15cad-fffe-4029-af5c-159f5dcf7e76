export default defineNuxtRouteMiddleware(async (to, _from) => {
    /**
     * The registrations tab must be hidden when users are not allowed to see registrations
     *
     * This is a route middleware that only applies when teh route is rendered server side.
     */

    if (to?.name === 'events-id-registrations' && process.server) {
        const event = await $fetch(`/api/events/${to.params?.id}`, {
            headers: useRequestHeaders(['cookie'])
        })

        const { canReadRegistrations } = useEventPermissions(useModelFactory('Event').create(event))

        if (unref(canReadRegistrations) !== true) {
            abortNavigation(`can see ${unref(canReadRegistrations)}`)
        }
    }
})
