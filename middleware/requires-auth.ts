export default defineNuxtRouteMiddleware(async (to, _) => {
    const { $isAuthenticated } = useNuxtApp()

    if (!$isAuthenticated && to.name !== 'login') {
        if (process.client) {
            const {
                public: { baseUrl }
            } = useRuntimeConfig()

            window.location.href = `${baseUrl}/login`
        }

        return navigateTo('/login', {
            replace: false,
            redirectCode: 302
        })
    } else {
        return
    }
})
