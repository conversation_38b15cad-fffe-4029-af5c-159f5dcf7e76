type Response = {
    id: number
    name: string
    unitTypes: {
        id: number
        name: string
        unitCategories: {
            id: number
            name: string
        }[]
    }[]
}[]

export default defineEventHandler(async (event) => {
    const { get } = await useApiProxy(event)

    const params = {
        technicarticletype: ['DOG', 'EQUIPMENT', 'MULTITUDE', 'RADIO', 'SINGLE_DEVICE', 'VEHICLE']
    }

    return get<Response>('technic/dependent-types', params)
})
