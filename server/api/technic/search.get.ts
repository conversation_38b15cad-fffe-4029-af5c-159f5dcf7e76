import { z, zh, useValidatedQuery, useSafeValidatedQuery } from 'h3-zod'

type Response = {
    totalItems: number
    limit: number
    offset: number
    items: any[]
}

export default defineEventHandler(async (event) => {
    const { post } = await useApiProxy(event)

    const queryData = await useValidatedQuery(
        event,
        z.object({
            offset: z.coerce.number().default(0),
            limit: z.coerce.number().default(5),
            searchTerm: z.string().default(''),
            technicArticleTypes: z.array(z.string()).optional(),
            technicArticleUnitTypes: z.array(z.string()).optional(),
            technicArticleUnitCategories: z.array(z.string()).optional(),
            warehouseIds: z.array(z.number()).optional(),
            withAvatar: z.coerce.boolean().default(true)
        })
    )

    return post<Response>(`technic/search`, {
        ...queryData,
        sortBy: [
            {
                field: 'ID',
                directionAsc: true
            }
        ]
    })
})
