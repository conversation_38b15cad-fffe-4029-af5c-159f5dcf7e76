import { ApiSchema } from '~/schema/factory'
/**
 * @see https://gitlab.drkserver.org/drkserver/documentation/api-documentation/-/blob/master/drkserver.api.openapi.final.yaml
 */
export default defineEventHandler(async (event) => {
    const { post } = await useApiProxy(event)

    const eventId = getRouterParam(event, 'id')

    const body = await readBody(event)

    const respond = await post<ApiSchema<'InternalPublicationType'>>(`/events/${eventId}/internal-publication/`, body)

    return respond
})
