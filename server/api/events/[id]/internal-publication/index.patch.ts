import { ApiSchema } from '~/schema/factory'
/**
 * Backend expexts the list of key as a query parameter `internalPublication` and returns nothing
 *
 * We'll return the given list within the response
 *
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/drkserver.api.openapi.final.yaml#/event/putInternalPublicationByEventId
 */
export default defineEventHandler(async (event) => {
    const { patch } = await useApiProxy(event)
    const eventId = getRouterParam(event, 'id')

    const internalPublication = await readBody(event)

    const respond = await patch<ApiSchema<'InternalPublicationType'>>(`/events/${eventId}/internal-publication/${internalPublication.id}`, internalPublication)

    return respond
})
