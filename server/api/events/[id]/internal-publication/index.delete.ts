/**
 * @see https://gitlab.drkserver.org/drkserver/documentation/api-documentation/-/blob/master/drkserver.api.openapi.final.yaml
 */
export default defineEventHandler(async (event) => {
    const { remove } = await useApiProxy(event)

    const eventId = getRouterParam(event, 'id')

    const internalPublication = await readBody(event)

    const respond = await remove(`/events/${eventId}/internal-publication/${internalPublication.id}`, {
        method: 'delete'
    })
    return respond

})
