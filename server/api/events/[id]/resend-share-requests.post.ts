import EventShareRequestStatus from '~~/enums/event-share-request-status'

export default defineEventHandler(async (event) => {
    const { post } = await useApiProxy(event)

    const body = (await readBody(event)) as ApiModel<'EventShareRequest'>[]

    const filteredShareRequests = body.filter((request) => request.status !== EventShareRequestStatus.ACCEPTED)

    return post(`/events/${event.context.params?.id}/share-requests`, filteredShareRequests)
})
