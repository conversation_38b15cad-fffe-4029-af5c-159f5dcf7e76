export default defineEventHandler(async (event) => {
    const { post, get } = await useApiProxy(event)
    const body = (await readBody(event)) as ApiModel<'EventShareRequest'>[]
    const id = event.context.params?.id

    const shareRequests = await get<ApiModel<'EventShareRequest'>[]>(`/events/${id}/share-requests`)

    if (!Array.isArray(shareRequests)) {
        return post(`/events/${id}/share-requests`, body)
    }

    //If shareRequest with given organization ID and targetGemeinschaft exists, we filter it
    const filteredRequests = body.filter(
        (newRequest) =>
            !shareRequests.find(
                (r) => r.targetOrganisation.id === newRequest.targetOrganisation.id && r.targetGemeinschaft === newRequest.targetGemeinschaft
            )
    )

    return await post(`/events/${id}/share-requests`, filteredRequests)
})
