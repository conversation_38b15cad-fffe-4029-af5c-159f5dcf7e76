import { z } from 'zod'
import { EventPostConflict } from '~~/schema/models/event-post'

const EventPostConflicts = z.array(
    z.object({
        /**
         * The event post id
         */
        eventPostId: z.number(),

        /**
         * The list of conflicts; grouped by resource settings
         */
        eventPostConflicts: z.array(EventPostConflict)
    })
)

export default defineEventHandler(async (event) => {
    const { post } = await useApiProxy(event)

    return EventPostConflicts.parse(
        // Fetch conflicts and parse into a known datastructure.
        // We don't have a dedicated model in our frontend.
        await post(`/events/${event.context.params.id}/signing-ups/resource-setting/conflicts`, [])
    )
})
