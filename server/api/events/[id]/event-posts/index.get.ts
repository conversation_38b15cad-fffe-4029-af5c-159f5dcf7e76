import { EventPost } from '~~/schema/models/event-post'

/**
 * Get all event posts for the given event
 */
export default defineEventHandler(async (event) => {
    const api = await useApiProxy(event)
    const eventId = getRouterParam(event, 'id')

    return EventPost.array()
        .parse(
            //..
            await api.get(`/events/${eventId}/event-posts`)
        )
        .filter(({ eventPostResourceType }) => {
            // DEL-254 – Exclude 'GROUP' ans 'ROOM'
            return ['PERSONAL', 'TECHNIC'].includes(eventPostResourceType)
        })
})
