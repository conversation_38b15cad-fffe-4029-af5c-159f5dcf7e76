import { z, useValidatedBody, useSafeValidatedBody } from 'h3-zod'

import { MemberResource, ArticleResource } from '~~/schema/models/resource'

const InputSchema = z.object({
    status: z.union([z.literal('AVAILABLE'), z.literal('NOT_AVAILABLE'), z.literal('REPLY_PENDING')]).default('AVAILABLE'),
    // This operation is currently only allowed on internal resources
    resource: z.union([MemberResource, ArticleResource]),
    periods: z
        .object({
            start: z.string().datetime(),
            end: z.string().datetime()
        })
        .array()
        .optional()
})

export default defineEventHandler(async (event) => {
    // return await useSafeValidatedBody(event, InputSchema)

    const { status, resource, periods } = await useValidatedBody(event, InputSchema)

    const eventId = getRouterParam(event, 'id')

    /**
     * Update signing up for member resources
     */
    if (resource.type === 'PERSONAL') {
        const { put: create } = await useApiProxy(event)

        return create(`events/${eventId}/signing-ups/PERSONAL/${resource.member.id}`, {
            status,
            periods
        })
    }

    /**
     * Update signing up for article resources
     */
    if (resource.type === 'TECHNIC') {
        const { put: create } = await useApiProxy(event)

        return create(`events/${eventId}/signing-ups/TECHNIC/${resource.article.id}`, {
            status,
            periods
        })
    }

    return sendError(event, createError({ message: 'Not implemented' }))
})
