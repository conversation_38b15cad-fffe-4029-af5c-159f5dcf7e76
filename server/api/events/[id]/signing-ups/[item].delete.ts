/**
 * This is a delete request containing a body
 *
 * It's expected that the body contains data for a signing up resource.
 * Depending on the type of the resource we can dispatch the request to the correct backend route.
 *
 * Alternativly we could fetch and await all signing ups from the repository and try to find the corresponding item in that array.
 *
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/drkserver.api.openapi.full.yaml#/event/deleteSigningUpByEventIdResourceTypeAndEntityId
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/drkserver.api.openapi.full.yaml#/event/deleteExternalPersonByEventIdAndExternalPersonId
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/drkserver.api.openapi.full.yaml#/event/deleteExternalTechnicArticleByEventIdAndExternalTechnicArticleId
 */
export default defineEventHandler(async (event) => {
    const { remove: removeSigningUp } = await useApiProxy(event)

    const { id: eventId } = getRouterParams(event)
    const resource: ApiModel<'Resource'> = await readBody(event)

    let path = null

    if (!!resource?.external === true && resource.type === 'PERSONAL') {
        path = `external-person/${resource.externalPerson.id}`
    }

    if (!!resource?.external === true && resource.type === 'TECHNIC') {
        path = `external-technic-article/${resource.externalArticle.id}`
    }

    if (!!resource?.external === false && resource.type === 'PERSONAL') {
        path = `${resource.type}/${resource.member.id}`
    }

    if (!!resource?.external === false && resource.type === 'TECHNIC') {
        path = `${resource.type}/${resource.article.id}`
    }

    return !!path
        ? removeSigningUp(`events/${eventId}/signing-ups/${path}`)
        : sendError(event, createError('Could not generate a valid path to repository'))
})
