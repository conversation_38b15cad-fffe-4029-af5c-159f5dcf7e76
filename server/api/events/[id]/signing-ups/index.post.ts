import { z, useValidatedBody, useSafeValidatedBody } from 'h3-zod'
import { Resource } from '~~/schema/models/resource'

/**
 * Create a new signing up based on properties of the given signing up resource
 *
 * This route dispatches to these backend actions:
 *
 * – addExternalTechnicArticleByEventId
 * - putSigningUpByEventIdResourceTypeAndEntityId
 * - addExternalPersonByEventId
 *
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/drkserver.api.openapi.full.yaml#/event/putSigningUpByEventIdResourceTypeAndEntityId
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/drkserver.api.openapi.full.yaml#/event/addExternalPersonByEventId
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/drkserver.api.openapi.full.yaml#/event/addExternalTechnicArticleByEventId
 *
 */

const InputSchema = z.object({
    status: z.union([z.literal('AVAILABLE'), z.literal('NOT_AVAILABLE'), z.literal('REPLY_PENDING')]).default('AVAILABLE'),
    resource: Resource,
    periods: z
        .object({
            start: z.string().datetime(),
            end: z.string().datetime()
        })
        .array()
        .optional()
})

export default defineEventHandler(async (event) => {
    const { status, resource, periods } = await useValidatedBody(event, InputSchema)

    const eventId = getRouterParam(event, 'id')

    /**
     * Set or update signing up for member resources
     */
    if (resource.type === 'PERSONAL' && resource.external === false) {
        const { put: create } = await useApiProxy(event)

        return create(`events/${eventId}/signing-ups/PERSONAL/${resource.member.id}`, {
            status,
            periods
        })
    }

    /**
     * Set or update signing up for article resources
     */
    if (resource.type === 'TECHNIC' && resource.external === false) {
        const { put: create } = await useApiProxy(event)

        return create(`events/${eventId}/signing-ups/TECHNIC/${resource.article.id}`, {
            status,
            periods
        })
    }

    /**
     * Create signing up for external articles
     */
    if (resource.type === 'TECHNIC' && resource.external === true) {
        const { post: create } = await useApiProxy(event)

        return create(`events/${eventId}/signing-ups/external-technic-article`, {
            ...resource.externalArticle
        })
    }

    /**
     * Create signing up for external persons
     */
    if (resource.type === 'PERSONAL' && resource.external === true) {
        const { post: create } = await useApiProxy(event)

        return create(`events/${eventId}/signing-ups/external-person`, {
            ...resource.externalPerson
        })
    }

    return sendError(event, createError({ message: 'Not implemented' }))
})
