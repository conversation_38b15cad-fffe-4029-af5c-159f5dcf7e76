/**
 * Get the list of all signing ups for the given Event
 *
 * On their side the search api is based on POST requests.
 *
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/drkserver.api.openapi.full.yaml#/event/getEventSigningUpsByEventIdAndFilterCriteria
 */

type ListResponse = {
    totalItems: number
    offset: number
    limit: number
    items: any[]
}

export default defineEventHandler(async (event) => {
    const api = await useApiProxy(event)

    const eventId = getRouterParam(event, 'id')

    const body = {
        offset: 0,
        limit: 1000,
        source: 'ALL',
        organisations: [],
        types: ['TECHNIC', 'PERSONAL'],
        status: []
    }

    return api.post<ListResponse>(`/events/${eventId}/signing-ups`, body)
})
