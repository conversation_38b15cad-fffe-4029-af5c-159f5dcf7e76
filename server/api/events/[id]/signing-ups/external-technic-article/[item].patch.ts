/**
 * @see https://gitlab.drkserver.org/drkserver/documentation/api-documentation/-/blob/master/drkserver.api.openapi.final.yaml#/event/updateExternalTechnicArticleByEventIdAndExternalTechnicArticleId
 */

export default defineEventHandler(async (event) => {
    const { patch: editExternalMaterial } = await useApiProxy(event)

    const { id: eventId } = getRouterParams(event)
    const resource: ApiModel<'ExternalTechnicArticle'> = await readBody(event)

    const path = `events/${eventId}/signing-ups/external-technic-article/${resource.id}`

    return !!path ? editExternalMaterial(path, resource) : sendError(event, createError('Could not generate a valid path to repository'))
})
