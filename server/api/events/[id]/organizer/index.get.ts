/**
 * Backend want's us to use a dedicated method for updating organizers but there is no way to retrieve organizer individually
 */

export default defineEventHandler(async (event) => {
    const api = await useApiProxy(event)

    const eventId = getRouterParam(event, 'id')

    const query = {
        fields: ['ORGANIZER']
    }

    const eventData = await api.get(`/events/${eventId}/masterdata`, query)

    // Cut of organizer and return it
    return eventData['organizer']
})
