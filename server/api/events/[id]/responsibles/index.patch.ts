export default defineEventHandler(async (request) => {
    const body = await readBody(request)

    const { get, post, remove } = await useApiProxy(request)
    const responsibles = await get<ApiModel<'EventResponsible'>[]>(`/events/${request.context.params.id}/responsibles`)

    if (Array.isArray(responsibles)) {
        const getMembersToAdd = (members: ApiModel<'EventResponsible'>[]) => {
            return members.filter((member: ApiModel<'EventResponsible'>) => {
                return !responsibles.some((r) => r.masterdata.id === member.masterdata.id)
            })
        }
        const getMembersToRemove = (members: ApiModel<'EventResponsible'>[]) => {
            return Array.isArray(responsibles)
                ? responsibles.filter((member: ApiModel<'EventResponsible'>) => {
                      return !members.some((r) => r.masterdata.id === member.masterdata.id)
                  })
                : []
        }

        for (const responsible of getMembersToAdd(body)) {
            const url = `/events/${request.context.params.id}/responsibles`
            await post(url, responsible)
        }

        for (const responsible of getMembersToRemove(body)) {
            const url = `/events/${request.context.params.id}/responsibles/members/${responsible.masterdata.id}`
            await remove(url)
        }
    }

    return await get(`/events/${request.context.params.id}/responsibles`)
})
