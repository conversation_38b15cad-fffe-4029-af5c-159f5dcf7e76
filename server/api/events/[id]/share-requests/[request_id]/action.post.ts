export default defineEventHandler(async (event) => {
    const { get } = await useApiProxy(event)
    const decision: string = await readBody(event)

    // Available values for decision that API accept are : ACCEPT, REJECT. We are getting ACCEPTED and REJECTED so we have to swap it
    const updateDecision = decision === 'ACCEPTED' ? 'ACCEPT' : 'REJECT'

    return get(`/events/${event.context.params.id}/share-requests/${event.context.params.request_id}/action?decision=${updateDecision}`)
})
