/**
 * Backend provides a dedicated method for catering updates but there is no way to retrieve this list individually
 */

export default defineEventHandler(async (event) => {
    const api = await useApiProxy(event)

    const eventId = getRouterParam(event, 'id')

    const query = {
        fields: ['EVENT']
    }

    const eventData = await api.get(`/events/${eventId}/masterdata`, query)

    // Cut of caterings and return it
    return eventData['caterings']
})
