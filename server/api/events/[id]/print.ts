import { h3ResponseBlobToBase64 } from '~/modules/api/server/utils/h3-response-blob-to-b64'
// import { useAuthHeaders } from '~~/modules/api/server/utils/api-proxy'

export default defineEventHandler(async (event) => {
    const { get } = await useApiProxy(event)

    const { type = 'BASICS' } = getQuery(event)

    const data = await h3ResponseBlobToBase64(get<any>(`/events/${event.context.params?.id}/print`, { type }))

    return send(event, data, 'application/pdf')

    /**
     * @todo
     *
     * We should consider to just use the original server response
     */

    // const {
    //     api: { baseUrl }
    // } = useRuntimeConfig()

    // const headers = await useAuthHeaders(event)

    // const url = new URL(`${baseUrl}/events/${event.context.params?.id}/print`)
    // url.searchParams.append('type', type as string)

    // return proxyRequest(event, url.toString(), { headers })
})
