type MultipartEntry = Awaited<ReturnType<typeof readMultipartFormData>>[0]

function makeFile({ data, type, filename }: MultipartEntry) {
    return new File([new Blob([data], { type })], filename)
}

function makeBlob({ data, type }: MultipartEntry) {
    return new Blob([data], { type })
}

export default defineEventHandler(async (event) => {
    const { rawFetcher: fetch } = await useApiProxy(event)

    const eventId = getRouterParam(event, 'id')
    const body = await readMultipartFormData(event)

    const formData = new FormData()

    formData.append('data', makeFile(body.find(({ name }) => name === 'data')))
    formData.append('eventDocument', makeBlob(body.find(({ name }) => name === 'eventDocument')))

    return fetch(`/events/${eventId}/documents`, {
        method: 'POST',
        body: formData
    })
})
