import { h3ResponseBlobToBase64 } from '~/modules/api/server/utils/h3-response-blob-to-b64'
// import { useAuthHeaders } from '~~/modules/api/server/utils/api-proxy'

export default defineEventHandler(async (event) => {
    const { get } = await useApiProxy(event)

    const { sortOrder = 'PRESENT_FROM' } = getQuery(event)

    const data = await h3ResponseBlobToBase64(get<any>(`/events/${event.context.params?.id}/registration/ordered-export`, { sortOrder }))

    return send(event, data, 'application/pdf')
})
