type PermissionsResult = {
    dependendOrganisation: {
        id: number
        objectHash: string
        permissions: any
        name: string
    }
    isEventManager: boolean
    canCreateEvent: boolean
    canFinishApprenticeship: boolean
}[]

export default defineEventHandler(async (event) => {
    const { get } = await useApiProxy(event)
    return get<PermissionsResult>(`/events/permissions`)
})
