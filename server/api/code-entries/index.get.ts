import { z } from 'zod'
import { CodeEntry } from '~~/schema/models/code-entry'

export default defineEventHandler(async (event) => {
    const { type: typeOrTypes } = getQuery(event)

    // Ensure Array
    const types = typeOrTypes instanceof Array ? typeOrTypes : [typeOrTypes]

    // Call backend
    const api = await useApiProxy(event)
    return api.post<z.infer<typeof CodeEntry>[]>(`/code-entries`, types)
})
