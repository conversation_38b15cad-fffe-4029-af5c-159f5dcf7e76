/**
 * Our wrapper around POST organisations/search
 *
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/drkserver.api.openapi.full.yaml#/
 */

export default defineEventHandler(async (httpEvent) => {
    const { post: fetch } = await useApiProxy(httpEvent)

    const {
        searchTerm = '',
        sortby = 'NAME',
        limit = 10,
        offset = 0,
        types = [],
        organisationIds = [],
        membersCanBeAdded = null,
        technicArticleCanBeAdded = null,
        chainOfCommandEnabled = null,
        organisationIdsWithChilds = null
    } = await readBody(httpEvent)

    return await fetch('organisations/search', {
        offset,
        limit,

        sortBy: [
            {
                field: sortby,
                directionAsc: true
            }
        ],
        types: types,

        // The search tearm
        searchTerm,

        // Restrict to these organisations
        organisationIds: Array.isArray(organisationIds) ? organisationIds : [organisationIds],

        /**
         * A boolean false for the following parameters does not mean, that this search parameter is disabled.
         * Instead, we would explicitly search for organisations that I can not (for example) add members to.
         *
         * Usually we don't want this behaviour, so we set these parameters to `null*
         */
        membersCanBeAdded,
        technicArticleCanBeAdded,
        chainOfCommandEnabled,
        organisationIdsWithChilds
    })
})
