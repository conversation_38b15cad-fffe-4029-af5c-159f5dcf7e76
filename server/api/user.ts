import { useAuthClient, useAuthContext } from '#openid'

export default defineEventHandler(async (event) => {
    try {
        const auth = await useAuthContext(event)
        const client = await useAuthClient()
        const userData = await client.userinfo(auth.accessToken)

        return { ...userData }
    } catch (error) {
        //throw error;
        sendError(event, createError({ statusCode: 422 }))
    }
})
