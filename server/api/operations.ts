/**
 * Our wrapper around POST operations/search
 *
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/drkserver.api.openapi.full.yaml#/
 */

export default defineEventHandler(async (httpEvent) => {
    const { post: fetch } = await useApiProxy(httpEvent)

    const {
        limit = 10,
        offset = 0,
        active = true,
    } = await readBody(httpEvent)

    return await fetch('operations/search', {
        offset,
        limit,
        active
    })
})
