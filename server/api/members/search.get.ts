/**
 * Our wrapper around members search
 *
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/drkserver.api.openapi.full.yaml#/members/searchMembers
 */

import { z, zh, useValidatedQuery, useSafeValidatedQuery } from 'h3-zod'

type Response = {
    totalItems: number
    limit: number
    offset: number
    items: any[]
}

export default defineEventHandler(async (event) => {
    const { sortBy, ...query } = await useValidatedQuery(
        event,
        z.object({
            offset: z.coerce.number().default(0),
            limit: z.coerce.number().default(5),
            sortBy: z.union([z.literal('FIRSTNAME'), z.literal('LASTNAME', z.literal('LEADING_ORGANISATION'))]).default('FIRSTNAME'),
            searchTerm: z.string().default(''),
            searchScope: z.union([z.literal('MY_MEMBERS'), z.literal('GLOBAL_MEMBERS')]).default('MY_MEMBERS'),
            withAvatar: z.coerce.boolean().default(true)
        })
    )

    const { post } = await useApiProxy(event)

    const body = {
        ...query,
        sortBy: [
            {
                field: sortBy,
                directionAsc: true
            }
        ],
        organisationIds: [],
        leadingOrganisationIds: []
    }

    return post<Response>(`members/search`, body)
})
