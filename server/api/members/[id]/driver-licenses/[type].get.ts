import { z } from 'zod'
import { DriverLicense } from '~~/schema/models/driver-license'

export default defineEventHandler(async (event) => {
    const api = await useApiProxy(event)

    const type = getRouterParam(event, 'type')

    if (type !== 'street') {
        return sendError(event, createError("Only 'street' is allowed as type"))
    }

    const licenses = DriverLicense.array().parse(
        // Fetch...
        await api.get(`/members/${event.context.params.id}/driver-licenses`)
    )

    // Return licenses
    return licenses.find(({ type }) => type === 'Strasse') || sendNoContent(event)
})
