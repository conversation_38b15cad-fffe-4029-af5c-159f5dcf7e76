import { z, useValidatedQuery, } from 'h3-zod'

type Response = {
    totalItems: number
    limit: number
    offset: number
    items: any[]
}

export default defineEventHandler(async (event) => {
    const { post } = await useApiProxy(event)

    const queryData = await useValidatedQuery(
        event,
        z.object({
            offset: z.coerce.number().default(0),
            limit: z.coerce.number().default(10),
            searchTerm: z.string().default(''),
        })
    )

    return post<Response>(`address/search`, {
        ...queryData,
        sortBy: [
            {
                field: 'CATEGORY',
                directionAsc: true
            }
        ]
    })
})

