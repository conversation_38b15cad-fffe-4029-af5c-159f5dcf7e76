import ical from 'ical-generator'
import { z } from 'zod'

const InputSchema = z
    .object({
        id: z.number(),
        start: z.coerce.date(),
        end: z.coerce.date(),
        summary: z.string(),
        description: z.string().nullable().optional(),
        location: z.string().nullable().optional()
    })
    .array()

export default defineEventHandler(async (event) => {
    const events = InputSchema.parse(await readBody(event))

    const calendar = ical({ name: 'DRK-Veranstaltungen' })

    events.forEach((element) => {
        calendar.createEvent({ ...element })
    })

    return send(event, calendar.toString(), 'text/calendar')
})
