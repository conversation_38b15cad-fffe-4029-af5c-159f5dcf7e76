# Flows and processes of "Event planning"

# Goals

-   [ ] Provide an architecture that makes it easy to implement the processes.
-   [ ] Implement the processes in their simplest form to prove that the archtitecture works. For example: When creating objects, only the absolutely
    necessary properties are captured.
-   [ ] The planning processes are strictly typified so that developers must adhere to the contracts drafted here.

## Components

The entry point to event planning is the component `pages/events/[id]/planning.vue`.

All components used in the planning process are located in the `/components/event/planning` folder and are currently imported automatically.

An attempt is made to reduce the responsibility of a component to as single a function as possible.

Some component names are suffixed to make it easier to identify where they are used.

Examples:

– \*.dialog.vue

-   \*.panel.vue

! It has to be discussed if this is useful.

### Dialogs

-   `useDialogController`

Some Dialogs may appear as Composers or Bottom-Sheets.

### Injections

Mutations should ideally be defined in one place and not hidden in child elements.

So that functions can be used in child elements we provide an InjectionKey `EventPlanningActions`.

## Composables

-   `useEventPlanning([event])`
-   `useEventMutations([event])`

## Event planning action function flow

Example:

```
async function removeEventPost(eventPost: ApiModel<'EventPost'>) {
    setLoader(true)

    try {
        await commit('removeEventPost', eventPost)
        notify({
            type: 'success',
            content: `Die Planstelle "${eventPost.description}" wurde entfernt.`
        })
        await commit('reloadEvent')
    } catch {
        notify({
            type: 'error',
            content: `Beim entfernen der Planstelle "${eventPost.description}" ist ein Fehler aufgetreten.`
        })
    }

    setLoader(false)
}
```

Responsibilities:

-   `commit` responsibility is to mutate data in backend (send a well-formed request)
-   `removeEventPost` (which is the actual Event planning action) have responsibility to manage: loading state, error state (in our case error
    notification), success notifications and reloading of necessary data

### Why try/catch?

TL;DR Best known solution to keep responsibilities seperated as stated above.

Other solutions that have not met expectations:

1. Using `onSuccess` when defining `useMutation`. Example:

```
const { mutateAsync: removeEventPost } = useMutation({
    mutationFn: (eventPost: ApiModel<'EventPost'>) => {
        return $fetch(`/api/events/${unref(input).id}/event-posts/${eventPost.id}`, { method: 'delete' })
    },
    onSuccess: () => {
        return queryClient.invalidateQueries(['events', unref(input).id])
    }
})
```

This actually waits until mutations finishes, but do not allow an easy way to control whether the necessary data should be reloaded (invalidated).

2. Using `onSuccess` when firing `mutation`. Example:

```
await commit('removeEventPost', eventPost, {
    onSuccess: async () => {
        notify({
            type: 'success',
            content: `Veranstaltungsbeitrag erfolgreich erstellt.`
        })

        await commit('reloadEvent')
    },
    onError: async () => {
        notify({
            type: 'error',
            content: `Beim entfernen der Planstelle "${eventPost.description}" ist ein Fehler aufgetreten.`
        })
    },
    onSettled: async () => {
        setLoader(false)
    }
})
```

Multiple issues here:

-   `onSuccess` when firing `mutation` does not wait to finish anything inside to fire up `onSettled`. We can move `setLoader` to `onSuccess` and
    `onError` but this way we duplicate code.
-   Returning a promise from `onSuccess` when firing `mutation` does not wait to finish promise before `onSuccess` is finished (does NOT work
    similarly to point 1.)
-   When we put anything after `await commit('createEventPost' (...)` (example `setLoader` or any further logic) it will fire up the moment
    `onSettled` is fired up (so before actually `onSuccess` finishes).
-   When `await commit('createEventPost' (...)` fails (i.e. due to 400/404 error) it will throw an error at this point and NOT fire any logic
    afterwards
