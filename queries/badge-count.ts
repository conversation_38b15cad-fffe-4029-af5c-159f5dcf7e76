import { useQuery } from '@tanstack/vue-query'

export function useFetchBadgeCount() {
    return useQuery(
        ['badge-count'],
        async () => {
            return await $fetch<BadeCountType>(`/api/badge-count`)
        },
        {
            select: (data) => data,
            refetchOnWindowFocus: true
        }
    )
}

type BadeCountType = {
    messagesCount: number
    todosCount: number
    notificationsCount: number
}
