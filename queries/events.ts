import { useQuery, useMutation, useQueryClient } from '@tanstack/vue-query'

export function useRemoveEvent(id: number) {
    const client = useQueryClient()
    const { schedule } = useNotifications()
    const message = schedule()

    return useMutation({
        mutationFn: (event: ApiModel<'Event'>) => {
            return $fetch<any, any, { method: 'delete' }>(`/api/events/${id}`, { method: 'delete' })
        },

        onMutate: (variables) => {
            message.show(`Das Ereignis "${variables.extendedDescription}" wird gelöscht.`)
        },
        onSuccess: async (data, variables) => {
            await client.invalidateQueries(['events'])
            message.makeSuccess(`Das hat geklappt: Du hast das Ereignis "${variables.extendedDescription}" gelöscht.`)
        },

        onError: (error, event) => {
            message.makeError(`Du wolltest das Ereignis "${event.extendedDescription}" löschen. Das hat nicht geklappt.`)
        }
    })
}
