import { useQuery } from '@tanstack/vue-query'

export function useFetchFavoriteMaterials() {
    return useQuery(
        ['favorite-materials'],
        async () => {
            return await $fetch<object>(`/api/events/favorite-technic-articles`)
        },
        {
            select: (data: ApiModelProps<'FavoriteTechnicArticle'>[]) => useModelFactory('FavoriteTechnicArticle').fromJson(data),
            staleTime: Infinity,
            cacheTime: Infinity
        }
    )
}
