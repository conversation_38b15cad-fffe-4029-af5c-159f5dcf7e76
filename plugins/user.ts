import { sortBy, uniqBy } from 'lodash-es'

export default defineNuxtPlugin(async () => {
    try {
        // 1. Fetch permissions

        const { data: permissions } = await useFetch('/api/events/permissions')

        const dependendOrganisations = computed(() => {
            return permissions.value?.map((entry) => useModelFactory('Organization').fromJson(entry.dependendOrganisation))
        })

        const organisationsWithPermissionToCreateEvents = computed(() => {
            const organisations = (permissions.value?.filter((entry) => entry.canCreateEvent) ?? []).map((entry) =>
                useModelFactory('Organization').from<PERSON>son(entry.dependendOrganisation)
            )

            return sortBy(uniqBy(organisations, 'id'), 'name')
        })

        // Can create event if he has permission for at least one dependend organisation
        const canCreateEvent = computed(() => {
            return !!permissions.value?.find((entry) => entry.canCreateEvent)
        })

        // 2. Fetch user's file

        const { data: myFile } = await useFetch('/api/my-file', {
            transform: ({ basicData, ...rest }: any) => {
                return {
                    basicData: useModelFactory('MemberData').fromJson(basicData),
                    ...rest
                }
            }
        })

        return {
            provide: {
                user: {
                    basicData: myFile.value?.basicData as ApiModel<'MemberData'>,
                    dependendOrganisations,
                    organisationsWithPermissionToCreateEvents,
                    canCreateEvent,
                    permissions,
                }
            }
        }
    } catch (error) {
        console.log(error)
    }
})
