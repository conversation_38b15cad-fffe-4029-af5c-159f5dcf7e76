import { Interval, Duration, DateTime } from 'luxon'

const dateRangeFormat = new Intl.DateTimeFormat('default', {
    month: '2-digit',
    day: '2-digit',
    hour: 'numeric',
    minute: 'numeric'

})

function isValidDate(date: Date) {
    if (!(date instanceof Date)) {
        return false;
    }

    // Convert JavaScript Date object to Luxon DateTime
    const dateTime = DateTime.fromJSDate(date);
    // Check if the date is valid
    return dateTime.isValid;
}

export default defineNuxtPlugin(() => ({
    provide: {
        formatDate: (date: Date, style: Intl.DateTimeFormatOptions['dateStyle'] = 'long') => {
            if (!!date) {
                return new Intl.DateTimeFormat('default', { dateStyle: style }).format(date)
            }

            return null
        },

        formatDateWithAbbreviatedDay: (
            date: Date,
            weekday: Intl.DateTimeFormatOptions['weekday'] = 'short',
            day: Intl.DateTimeFormatOptions['day'] = '2-digit',
            month: Intl.DateTimeFormatOptions['month'] = '2-digit',
            year: Intl.DateTimeFormatOptions['year'] = 'numeric',
        ) => {
            if (isValidDate(date)) {
                return new Intl.DateTimeFormat('default', { weekday, day, month, year}).format(date)
            }

            return null
        },

        formatDateWithAbbreviatedDayAndTime: (
            date: Date,
            weekday: Intl.DateTimeFormatOptions['weekday'] = 'short',
            day: Intl.DateTimeFormatOptions['day'] = '2-digit',
            month: Intl.DateTimeFormatOptions['month'] = '2-digit',
            year: Intl.DateTimeFormatOptions['year'] = 'numeric',
            hour: Intl.DateTimeFormatOptions['hour'] = '2-digit',
            minute: Intl.DateTimeFormatOptions['minute'] = '2-digit',
        ) => {
            if (isValidDate(date)) {
               return new Intl.DateTimeFormat('default', { weekday, day, month, year, hour, minute}).format(date)
            }
            return null
        },

        formatDateRangeWithAbbreviatedDayAndTime: (start: Date, end: Date) => {
            if (isValidDate(start) && isValidDate(end)) {
                return new Intl.DateTimeFormat('default', { weekday: 'short', day: '2-digit', month: '2-digit', year: '2-digit', hour: '2-digit', minute: '2-digit'}).formatRange(start, end)
            }

            return null
        },

        formatTime: (date: Date, style: Intl.DateTimeFormatOptions['timeStyle'] = 'short') => {
            if (isValidDate(date)) {
                return new Intl.DateTimeFormat('default', { timeStyle: style }).format(date)
            }

            return null
        },

        formatDateTime: (date: Date, options?: Intl.DateTimeFormatOptions) => {
            const { dateStyle = 'long', timeStyle = 'short' } = options || {}
            if (!!date) {
                return new Intl.DateTimeFormat('default', { dateStyle, timeStyle }).format(date)
            }

            return null
        },

        formatDateRange: (start: Date, end: Date) => {
            if (isValidDate(start) && isValidDate(end)) {
                return dateRangeFormat.formatRange(start, end)
            }

            return null
        },

        formatDuration: (start: Date, end: Date, precision: 'exact' | 'lax' = 'exact') => {
            if (!start || !end) {
                return null
            }

            // we do not want to take seconds and miliseconds in considiration for duration
            const startDateTime = DateTime.fromJSDate(start).set({ second: 0, millisecond: 0 });
            const endDateTime = DateTime.fromJSDate(end).set({ second: 0, millisecond: 0 });

            const interval = Interval.fromDateTimes(startDateTime, endDateTime)

            if (!interval.isValid) {
                return null
            }

            if (precision === 'lax') {
                if (interval.length('hours') > 14 * 24) {
                    const duration = interval.toDuration(['weeks', 'days', 'hours', 'minutes'])

                    return Duration.fromObject({
                        weeks: duration.weeks
                    }).toHuman({ unitDisplay: 'long' })
                } else if (interval.length('hours') > 7 * 24) {
                    const duration = interval.toDuration(['days', 'hours', 'minutes'])

                    return Duration.fromObject({
                        days: duration.days
                    }).toHuman({ unitDisplay: 'long' })
                } else if (interval.length('hours') > 72) {
                    const duration = interval.toDuration(['days', 'hours', 'minutes'])

                    return Duration.fromObject({
                        days: duration.days
                    }).toHuman({ unitDisplay: 'long' })
                } else if (interval.length('hours') >= 2) {
                    const duration = interval.toDuration('hours')

                    return Duration.fromObject({
                        hours: duration.hours
                    }).toHuman({ unitDisplay: 'long' })
                } else {
                    const duration = interval.toDuration('minutes')

                    return Duration.fromObject({
                        minutes: duration.minutes
                    }).toHuman({ unitDisplay: 'long' })
                }
            } else {

                if(interval.length()===0) {
                    return Duration.fromObject({minutes: 0}).toHuman({ unitDisplay: 'long' })
                }

                const duration = interval.toDuration(['weeks','days', 'hours', 'minutes']).normalize().rescale()
                return duration.toHuman({ unitDisplay: 'long' })
            }
        }
    }
}))
