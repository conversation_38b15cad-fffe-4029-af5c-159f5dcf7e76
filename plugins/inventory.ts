import ListType from '~~/enums/listType'

export default defineNuxtPlugin(async (_nuxtApp) => {
    try {
        const { data } = await useFetch(`/api/code-entries/${ListType.SigningUpStatus}`)

        const signingUpStates: ApiModel<'CodeEntry'>[] = useModelFactory('CodeEntry').fromJson(data.value)

        const codeEntryLists = [
            //...all code entries that are often used and should therefore be loaded on startup
            ListType.DriversLicense,
        ]

        const { data: codeEntries } = await useFetch(`/api/code-entries`, {
            query: {
                type: codeEntryLists
            },
            transform(data) {
                return useModelFactory('CodeEntry').from<PERSON>son(data)
            }
        })

        /**
         * @todo Check if we have to await the result.
         *
         * The first time dependent types are needed is after a user interaction
         */
        const { data: dependentTypes } = useFetch('/api/technic/dependent-types')

        return {
            provide: {
                signingUpStates,
                codeEntries,
                dependentTypes
            }
        }
    } catch (error) {
        console.log(error)
    }
})
