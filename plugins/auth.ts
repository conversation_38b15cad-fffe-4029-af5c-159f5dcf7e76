export default defineNuxtPlugin(async (_nuxtApp) => {
    const { data: user } = await useFetch('/api/user')

    /**
     * If the user is logged in and no device cookie is set, we call the /client-detection route.
     * This way we make sure that the appropriate cookies 'X-Client-Detection' and 'X-Client-Name' are set.
     *
     * We do this here because it resolves the Promise of this plugin before the frontend makes API calls.
     * (These API calls sometimes happen in parallel, so API detection would happen several times).
     */
    if (!!user.value && !useCookie('X-Client-Detection').value) {
        await useFetch('/api/client-detection')
    }

    return {
        provide: {
            isAuthenticated: !!user.value
        }
    }
})
