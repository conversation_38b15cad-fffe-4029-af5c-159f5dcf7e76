function fullName(data: ApiModel<'MemberData'> | ApiModel<'ExternalPerson'>) {
    if(data) {
        return [data.firstname, data.lastname].join(' ')
    } else {
        return "Gelöschte Person"
    }
}

function resourceName(resource: ApiModel<'Resource'>) {
    if (resource.type === 'TECHNIC') {
        if (!!resource.external) {
            return resource.externalArticle.name
        } else {
            return resource.article.identification || 'Unbekannter Artikel'
        }
    }

    if (resource.type === 'PERSONAL') {
        if (!!resource.external) {
            return fullName(resource.externalPerson)
        } else {
            return fullName(resource.member)
        }
    }
}

export default defineNuxtPlugin(() => {
    return {
        provide: {
            fullName,
            resourceName
        }
    }
})
