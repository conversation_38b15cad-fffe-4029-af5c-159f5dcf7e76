{"volar.takeOverMode.enabled": true, "volar.formatting.enable": false, "coc.preferences.formatOnSaveFiletypes": ["vue", "js", "jsx", "ts", "tsx", "html", "css", "scss", "less", "json", "yml", "yaml", "md", "markdown"], "tailwindCSS.headwind.runOnSave": true, "tailwindCSS.classAttributes": ["class", "className", "outer-class", "wrapper-class", "label-class", "input-class", "message-class", "messages-class"], "tailwindCSS.includeLanguages": {}, "tailwindCSS.experimental.configFile": "modules/ui/tailwind.config.js", "tsserver.enable": false, "diagnostic.checkCurrentLine": true, "cSpell.enabled": false, "colors.filetypes": ["*"]}