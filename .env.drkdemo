# BASE_URL = http://localhost:3000

# KEYCLOAK_CLIENT_ID=portal-vue-client
KEYCLOAK_REALM=drkdemo
KEYCLOAK_URL=https://login.drkserver.org/auth
# KEYCLOAK_CLIENT_SECRET=

REDIS_URL = 127.0.0.1:6379
REDIS_KEY_PREFIX=__nuxt
#REDIS_USERNAME=
#REDIS_PASSWORD=

API_APP_NAME='Local Development'
API_BASE_URL=https://demo-api.drkserver.org

# SENTRY_DSN=https://<EMAIL>/41
# SENTRY_TUNNEL=tunnel/

# That URL is used for generation of legacy URLs and calls to "device detection"
PORTAL_URL=https://demo.drkserver.org