/**
 * Describes possible values of VALUELIST_OPERATION_TYPE
 *
 * @see https://develop.drkserver.mogic-server.de/v/api/code-entries/40
 *
 * This is the `value1` of a code-entry
 */
export enum EventOperationType {
	Operation = 'EUeD', // 25233, // "Einsatz/Übung/Dienst"
	Apprenticeship = 'Ausb' // 25232, // Ausbildung
}

export enum EventStatusType {
	waitingForApproval = 'WAITING_FOR_APPROVAL',
	approved = 'APPROVED',
	canceled = 'CANCELED',
	finished = 'FINISHED'
}
export default EventOperationType