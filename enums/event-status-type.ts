/**
 * Describes possible values of VALUELIST_STATUS_TYPE
 *
 * @see https://develop.drkserver.mogic-server.de/v/api/code-entries/40
 *
 * This is the `value1` of a code-entry
 */
export enum EventStatusEnum {
  WAITING_FOR_APPROVAL,
  APPROVED,
  CANCELED,
  FINISHED
}

export enum EventStatusLabelEnum {
  freigegeben,
  bestätigt,
  abgesagt,
  abgeschlossen
}

export enum EventStatusInfoEnum {
  'Ist noch nicht bestätigt (nur für Planende wichtig)',
  'Ist bestätigt (nur für Planende wichtig)',
  'Ist abgesagt',
  'Ist abgeschlossen'
}

export type EventStatusLabel = keyof typeof EventStatusLabelEnum
export type EventStatusType = keyof typeof EventStatusEnum

export default EventStatusType