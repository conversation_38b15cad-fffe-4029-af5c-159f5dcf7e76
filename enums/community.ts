
export enum Community {
    ALL = 'ALL',
    BER = 'BER',
    BW = 'BW',
    JRK = 'JRK',
    WUS = 'WUS',
    WW = 'WW'
}

export default Community

export function withoutAbbreviation(community: Community): string {
    switch (community) {
        case Community.ALL:
            return 'Gesamte Gliederung';
        case Community.BER:
            return 'Bereitschaft';
        case Community.BW:
            return 'Bergwacht';
        case Community.JRK:
            return 'Jugendrotkreuz';
        case Community.WUS:
            return 'Wohlfahrts- und Sozialarbeit';
        case Community.WW:
            return 'Wasserwacht';
        default:
            return ''; // Handle the default case as needed
    }
}
