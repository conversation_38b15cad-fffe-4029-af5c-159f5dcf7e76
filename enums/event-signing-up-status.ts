/**
 * Describes possible values of VALUELIST_EVENT_RESOURCESIGNINGUPSTATUS
 * 
 * @see https://develop.drkserver.mogic-server.de/v/api/code-entries/602
 * 
 * 
 * UNKNOWN = VALUELIST_EVENT_RESOURCESIGNINGUPSTATUS.id where value1 == "e" //Rückmeldung offen 
 * AVAILABLE = VALUELIST_EVENT_RESOURCESIGNINGUPSTATUS.id where value1 == "v" // verfügbar 
 * PARTIAL = 'PARTIAL', 
 * UNAVAILABLE = VALUELIST_EVENT_RESOURCESIGNINGUPSTATUS.id where value1 == "nv" // nicht verfügbar
 * 
 * The state "PARTIAL" is virtual and means: The status is "AVAILABLE" but the contaning signing up model has a list of periods.
 * 
 * The numeric value is used for ordering of signing ups
 * @see https://jira.mogic.com/browse/DEL-283
 * 
 * Rejeccted state:
 * @see https://jira.mogic.com/browse/DEL-155
 * 
 */
export enum EventSigningUpStatus {
	AVAILABLE = 100,
	PARTIAL = 80,
	UNKNOWN = 50,
	REJECTED = 30,
	UNAVAILABLE = 10
}
export default EventSigningUpStatus
