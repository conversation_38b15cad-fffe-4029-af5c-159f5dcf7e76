import ListType from '~~/enums/listType'

// CodeEntries do not have category field. So we create object and add category description to a codeEntry if category exist and null if not
export const statusAtDrk = {
    [ListType.MemberStatus]: null
} as const

export const membershipEntries = {
    [ListType.MembershipentryE]: null,
    [ListType.MembershipentryFD]: null,
    [ListType.MembershipentryF]: null,
    [ListType.MembershipentryS]: null,
    [ListType.MembershipentryUH]: null,
    [ListType.MembershipentryEH]: null
} as const

export const typesOfMembership = {
    [ListType.TypeOfMembershipE]: null,
    [ListType.TypeOfMembershipFD]: null,
    [ListType.TypeOfMembershipF]: null,
    [ListType.TypeOfMembershipS]: null,
    [ListType.TypeOfMembershipUH]: null,
    [ListType.TypeOfMembershipEH]: null,
    [ListType.TypeOfMembershipH]: null
} as const

export const membershipGroups = {
    [ListType.GroupCategory]: null
} as const

export const membershipGroupDescriptions = {
    [ListType.GroupCategoryDescBound]: 'Gremium/Gruppe/Ausschuss (Bundesebene)',
    [ListType.GroupCategoryDescJrk]: 'Jugendrotkreuz',
    [ListType.GroupCategoryDescLa]: 'Gremium/Gruppe/Ausschuss (LV-Ebene)',
    [ListType.GroupCategoryDescKa]: 'Gremium/Gruppe/Ausschuss (KV-Ebene)',
    [ListType.GroupCategoryDescRkg]: 'Rotkreuzgemeinschaft',
    [ListType.GroupCategoryDescPv]: 'Präsidium/Vorstand',
    [ListType.Department]: 'Abteilung / Fachbereich / Stabsstelle etc.',
    [ListType.AgeAndHonorCamaraderie]: 'Alters- und Ehrenkameradschaft',
    [ListType.WorkingCircle]: 'Arbeitsgruppe / -kreis',
    [ListType.CommitteeGroupOvLevel]: 'Gremium/Gruppe/Ausschuss (OV-Ebene)',
    [ListType.CommitteeGroupDistrictLevel]: 'Gremium/Gruppe/Ausschuss (Bezirksebene)',
    [ListType.BloodDonationSystem]: 'Blutspendewesen',
    [ListType.Group]: 'Gruppe',
    [ListType.PersonalInformation]: 'Personenauskunft',
    [ListType.Instructor]: 'Ausbilder',
    [ListType.ProjectGroup]: 'Projektgruppe',
    [ListType.ServiceDog]: 'Diensthundearbeit',
    [ListType.ForeignAid]: 'Auslandshilfe',
    [ListType.Management]: 'Leitung',
    [ListType.Readiness]: 'Bereitschaft',
    [ListType.LocalGroup]: 'Ortsverein / -gruppe',
    [ListType.CrisisManagement]: 'Krisenmanagement',
    [ListType.Lifeguards]: 'Wasserwacht',
    [ListType.EmergencyServices]: 'Einsatzdienste',
    [ListType.MountainRescueService]: 'Bergwacht',
    [ListType.InformationOffice]: 'Amtliches Auskunftsbüro',
    [ListType.MissingPersonTracingService]: 'Suchdienst',
    [ListType.GenericBRK]: 'BRK - Gremium/Gruppe/Ausschuss'
} as const

export const operations = {
    [ListType.OperationCategory]: null
} as const

export const operationDescriptions = {
    [ListType.OperationCategoryDesc]: null
} as const

export const functions = {
    [ListType.Function]: null
} as const

export const operationQualifications = {
    [ListType.Leadingship]: 'Führung',
    [ListType.Ambulance]: 'Sanitätsdienst',
    [ListType.SupportService]: 'Betreuungsdienst',
    [ListType.ITService]: 'IuK',
    [ListType.WaterRescue]: 'Wasserwacht',
    [ListType.NursingService]: 'Pflegedienst',
    [ListType.YouthRedCross]: 'Jugendrotkreuz',
    [ListType.OtherOperationQualification]: 'Sonstige Einsatzqualifikationen',
    [ListType.ABCService]: 'CBRN-Dienst',
    [ListType.TechAndSafety]: 'Technik und Sicherheit',
    [ListType.InformationOnPersons]: 'Personenauskunft',
    [ListType.PSNV]: 'PSNV',
    [ListType.MountainRescue]: 'Bergwacht',
    [ListType.UnboundVolunteers]: 'Ungebundene Helfer'
} as const

export enum CodeEntriesNames {
    StatusAtDrk = 'statusAtDrk',
    TypesOfMembership = 'typesOfMembership',
    MembershipGroups = 'membershipGroups',
    MembershipEntries = 'membershipEntries',
    MembershipGroupDescriptions = 'membershipGroupDescriptions',
    Operations = 'operations',
    OperationDescriptions = 'operationDescriptions',
    Functions = 'functions',
    OperationQualifications = 'operationQualifications'
}
