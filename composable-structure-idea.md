# Composable structure idea

Documentation of this part is written as is, it might need update in future to include reasoning and more examples.

`scope` can include any functionality scope that makes sense from architecture perspective. We are using i.e. `event-planning` and
`event-manage-resources` scopes.

Example structure:

-   `scope-mutations`: Mutations (requests) for specific scope (i.e. create event, create event post, delete event post). Can include function to
    reload/refresh the scope.
-   `scope`: Common functions (helpers) for specific scope (i.e. event posts getter, get signing ups by type)
-   `scope-actions`: Actions that use mutations and handle their side effects via UI/UX (i.e. loaders, notifications and error handling). They should
    not be responsible for dialog reveals.
-   `scope-dialogs`: Dialogs that are used within the scope
-   `scope-loader`: Just a loader for specific scope

There will be name conficts in `scope-mutations` and `scope-actions`, but I think there is very low probability that you will need to use i.e.
`createEventPost` from scope-mutations and `createEventPost` from scope-actions in the same component (apart from main one that injects
`scope-actions`).

## Examples

Find examples in:

-   `composables/event-planning`
-   `composables/event-manage-resources`

You can find usage of those composables in specific components.

It would be nice to fill this part with actual code examples.
