# Authentication with Keycloak

Users of the DRKServer are authenticated against a central Keycloak service.

Our goal is to have authentication requests and state handling happen on the server (the server-side Nuxt), not client.

This module provides all the tools to authenticate users and make the login status available in the Nuxt application.

_Specific problem_: There is no(??) good node middleware for openID authentication as open source projects. Unfortunately, all ready-made solutions are based on Express and don't work well with Nuxt 3's new underlaying [nitro](https://v3.nuxtjs.org/guide/concepts/server-engine) server engine. We therefore decided to rebuild the necessary middleware for [nitropack](https://github.com/unjs/nitro) and [h3](https://github.com/unjs/h3).

🤷‍♀️ Nevertheless, like almost all Node implementations, we use the "[open id](https://www.npmjs.com/package/openid-client)" library to provide needed functions of an OpenID Relying Party.

## Components of the module:

The authentication flow happens with the interaction of these components:

-   Session handling
-   Request handlers for login and logout
-   Refresh middleware
-   Global Auth-State and Nuxt-Plugin

The authentication status is determined on the incoming request within the nitro layer. This is essentially the task of the session handler, which manages and evaluates an HTTP-Only cookie.

The same is true for login and logout handlers. They do not leave the server layer and are unknown to the Vue application itself. Authentication calls must consequently request new documents. (Means bypassing the Vue router a.k.a hard loads).

An immutable auth state is then set in the Nuxt application's SSR process, to which the Vue application (think of navigation guards) can react.

📣 The Auth module only provides status information. It is not responsible for protecting the application itself. That is supposed to happen on application level.

### Public server routes

The module automatically registers all public server routes that are relevant for user authentication.

#### /login

...is responsible for remembering the URL to which users should be directed after a succesful login. (🚸 Not yet)
Then redirects to Keycloak in order to perform a login.

-   Implementation: [./middleware/login.ts](middleware/login.ts)
-   Example: https://develop.drkserver.mogic-server.de/login

#### /callback

...is called after successful login by Keycloak. Verifies callback params and internally stores the recieved token set in user session. It redirects back to a valid application route. (🚸 "/" for now)

-   Implementation: [./middleware/callback.ts](middleware/callback.ts)

#### /logout

...performs a request to Keycloak in order to end current session in Keycloak. Redirects back to "/" then. Thereby the token set is removed from current user session.

-   Implementation: [./middleware/logout.ts](middleware/logout.ts)
-   Example: https://develop.drkserver.mogic-server.de/logout

### Refresh Middleware

The module pushes a refresh middleware to the top of the Nitro stack so that it is processed on every HTTP request to the Nuxt application.

-   Implementation: [./middleware/refresh.ts](middleware/refresh.ts)

### Autoimported Functions

The module provides two functions in the global Nitro context (i.e. SSR only). So in middleware, API or router handlers they do not need to be imported explicitly.

`useAuthContext` extracts the authentication status (e.g. `isAuthenticated` oder `accessToken`) from the current request. For example, outgoing API requests can be authenticated this way.

Made up example for an imaginary API handler:

```typescript
export default defineEventHandler(async (event) => {
  const auth = await useAuthContext(event);

  return $fetch("https://other/api/todos", { headers: {
      Authentication: `Bearer ${auth.accessToken}`
  }})
});
```

`useAuthClient` provides the global OpenID client. Together with the `useAuthContext`, this can be used to create arbitrary OpenID middlewares. (Currently there is no use case for this).

The middleware of this module were assembled like this. So you can have a look in the [middleware directory](./middleware/) to find examples of interaction.

_Implementations_:

-   [./lib/auth-context.ts](lib/auth-context.ts)
-   [./lib/auth-client.ts](lib/auth-client.ts)

If necessary, the functions can also be imported explicitly from the virtual namespace "#openid".

```typescript
import { useAuthContext, useAuthClient } from "#openid";
```

## Securing the app

(🚸 Currently this lives in the application, but should potentially be transferred to the module)

The crucial role in communicating the authentication state from server to client is fulfilled by the Composable `useAuthState`. It stores the state of authentication (🚸 just yay or nay atm, but could potentially also include user profile or similar) in a variable that is created on the server and then hydrated in the client.

To ensure that this variable is already populated on the server, it is added to the Nuxt context in a dedicated plugin. Within the Vue application, the authentication status can be accessed with the $auth shortcut.

Example in Typescript:

```typescript
const { $auth } = useNuxtApp();
// same as $auth = useAuthState();
console.log($auth);
```

Example in Vue SFC:

```html
<template>
    <div>
        <button v-if="$auth === true">Sign out</button>
    </div>
</template>
```

Implementations:

-   Composable; [../../composables/auth-state.ts](../../composables/auth-state.ts)
-   Plugin: [../../plugins/auth/](../../plugins/auth/index.ts)

### Navigation guards

It is up to the application to handle the authentication status. This can happen, for example, in middlewares (a.k.a navigation guards).

If you want to ensure that all application routes are secured and unauthenticated users are prompted to log in, you can store this middleware in the projects `~/middleware/` folder.

Example: `requires-auth.global.ts`

```typescript
export default defineNuxtRouteMiddleware(async (_to, _from) => {
    if (!useAuthState()) {
        const { public: { baseUrl } } = useRuntimeConfig();
        return await navigateTo(baseUrl + "/login");
    }
});
```

## Session storage

Session data is stored on the server side. If necessary (e.g. user has successfully logged in), token set parameters are stored serialized as JSON strings for the corresponding session ID. The session ID itself is stored in a cookie (http-only be default).

A token set consists of these properties and the session handler can generate a valid [TokenSet-Object](https://github.com/panva/node-openid-client/blob/main/docs/README.md#class-tokenset) out of them:

-   `access_token`
-   `token_type`
-   `id_token`
-   `refresh_token`
-   `expires_in`
-   `expires_at`
-   `session_state`

~~Two storage adapters are available:~~

-   ~~[LRU-Cache](lib/session-stores/lru-cache.ts) stores session data within the node process. The memory is therefore empty after a restart of the process (e.g. deployment)~~.
- A [Redis adapter](lib/session-stores/redis.ts) has to be configured.

How the Redis is initialized can be controlled by the environment variables `REDIS_URL`, `REDIS_SENTINELS` and `REDIS_NODES`.

`REDIS_NODES` activates Redis cluster
`REDIS_SENTINELS` activates Redis-Sentinel
`REDIS_URL` activates Redis-Standalone

Redis-Cluster is preferred over Redis-Sentinel. The standalone Redis is treated as a fallback and should be configured for each development machine or as the simple solution for preview systems.

All Redis connections can be initialized with these optional parameters:

- `REDIS_KEY_PREFIX`
- `REDIS_USERNAME`
- `REDIS_PASSWORD`

When Sentinel-Mode is enabled these optional parameters are taken into account:

- `REDIS_SENTINEL_NAME` (the master name)
- `REDIS_SENTINEL_USERNAME` (acl / sentinel user)
- `REDIS_SENTINEL_PASSWORD` (acl / sentinal password)

Implementation:

-   [useAuthSession](./lib/auth-session.ts)
-   ~~[session-stores/lru-cache](lib/session-stores/lru-cache.ts)~~
-   [session-stores/redis](lib/session-stores/redis.ts)

## Configuration

(🚸 The default settings need a review)

The Auth module works out of the box with environment variables but can also be configured in `~/nuxt.config.ts`.

For example:

```typescript
export default defineNuxtConfig({
    ssr: true,

    modules: [
        "~/modules/auth",
    ],

    auth: {
        keycloak: {
            clientId: 'another-client-',
            clientSecret: 'my-secret-12345'
        },
        session: {
            type: 'redis',
            cookie: {
                name: 'session',
            },
            storage: {
                url: ' 127.0.0.1:6379'
                options: {
                    keyPrefix: 'drk:session:data',
                }
            }
        }
    }
})
```

### Available options

#### `.keycloak`

-   `url`: string (defaults to: `process.env.KEYCLOAK_URL`, example: "https://login.drkserver.org/auth")
-   `clientId`: string (defaults to: `process.env.KEYCLOAK_CLIENT_ID`)
-   `realm` : string (defaults to: `process.env.KEYCLOAK_REALM`)
-   `clientSecret` string (defaults to: `process.env.KEYCLOAK_CLIENT_SECRET`)

#### `.session`

-   `type`: `redis` or `lru` (Should be set to `redis` when `process.env.REDIS_URL` is set)

#### `session.cookie`

-   `name`: string (defaults to "auth")
-   `options`: [`CookieAttributes`](https://github.com/theodorejb/es-cookie/blob/master/src/CookieAttributes.ts) (defaults to: `{path:"/", httpOnly: true}`)

#### `.session.storage`

That object is only applied when `session.type == 'redis'`

-   `url`: string (process.env.REDIS_URL)
-   `options`: [`RedisOptions`](https://github.com/luin/ioredis/blob/main/lib/redis/RedisOptions.ts) (defaults to: `{ keyPrefix: 'em-light'}`),
