import { useAuthClient, useAuthContext } from '../utils'

export default defineEventHandler(async (event) => {
    const client = await useAuthClient()
    const auth = await useAuthContext(event)
    const {
        public: { baseUrl }
    } = useRuntimeConfig()

    let redirectTo = baseUrl

    if (auth.isAuthenticated) {
        const idTokenHint = await auth.logout()

        redirectTo = client.endSessionUrl({
            post_logout_redirect_uri: baseUrl,
            id_token_hint: idTokenHint
        })
    }

    return sendRedirect(event, redirectTo)
})
