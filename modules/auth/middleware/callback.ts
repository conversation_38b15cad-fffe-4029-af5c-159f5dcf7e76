import { useAuthClient, useAuthContext, useAuthReferer } from '../utils'

export default defineEventHandler(async (event) => {
    const client = await useAuthClient()
    const auth = await useAuthContext(event)
    const referer = await useAuthReferer(event)

    const {
        public: { baseUrl }
    } = useRuntimeConfig()

    const callback = `${baseUrl}/callback`
    const params = client.callbackParams(event.node.req)
    const checks = {}
    const extras = {}

    await auth.login(await client.callback(callback, params, checks, extras))

    const path = await referer.get()
    return sendRedirect(event, `${baseUrl}${path}`)
})
