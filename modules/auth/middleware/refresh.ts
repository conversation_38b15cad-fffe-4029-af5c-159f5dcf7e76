import { useAuthClient, useAuthContext } from '../utils'

export default defineEventHandler(async (event) => {
    const auth = await useAuthContext(event)

    if (auth.isAuthenticated && auth.isExpired) {
        const client = await useAuthClient()

        const extras = {}

        try {
            await auth.login(await client.refresh(auth.refreshToken, extras))
        } catch (error) {
            await auth.logout()
        }
    }
})
