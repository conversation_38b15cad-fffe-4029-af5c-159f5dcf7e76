import { useAuthClient, useAuthContext, useAuthReferer } from '../utils'

export default defineEventHandler(async (event) => {
    const client = await useAuthClient()
    const auth = await useAuthContext(event)
    const referer = await useAuthReferer(event)

    const {
        public: { baseUrl }
    } = useRuntimeConfig()

    await referer.set(
        event.req.headers.referer.replace(baseUrl, "")
    )

    const redirectTo = !auth.isAuthenticated
        ? client.authorizationUrl({
            redirect_uri: `${baseUrl}/callback`
        })
        : baseUrl

    return sendRedirect(event, redirectTo)
})
