import type { H3Event } from 'h3'
import { TokenSet } from 'openid-client'
import useAuthSession from './auth-session'

type AuthSession = Awaited<ReturnType<typeof useAuthSession>>

class AuthContext {
    private constructor(private session: AuthSession) {}

    get isAuthenticated() {
        return !!this.session.value?.claims()
    }

    get isExpired() {
        return !!this.session.value?.expired()
    }

    get refreshToken() {
        return this.session.value?.refresh_token
    }

    get accessToken() {
        return this.session.value?.access_token
    }

    async login(tokenSet: TokenSet) {
        await this.session.set(tokenSet)
    }

    async logout() {
        return await this.session.destroy()
    }

    public static async create(event: H3Event) {
        return new AuthContext(await useAuthSession(event))
    }
}

export const useAuthContext = async function (event: H3Event): Promise<AuthContext> {
    return (event.context.auth ||= await AuthContext.create(event))
}

export default useAuthContext
