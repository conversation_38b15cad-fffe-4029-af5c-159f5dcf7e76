import { Issuer } from 'openid-client'
import memoize from 'p-memoize'

const config = useRuntimeConfig()

const create = async function () {
    const issuer = await Issuer.discover(`${config.keycloak.url}/realms/${config.keycloak.realm}`)

    return new issuer.Client({
        client_id: config.keycloak.clientId,
        client_secret: config.keycloak.clientSecret
    })
}

const memoizedCreate = memoize(create)

export const useAuthClient = function () {
    return memoizedCreate()
}

export default useAuthClient
