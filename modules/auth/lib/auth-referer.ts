
import { randomBytes } from 'crypto'
import type { H3Event } from 'h3'
import { getCookie } from 'h3'

export type TAuthReferer = {
  get: () => Promise<string>,
  set: (referer: string) => Promise<void>,
}

export const useAuthReferer = async function (event: H3Event): Promise<TAuthReferer> {

  const config = useRuntimeConfig()
  const { cookie: { name, options } } = config.authSession
  let sessionId = getCookie(event, name)
  if (!sessionId) {
    sessionId = randomBytes(16).toString('hex')
    setCookie(event, name, sessionId, options)
  }
  const refererKey = `login_referer:${sessionId}`

  return {
    get: async (): Promise<string> => {
      const referer = await useStorage("redis").getItem(refererKey)
      await useStorage("redis").removeItem(refererKey)
      return (typeof referer === 'string' ? referer : '/')
    },
    set: async (referer: string) => {
      const url = new URL(referer, config.public.baseUrl)
      if (url.origin !== new URL(config.public.baseUrl).origin) {
        throw new Error('Invalid referer URL')
      }
      await useStorage("redis").setItem(refererKey, referer)
    }
  }
}

export default useAuthReferer
