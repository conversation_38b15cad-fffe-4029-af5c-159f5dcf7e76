import memoize from 'p-memoize'
import type { TokenSetParameters } from 'openid-client'

type SessionStorageEnvironment = {
    REDIS_URL?: string
    REDIS_KEY_PREFIX?: string
    REDIS_USERNAME?: string
    REDIS_PASSWORD?: string
    REDIS_SENTINELS?: string
    REDIS_SENTINEL_NAME?: string
    REDIS_SENTINEL_USERNAME?: string
    REDIS_SENTINEL_PASSWORD?: string
    REDIS_NODES?: string
}

type SessionStorageOptions = ReturnType<typeof useRuntimeConfig>['authSession']

/**
 *
 * This is used within nuxt.config to build a valid redis configuration from .env variables
 *
 * @param env
 */
export function buildSessionStorageOptionsFromEnvironment(env: SessionStorageEnvironment): SessionStorageOptions {
    if (env['REDIS_NODES']) {
        return {
            type: 'cluster',
            storage: {
                nodes: env['REDIS_NODES'].split(' '),
                options: {
                    keyPrefix: env['REDIS_KEY_PREFIX'],
                    redisOptions: {
                        username: env['REDIS_USERNAME'] ?? null,
                        password: env['REDIS_PASSWORD'] ?? null
                    }
                }
            }
        }
    }

    if (env['REDIS_SENTINELS']) {
        return {
            type: 'sentinel',
            storage: {
                options: {
                    sentinels: env['REDIS_SENTINELS']
                        .split(' ')
                        .filter((v) => !!v)
                        .map((node) => {
                            const [host, port] = node.split(':')
                            return {
                                host,
                                port: parseInt(port)
                            }
                        }),
                    name: env['REDIS_SENTINEL_NAME'] ?? null,
                    sentinelUsername: env['REDIS_SENTINEL_USERNAME'] ?? null,
                    sentinelPassword: env['REDIS_SENTINEL_PASSWORD'] ?? null,
                    keyPrefix: env['REDIS_KEY_PREFIX'],
                    username: env['REDIS_USERNAME'] ?? null,
                    password: env['REDIS_PASSWORD'] ?? null
                }
            }
        }
    }

    if (env['REDIS_URL']) {
        return {
            type: 'redis',
            storage: {
                url: env['REDIS_URL'],
                options: {
                    keyPrefix: env['REDIS_KEY_PREFIX'] ?? null,
                    username: env['REDIS_USERNAME'] ?? null,
                    password: env['REDIS_PASSWORD'] ?? null
                }
            }
        }
    }

    throw new Error('No valid configuration for Redis found')
}

export interface SessionStore {
    get(sessionId: string): Promise<TokenSetParameters>
    set(sessionId: string, value: TokenSetParameters): Promise<void>
    delete(sessionId: string): Promise<void>
}

const memoizedCreate = memoize(async function create() {
    return (await import('./session-stores/redis')).default
})

export const useSessionStore = function (...args: Parameters<typeof memoizedCreate>) {
    return memoizedCreate(...args)
}

export default useSessionStore
