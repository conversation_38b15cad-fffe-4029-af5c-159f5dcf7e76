import type { TokenSetParameters } from 'openid-client'
import Redis from 'ioredis'
import { SessionStore } from '../session-store'

function create() {
    const { authSession } = useRuntimeConfig()

    const { type, storage } = authSession

    switch (type) {
        case 'redis': {
            return new Redis(storage.url, storage.options)
        }

        case 'cluster': {
            return new Redis.Cluster(storage.nodes, storage.options)
        }

        case 'sentinel': {
            return new Redis(storage.options)
        }

        default: {
            throw new Error('Trying to initialize redis with wrong type paramter')
        }
    }
}

class RedisStore implements SessionStore {
    constructor(private client = create()) {}

    async get(sessionId: string) {
        const stringifiedToken = await this.client.get(sessionId)
        if (stringifiedToken) {
            return JSON.parse(stringifiedToken)
        }
    }

    async set(sessionId: string, value: TokenSetParameters) {
        await this.client.set(sessionId, JSON.stringify(value))
    }

    async delete(sessionId: string) {
        await this.client.del(sessionId)
    }
}

export default new RedisStore()
