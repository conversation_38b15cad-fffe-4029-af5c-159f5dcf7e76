import type { H3Event } from 'h3'
import type { TokenSetParameters } from 'openid-client'
import type { SessionStore } from './session-store'

import { randomBytes } from 'crypto'
import { TokenSet } from 'openid-client'
import { getCookie, setCookie } from 'h3'
import { useSessionStore } from './session-store'

const config = useRuntimeConfig()

class AuthSession {
    value?: TokenSet

    /**
     *
     * @param id
     * @param store
     * @param initial
     */
    private constructor(private readonly id: string, private readonly store: SessionStore, initial?: TokenSetParameters) {
        this.value = !!initial ? new TokenSet(initial) : undefined
    }

    async reload() {
        const parameters = await this.store.get(this.id)
        this.value = !!parameters ? new TokenSet(parameters) : undefined
    }

    async set(tokenSet: TokenSet) {
        this.value = tokenSet
        await this.store.set(this.id, Object.assign({}, tokenSet))
    }

    async destroy(): Promise<TokenSet['id_token']> {
        const idToken = this.value?.id_token
        this.value = undefined
        await this.store.delete(this.id)

        return idToken
    }

    public static async create(id: string, store: SessionStore) {
        const initial = await store.get(id)
        return new AuthSession(id, store, initial)
    }
}

export const useAuthSession = async function (event: H3Event): Promise<AuthSession> {
    const {
        cookie: { name, options }
    } = config.authSession

    const cookie = getCookie(event, name)
    const id = cookie || randomBytes(16).toString('hex')

    const storage = await useSessionStore()

    if (!cookie) {
        setCookie(event, name, id, options)
    }

    return await AuthSession.create(id, storage)
}

export default useAuthSession
