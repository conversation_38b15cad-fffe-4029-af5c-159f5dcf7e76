import { H3Error } from 'h3'

export const dataIsBlob = (data: H3Error | Blob): data is Blob => (data as Blob).text !== undefined

export const h3ResponseBlobToBase64 = async (data: Promise<H3Error | Blob>) => {
    const possibleBlob = await data

    if (dataIsBlob(possibleBlob)) {
        const arrayBuffer = await possibleBlob.arrayBuffer()
        return Buffer.from(arrayBuffer).toString('base64')
    }

    return possibleBlob
}
