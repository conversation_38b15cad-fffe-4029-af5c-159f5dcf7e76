import { FetchError, type FetchOptions, ofetch } from 'ofetch'
import { createError, getCookie, type H3Error, type H3Event, isError, sendError } from 'h3'
import defu from 'defu'

import { useAuthContext } from '#openid'

/**
 * If no cookies are set for Device-ID and -Name, they are retrieved from the API.
 *
 * @param event
 */
export const useClientDetection = async function (event: H3Event) {
    const {
        api: { clientName: defaultClientName }
    } = useRuntimeConfig()

    const clientIdFromCookie = getCookie(event, 'X-Client-Detection')
    const clientNameFromCookie = getCookie(event, 'X-Client-Name')

    if (!clientIdFromCookie) {
        const { accessToken } = await useAuthContext(event)

        const {
            public: { portalUrl }
        } = useRuntimeConfig()

        const headers = {
            'Authorization': `Bearer ${accessToken}`,
            'X-Forwarded-For': getHeader(event, 'X-Forwarded-For'),
            'User-Agent': getHeader(event, 'User-Agent')
        }

        const { clientIdentification, clientName } = await ofetch(`${portalUrl}/client-detection`, { headers })

        setCookie(event, 'X-Client-Detection', clientIdentification, { path: '/' })
        setCookie(event, 'X-Client-Name', clientName, { path: '/' })

        return {
            clientIdentification,
            clientName
        }
    } else {
        return {
            clientIdentification: clientIdFromCookie,
            clientName: clientNameFromCookie || defaultClientName
        }
    }
}

/**
 * Build all needed authorization headers
 *
 * @param event
 */
export async function useAuthHeaders(event: H3Event) {
    const { accessToken } = await useAuthContext(event)
    const { clientIdentification, clientName } = await useClientDetection(event)

    return {
        'Authorization': `Bearer ${accessToken}`,
        'X-Client-Identification': clientIdentification,
        'X-Client-Name': clientName
    }
}

type ResponseType = 'blob' | 'json'
/**
 * Build authorized functions in order to make requests to the external API
 *
 * @param event
 * @param defaultParams
 */
export const useApiProxy = async function (event: H3Event, defaultParams?: Record<string, any>) {
    const {
        api: { baseUrl: baseURL }
    } = useRuntimeConfig()

    /**
     * This is he reusable fetcher.
     *
     * @param path
     * @param options
     */
    const fetcher = async function <T extends any, R extends ResponseType = 'json'>(path: string, options: FetchOptions<R>) {
        const headers = await useAuthHeaders(event)

        const params = defu(defaultParams, {
            fetchPermissions: false
        })

        const fetchOptions = defu(options, {
            method: 'get' as const,
            baseURL,
            headers,
            params
        })

        return ofetch<T, R>(path, fetchOptions).catch((error: FetchError) => {
            console.log('error', error)
            sendError(event, error)
        })
    }

    const nativeFetcher = async function (path: string, options: RequestInit) {
        const headers = await useAuthHeaders(event)

        const params = defu(defaultParams, {
            fetchPermissions: false
        })

        const fetchOptions = defu(options, {
            method: 'get' as const,
            headers,
            params
        })

        return ofetch.native(`${baseURL}/${path}`, fetchOptions)
    }

    return {
        /**
         * Performs an authorized GET request
         *
         * @param path
         * @param params
         */
        get: function <T extends any, R extends ResponseType = 'json'>(path: string, params?: Record<string, any>) {
            return fetcher<T, R>(path, { params })
        },

        /**
         * Performs an authorized POST request
         *
         * @param path
         * @param body
         * @param params
         * @param headers
         */
        post: async function <T extends any, R extends ResponseType = 'json'>(
            path: string,
            body?: Record<string, any>,
            params?: Record<string, any>,
            headers?: Record<string, any>
        ) {
            return fetcher<T, R>(path, { method: 'POST', body, params, headers })
        },

        /**
         * Performs an authorized PATCH request
         *
         * @param path
         * @param body
         * @param params
         * @param headers
         */
        patch: function <T extends any = any, R extends ResponseType = 'json'>(
            path: string,
            body?: Record<string, any>,
            params?: Record<string, any>,
            headers?: Record<string, any>
        ) {
            return fetcher<T, R>(path, { method: 'PATCH', body, params, headers })
        },

        /**
         * Performs an authorized PUT request
         *
         * @param path
         * @param body
         * @param params
         * @param headers
         */
        put: function <T extends any = any, R extends ResponseType = 'json'>(
            path: string,
            body?: Record<string, any>,
            params?: Record<string, any>,
            headers?: Record<string, any>
        ) {
            return fetcher<T, R>(path, { method: 'PUT', body, params, headers })
        },

        /**
         * Performs an authorized DELETE request
         *
         * @param path
         * @param params
         */
        remove: async function <T extends any = any, R extends ResponseType = 'json'>(path: string, params?: Record<string, any>) {
            return fetcher<T, R>(path, { method: 'DELETE', params })
        },

        /**
         * Sometimes our fetch abstraction does not match the signature of the backend API. That's why we also provide the fetcher function.
         *
         * Example POST /code-entries expects an array as body parameter but our post function can just handle objects.
         */
        rawFetcher: fetcher,

        /**
         * Wrapper around js FetchAPI
         */
        nativeFetcher
    }
}

export default useApiProxy
