import __repository__, { type ModelNames, type ModelFromRepository } from './__repository__'
import { type ApiModelConstructor } from './model'

/**
 *
 * @param options
 */
export function toDate(options?: { default?: Date }) {
    return (value: string) => (!!value ? new Date(value) : null)
}

/**
 * The order of signatures is important since parameter detection (e.g. Parameters<T>) on overloaded functions is not possible
 * We want to have the search-by-name variant the default
 */
export function toModel<T extends {}>(constructor: ApiModelConstructor<T>): (data: object) => T
export function toModel<T extends ModelNames>(name: T): (data: object) => ModelFromRepository<T>
export function toModel<T extends {}>(constructorOrName: ApiModelConstructor<T> | string) {
    return (data: any) => {
        const ModelConstructor = typeof constructorOrName === 'string' ? __repository__[constructorOrName] : constructorOrName

        return !!data ? ModelConstructor.fromJson(data) : null
    }
}

/**
 * The order of signatures is important since parameter detection (e.g. Parameters<T>) on overloaded functions is not possible
 * We want to have the search-by-name variant the default
 */
export function toModels<T extends {}>(constructor: ApiModelConstructor<T>): (data: Array<object>) => Array<T>
export function toModels<T extends ModelNames>(name: T): (data: Array<object>) => Array<ModelFromRepository<T>>
export function toModels<T extends {}>(constructorOrName: ApiModelConstructor<T> | string) {
    return (data: Array<object>) => {
        const ModelConstructor = typeof constructorOrName === 'string' ? __repository__[constructorOrName] : constructorOrName

        return !!data ? ModelConstructor.fromJson(data) : null
    }
}
