import { type Model, type ModelProps } from './model'

import * as __helpers__ from './helpers'
import __repository__, { type ModelFromRepository, type ModelNames } from './__repository__'

type Constructor<T extends any = {}> = new (...args: any) => T

type Helpers = keyof typeof __helpers__

type Transformations<T extends Model = Model> = { [Property in keyof T]?: Transformator }
type Transformator = (value: any) => any
type TransformatorBuilder<T extends (...args: any[]) => Transformator> = T
type TransformationCallback<T extends Model> = (helpers: { [Key in Helpers]: TransformatorBuilder<(typeof __helpers__)[Key]> }) => Transformations<T>

/**
 * This is the global storage of transformations
 */
const storedTransformators = new Map<Constructor, Transformations>()

export const recieveTransformations = function <T extends Model>(Model: Constructor<T>): Transformations<T> {
    return storedTransformators.get(Model) || {}
}

export const recievePropertyNames = function <T extends Model>(Model: Constructor<T>) {
    return Object.keys(new Model()) as [keyof T]
}

/**
 * This is the global storage of serializable properties
 */
const storedSerializableProperties = new Map<Constructor, Set<string>>()

export const registerSerializableProperty = function <T extends Model>(Model: Constructor<T>, prop: string) {
    const store = storedSerializableProperties
    const properties = store.get(Model) || new Set()

    store.set(Model, properties.add(prop))
}

export const receiveSerializableProperties = function <T extends Model>(Model: Constructor<T>) {
    const properties = storedSerializableProperties.get(Model) || new Set()

    // These properties are always fillable
    ;['id', 'permissions'].forEach((prop) => properties.add(prop))

    return properties
}
/**
 * Example 1
 *
 * registerTransformations(Event, {
 *   dateUpTo: (value?: string) => new Date(value),
 * })
 */

/**
 * Example 2
 *
 * registerTransformations(Event, ({ toDate }) => ({
 *   dateUpTo: toDate,
 * }))
 */
export function registerTransformations<T extends Model>(ModelConstructor: Constructor<T>, transformations: Transformations<T>): void
export function registerTransformations<T extends Model>(ModelConstructor: Constructor<T>, transformations: TransformationCallback<T>): void
export function registerTransformations<T extends Model>(
    ModelConstructor: Constructor<T>,
    transformations: Transformations<T> | TransformationCallback<T>
) {
    if (typeof transformations === 'function') {
        transformations = transformations(__helpers__)
    }

    storedTransformators.set(ModelConstructor, Object.assign(recieveTransformations(ModelConstructor), transformations))
}

export function useModelFactory<T extends ModelNames>(name: T) {
    return __repository__[name]
}

declare global {
    type ApiModel<T extends ModelNames> = ModelFromRepository<T>

    type ApiModelProps<T extends ModelNames> = ModelProps<ApiModel<T>>

    type ApiModels<T extends ModelNames> = ApiModel<T>[]
}
