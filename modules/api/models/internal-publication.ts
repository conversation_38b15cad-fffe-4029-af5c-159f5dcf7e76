import { defineModel, serializable, castings as castTo } from '../model'

/**
 * Internal Publiation
 *
 * – https://gitlab.drkserver.org/drkserver/documentation/api-documentation/-/blob/master/drkserver.api.openapi.final.yaml
 */
class InternalPublication {

    @serializable()
    id: number

    @serializable()
    remark: string

    @castTo.date()
    @serializable()
    recievingDate: Date

    @serializable()
    @castTo.model('CodeEntry')
    statusAtDrk: ApiModel<'CodeEntry'>[]

    @serializable()
    @castTo.model('CodeEntry')
    typesOfMembership: ApiModel<'CodeEntry'>[]

    @serializable()
    @castTo.model('CodeEntry')
    membershipEntries: ApiModel<'CodeEntry'>[]

    @serializable()
    @castTo.model('CodeEntry')
    functions: ApiModel<'CodeEntry'>[]

    @serializable()
    @castTo.model('CodeEntry')
    membershipGroups: ApiModel<'CodeEntry'>[]

    @serializable()
    @castTo.model('CodeEntry')
    membershipGroupDescriptions: ApiModel<'CodeEntry'>[]

    @serializable()
    @castTo.model('CodeEntry')
    operations: ApiModel<'CodeEntry'>[]

    @serializable()
    @castTo.model('CodeEntry')
    operationDescriptions: ApiModel<'CodeEntry'>[]

    @serializable()
    @castTo.model('CodeEntry')
    operationQualifications: ApiModel<'CodeEntry'>[]
}

export default defineModel(InternalPublication)
