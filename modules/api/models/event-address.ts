import { defineModel, serializable, castings as castTo } from '../model'

/**
 * Event address as it's used for event locations
 *
 * - https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/events/EventLocation.yaml
 */
class EventAdress {
    @castTo.model('AddressContact')
    @serializable()
    addressContact: ApiModel<'AddressContact'>

    @castTo.model('AddressLocation')
    @serializable()
    addressValue: ApiModel<'AddressLocation'>

    @serializable()
    contacts: any

    @serializable()
    freeAddressText: string

    @serializable()
    sortOrder: number

    @serializable()
    remark: string

    /**
     * Return structured location data from ether addressValue or addressContact
     */
    get location() {
        return this.addressValue || this.addressContact?.location
    }
}

export default defineModel(EventAdress)
