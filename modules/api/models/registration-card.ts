import { Gender } from 'enums/gender'
import { defineModel, serializable, castings as castTo } from '../model'

class RegistrationCard {
    @serializable()
    firstname: string

    @serializable()
    lastname: string

    @serializable()
    telephone: string

    @serializable()
    mail: string

    @serializable()
    @castTo.date()
    birthday: Date | null = null

    @serializable()
    gender: Gender

    @serializable()
    zipCode: string

    @serializable()
    nationality: string

    @serializable()
    street: string

    @serializable()
    city: string

    @serializable()
    districtBranch: string

    @serializable()
    redCrossUnit: string

    @serializable()
    placeOfAction: string

    @serializable()
    disasterPreparednessUnit: string

    @serializable()
    @castTo.date()
    startOfAction: Date = null

    @serializable()
    @castTo.date()
    endOfAction: Date = null
}

export default defineModel(RegistrationCard)
