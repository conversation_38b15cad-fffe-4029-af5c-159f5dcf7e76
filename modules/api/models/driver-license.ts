import { defineModel, serializable, castings as castTo } from '../model'

class DriverLicense {
    @serializable()
    id: number

    @serializable()
    lastLicenseControl: string

    @serializable()
    nextLicenseControl: string

    // example: <PERSON>rasse
    @serializable()
    type: string

    @serializable()
    acquiredLicenseClasses: AcquiredLicenseClass[]

    @serializable()
    @castTo.model('CodeEntry')
    driversLicenseExtensionsAndRestrictions: ApiModel<'CodeEntry'>[]

    @serializable()
    @castTo.model('CodeEntry')
    qualifications: ApiModel<'CodeEntry'>[]

    @serializable()
    lastControl: any
}

export default defineModel(DriverLicense)

type AcquiredLicenseClass = {
    extensionsAndRestrictions: any
    id: number
    licenseClass: ApiModel<'CodeEntry'>
    validityFrom: string
    validityTo: string
}
