import { defineModel, serializable } from '../model'

class TechnicDependentType {
    @serializable()
    name: string

    @serializable()
    id: number

    @serializable()
    unitTypes: unitType
}

export default defineModel(TechnicDependentType)

type unitType = {
    name: string
    id: number
    unitCategories: unitCategoriesType
}[]

type unitCategoriesType = {
    name: string
    id: number
}[]
