import { castings, defineModel, serializable } from '../model'

class Apprenticeship {
    /**
     * ...
     */
    @castings.model('CodeEntry')
    @serializable()
    typeOfApprenticeship: ApiModel<'CodeEntry'> = null

    /**
     * ...
     */
    @castings.model('Organization')
    @serializable()
    organisation: ApiModel<'Organization'>

    /**
     * ...
     */
    @serializable()
    name: string

    /**
     * ...
     */
    shortname: string

    /**
     * ...
     */
    maxParticipants: number

    /**
     * We have no glue what it is, but it's needed in api requests
     */
    @serializable()
    hasTest: boolean

    /**
     * ...
     */
    tags: any[]
}

export default defineModel(Apprenticeship)
