import { defineModel, serializable, castings as castTo } from '../model'


/**
 * Communications
 *
 * @see https://gitlab.drkserver.org/drkserver/documentation/api-documentation/-/blob/master/models/members/CommunicationEntry.yaml
 */
class Communications {
    @castTo.model('CodeEntry')
    @serializable()
    communicationType: ApiModel<'CodeEntry'>

    @serializable()
    contact: string

    @serializable()
    priority: 1 | 2 | 3

    @serializable()
    useForDistributionLists: boolean

    @serializable()
    central: boolean

    @castTo.model('CodeEntry')
    @serializable()
    visibility: string

}

export default defineModel(Communications)
