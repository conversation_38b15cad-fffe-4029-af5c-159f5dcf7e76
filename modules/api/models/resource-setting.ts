import { defineModel, serializable, castings as castTo } from '../model'

class ResourceSetting {
    @serializable()
    id: number

    @serializable()
    @castTo.model('EventPost')
    eventPost: ApiModel<'EventPost'>

    @serializable()
    @castTo.model('SigningUp')
    eventSigningUp: ApiModel<'SigningUp'>

    @serializable()
    @castTo.collection('Period')
    periods: ApiModel<'Period'>[]
}

export default defineModel(ResourceSetting)
