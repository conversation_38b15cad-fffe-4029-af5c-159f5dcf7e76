import { defineModel, serializable, castings as castTo } from '../model'

class CodeEntry {
    /**
     * The id of code entry list
     *
     * @see enum/listType
     */
    @serializable()
    readonly listId: number

    /**
     * The string representation of the list type
     */
    @serializable()
    readonly listIdentifier: string

    /**
     * Most API ressources request that value to be set.
     */
    @serializable()
    readonly value1: string = ''

    /**
     * Describe me
     */
    @serializable()
    readonly value2: string

    /**
     * Describe me
     */
    @serializable()
    readonly value3: string

    /**
     * Describe me
     */
    @serializable()
    readonly value4: string

    /**
    * Describe me
    */
    @serializable()
    @castTo.model('Organization')
    organisation: ApiModel<'Organization'>

    /**
     * Provides an alias for the list id
     */
    get type() {
        return this.listId
    }
}

export default defineModel(CodeEntry)
