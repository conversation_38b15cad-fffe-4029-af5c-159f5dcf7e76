import { defineModel, serializable } from '../model'

class ExternalTechnicArticle {
    @serializable()
    name: string

    /**
     * TechnicArticleType
     */
    @serializable()
    type: {
        id: number
        name: string
    }

    /**
     * TechnicArticleUnitType
     */
    @serializable()
    unitType: {
        id: number
        name: string
    }

    /**
     * TechnicArticleUnitCategory
     */
    @serializable()
    unitCategory: {
        id: number
        name: string
    }

    @serializable()
    functional: boolean

    @serializable()
    availableFrom: string

    @serializable()
    availableTo: string

    @serializable()
    remark: string
}

export default defineModel(ExternalTechnicArticle)
