import EventSigningUpStatus from '~~/enums/event-signing-up-status'
import { defineModel, serializable, castings as castTo } from '../model'

/**
 * SigningUp is our representation of EventSigningUp
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/events/EventSigningUp.yaml
 */
class SigningUp {
    @serializable()
    @castTo.model('Resource')
    resource: ApiModel<'Resource'>

    /**
     * Response status of the helping person
     *
     * - { id: 25244; value2: "nicht verfügbar" }
     * - { id: 25239; value2: "verfügbar" }
     */
    @castTo.model('CodeEntry')
    @serializable()
    status: ApiModel<'CodeEntry'>

    /**
     * It looks as if this is a mirror if `status`;
     *
     * But we are using it to determine if a signing up can be removed from the event
     */
    @serializable()
    userStatus: any = null

    /**
     * Assigned to event
     *
     * Describes whether the person who planned the event has assigned the helper to this event.
     *
     * isReadOnly
     */
    @serializable()
    readonly isAssigned: boolean

    /**
     * Available in Periods
     *
     * The user has reported himself available for these periods.
     */
    @serializable()
    @castTo.collection('Period')
    periods: ApiModel<'Period'>[]

    /**
     * Remark
     *
     * Users can leave a note when they set the status.
     */
    @serializable()
    remark: string

    /**
     * Seems  to be not in use
     */
    // cancelingSigningUpRemark: string

    /**
     * Muss beim Senden gesetzt sein.
     * Wir nehmen die erste für den Nutzer mögliche Organisation (getSendingOrganisations)
     */
    @serializable()
    @castTo.model('Organization')
    sendingOrganisation: ApiModel<'Organization'>

    /**
     * dateOfResponse
     */
    @serializable()
    @castTo.date()
    dateOfResponse: Date

    /**
     * Available in Periods
     *
     * The user has reported himself available for these periods.
     */
    @serializable()
    @castTo.model('ResourceSetting')
    assignedResourceSettings: ApiModel<'ResourceSetting'>[]

    /**
     * Wird von uns ignoriert
     */
    // payingOrganisation: any

    /**
     * requiredNightQuarters
     */
    @serializable()
    requiredNightQuarters: boolean

}

export default defineModel(SigningUp)
