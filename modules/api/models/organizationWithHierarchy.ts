import { defineModel, serializable } from '~/modules/api/model'

class OrganizationWithHierarchy {
    @serializable()
    name: string

    @serializable()
    parent: number

    @serializable()
    parentInChainOfCommand: number

    @serializable()
    membersCanBeAdded: boolean

    @serializable()
    technicArticlesCanBeAdded: boolean

    @serializable()
    chainOfCommandEnabled: boolean

    @serializable()
    type: string

    @serializable()
    sortOrder: number

    @serializable()
    parents: { id: number; name: string }[]

    @serializable()
    parentsInChainOfCommand: { id: number; name: string }[]
}

export default defineModel(OrganizationWithHierarchy)
