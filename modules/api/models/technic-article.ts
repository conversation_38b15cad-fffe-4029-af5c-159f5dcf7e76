import { defineModel, serializable } from '../model'

class TechnicArticle {
    @serializable()
    name: string

    @serializable()
    id: number

    @serializable()
    number: string

    @serializable()
    type: any

    @serializable()
    unitType: any

    @serializable()
    unitCategory: any

    @serializable()
    identification: string

    @serializable()
    warehouse: any

    @serializable()
    avatar: string
}

export default defineModel(TechnicArticle)
