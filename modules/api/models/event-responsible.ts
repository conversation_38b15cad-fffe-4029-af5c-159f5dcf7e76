import { castings as castTo, defineModel, serializable } from '../model'

/**
 * Responsible member
 *
 * - https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/events/EventResponsible.yaml
 */
class EventResponsible {
    /**
     * Member data
     */
    @castTo.collection('MemberData')
    @serializable()
    masterdata: ApiModel<'MemberData'>

    /**
     * Communications
     */
    @castTo.collection('Communications')
    @serializable()
    communications: ApiModel<'Communications'>[]
}

export default defineModel(EventResponsible)
