import { defineModel, serializable, castings as castTo } from '../model'

/**
 * Membership
 *
 * – https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/members/Membership.yaml
 */
class Membership {
    /**
     * Leading File
     *
     * Guess: This membership describes the leading organisation for this member
     */
    @serializable()
    leadingFile: boolean

    /**
     * The Organisation
     */
    @castTo.model('Organization')
    @serializable()
    organisation: ApiModel<'Organization'>
}

export default defineModel(Membership)
