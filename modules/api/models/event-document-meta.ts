import { defineModel, serializable, castings as castTo } from '../model'

/**
 * Document model
 *
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/address/AddressLocation.yaml
 */
class EventDocumentMeta {
    @castTo.model('CodeEntry')
    @serializable()
    type: ApiModel<'CodeEntry'>

    @castTo.model('CodeEntry')
    @serializable()
    permission: ApiModel<'CodeEntry'>

    @serializable()
    fileName: string

    @serializable()
    fileNameOriginal: string

    @serializable()
    description: string
}

export default defineModel(EventDocumentMeta)
