import EventOperationType from '~~/enums/event-operation-type'
import EventStatusType from '~~/enums/event-status-type'
import { defineModel, serializable, castings as castTo } from '../model'

/**
 * Event
 *
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/events/ExtendedEvent.yaml
 */
class Event {
    /**
     * Describe me
     */
    @castTo.model('Organization')
    @serializable()
    organisation: ApiModel<'Organization'> = null

    /**
     * Organizer
     */
    @castTo.model('ComplexAddressContact')
    @serializable()
    organizer: ApiModel<'ComplexAddressContact'>

    /**
     * Describe me
     */
    @castTo.date()
    @serializable()
    dateFrom: Date = null

    /**
     * Describe me
     */
    @castTo.date()
    @serializable()
    dateUpTo: Date = null

    /**
     * Describe me
     */
    @castTo.model('CodeEntry')
    @serializable()
    category: ApiModel<'CodeEntry'>

    /**
     * Describe me
     */
    @castTo.collection('CodeEntry')
    @serializable()
    caterings: ApiModel<'CodeEntry'>[]

    /**
     * Describe me
     */
    @castTo.collection('CodeEntry')
    @serializable()
    dressCodes: ApiModel<'CodeEntry'>[]

    /**
     * Describe me
     */
    @castTo.model('Apprenticeship')
    @serializable()
    apprenticeshipType: ApiModel<'Apprenticeship'>

    /**
     * WAITING_FOR_APPROVAL, APPROVED, CANCELED, FINISHED
     */
    @serializable()
    status: EventStatusType

    /**
     * Describe me
     */
    @castTo.model('CodeEntry')
    @serializable()
    description: ApiModel<'CodeEntry'>

    /**
     * Describe me
     */
    @castTo.collection('CodeEntry')
    @serializable()
    eventTags: ApiModel<'CodeEntry'>[]

    /**
     * Describe me
     */
    @serializable()
    hasEventTags: boolean

    /**
     * Describe me
     */
    @serializable()
    extendedDescription: string = null

    /**
     * Describe me
     */
    @castTo.model('Operation')
    @serializable()
    operation: ApiModel<'Operation'>

    /**
     * Describe me
     */
    @serializable()
    remark: string = null

    /**
     * Kennzeichen
     */
    @serializable()
    number: string = null

    /**
     * Übernachtung verfügbar
     */
    @serializable()
    hasNightQuarters = false

    @serializable()
    requiredFemaleQuarters: number = null

    @serializable()
    requiredMaleQuarters: number = null

    @serializable()
    requiredDiverseQuarters: number = null

    @serializable()
    requiredNoinfoQuarters: number = null

    /**
     * Teilnahme kostenpflichtig
     */
    @serializable()
    withCosts = false

    /**
     * Kosten werden erstattet
     */
    @serializable()
    reimbursementOfCosts = false

    /**
     * shareRequests
     */
    @castTo.model('EventShareRequest')
    @serializable()
    shareRequests: ApiModel<'EventShareRequest'>[] | null

    /**
     * shareRequestsReceived
     */
    @castTo.model('EventShareRequest')
    @serializable()
    shareRequestsReceived: ApiModel<'EventShareRequest'>[] | null

    /**
     * True when event was shared by the user
     */
    @serializable()
    shared: boolean

    /**
     * True when other event was shared with the user
     */
    @serializable()
    shareRequestReceived: boolean

    /**
     * Describe me
     */
    @serializable()
    internalEventRemark: string = null

    /**
     * Describe me
     */
    @serializable()
    publicEventRemark: string = null

    /**
     * Describe me
     */
    @castTo.model('EventPost')
    @serializable()
    eventPosts: ApiModel<'EventPost'>[]

    /**
     * Describe me
     */
    @serializable()
    meetingpoint: string = null

    /**
     * Describe me
     */
    @castTo.date()
    @serializable()
    meetingpointTime: Date = null

    /**
     * The event posts statistic is also available when EVENT_POSTS is _not_ set for FIELD
     */
    @serializable()
    eventPostStatistics: Record<'mandatoryPersonal' | 'optionalPersonal', { desired: number; current: number }>

    /**
     * Describe me
     */
    @serializable()
    reasonOfCancellation: ApiModel<'ReasonOfCancellation'> | null

    /**
     * Describe me
     */
    @serializable()
    remarkOfCancellation: string = null

    /**
     * My Signing Up and My Assignment Status
     *
     * The property describes whether (and how) the user signed up for the event to be available.
     *
     * If the property is empty, it means that the user has not given any feedback yet.
     */
    @castTo.model('SigningUp')
    @serializable()
    mySigningUp: ApiModel<'SigningUp'>

    /**
     *  AllSigningUps is our representation of EventAllSigningUps
     */
    @castTo.model('AllSigningUps')
    @serializable()
    allSigningUps: ApiModel<'AllSigningUps'>

    /**
     * Locations of the events that are listed during event location assignment
     */
    @castTo.collection('EventAddress')
    @serializable()
    locations: ApiModel<'EventAddress'>[]

    /**
     * Documents attached to the event
     */
    @castTo.collection('EventDocumentMeta')
    @serializable()
    documents: ApiModel<'EventDocumentMeta'>[]

    /**
     * People who are responsible for event
     */
    @castTo.collection('EventResponsible')
    @serializable()
    responsibles: ApiModel<'EventResponsible'>[]

    /**
     * This is what the current user is allowed to perform on this event
     */
    @serializable()
    detailedPermission?: Record<'canEdit' | 'canClose' | 'canOpen' | 'canRegister', boolean>

    /**
     * Dynamic getter for retrieving the type of this object
     *
     * This is Art/Typ such as "Ausbildung oder Dienste"
     */
    get type(): EventOperationType {
        return this.category?.value1 as EventOperationType
    }
}

export default defineModel(Event)
