import { defineModel, serializable, castings as castTo } from '../model'

class CalendarEvent {
    @serializable()
    id: number

    @serializable()
    partId: string

    @serializable()
    title: string

    @serializable()
    titleForWeekEvent: string

    @serializable()
    extendedDescription: string

    @castTo.date()
    @serializable()
    dateFrom: Date = null

    @castTo.date()
    @serializable()
    dateUpTo: Date = null

    @castTo.date()
    @serializable()
    partDateFrom: Date = null

    @castTo.date()
    @serializable()
    partDateUpTo: Date = null

    @serializable()
    monthViewRow: number

    @serializable()
    weekViewRow: number

    @serializable()
    slotColumn: number

    @serializable()
    totalColumnsInSlot: number

    @serializable()
    timeSlotKey: string

    @serializable()
    isMultiDay: boolean
}

export default defineModel(CalendarEvent)
