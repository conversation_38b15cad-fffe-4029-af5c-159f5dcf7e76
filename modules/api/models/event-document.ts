import { defineModel, serializable } from '../model'

/**
 * Document model
 *
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/address/AddressLocation.yaml
 */
class EventDocument {
    //@castTo.model('EventDocumentMeta')
    @serializable()
    eventDocument: ApiModel<'EventDocumentMeta'>

    @serializable()
    data: any
}

export default defineModel(EventDocument)
