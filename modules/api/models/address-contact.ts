import { defineModel, serializable, castings as castTo } from '../model'

/**
 * This class represents a contact within the DRK context
 *
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/address/AddressContact.yaml
 */
class AddressContact {
    /**
     * Category
     */
    @castTo.model('CodeEntry')
    @serializable()
    category: ApiModel<'CodeEntry'>

    /**
     * Type
     */
    @castTo.model('CodeEntry')
    @serializable()
    type: ApiModel<'CodeEntry'>

    /**
     * Display name variant
     *
     * example: Deutsches Rotes Kreuz
     */
    @serializable()
    name1: string

    /**
     * Display name variant
     *
     * example: Landesverband Westfalen-Lippe e.V.
     */
    @serializable()
    name2: string

    /**
     * Display name variant
     *
     * example: Landesgeschäftsstelle
     */
    @serializable()
    name3: string

    /**
     * The organization this address belongs to
     */
    @serializable()
    organisation: ApiModel<'Organization'>

    /**
     * Location
     */
    @castTo.model('AddressLocation')
    @serializable()
    location: ApiModel<'AddressLocation'>

    /**
     * Avatar
     */
    @serializable()
    avatar: string


    /**
     * The display name combines name1 to name3
     */
    get displayName() {
        return [this.name1, this.name2, this.name3].filter((name) => !!name).join(', ')
    }
}

export default defineModel(AddressContact)
