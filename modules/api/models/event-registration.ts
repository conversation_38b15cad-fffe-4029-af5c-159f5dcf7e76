import { defineModel, serializable, castings as castTo } from '../model'

class EventRegistration {
    @serializable()
    @castTo.model('Resource')
    resource: ApiModel<'Resource'>

    @serializable()
    id: number

    @serializable()
    @castTo.model('RegistrationCard')
    registrationCard: ApiModel<'RegistrationCard'>

    @serializable()
    @castTo.date()
    start: Date = null

    @serializable()
    @castTo.date()
    end: Date = null

    @serializable()
    permissions: any
}

export default defineModel(EventRegistration)
