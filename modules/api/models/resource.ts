import { defineModel, serializable, castings as castTo } from '../model'

class Resource {
    @serializable()
    type: 'TECHNIC' | 'PERSONAL'

    @serializable()
    external: boolean

    @serializable()
    @castTo.model('MemberData')
    member: ApiModel<'MemberData'>

    @serializable()
    @castTo.model('TechnicArticle')
    article: ApiModel<'TechnicArticle'>

    @serializable()
    @castTo.model('ExternalPerson')
    externalPerson: ApiModel<'ExternalPerson'>

    @serializable()
    externalRoom: any

    @serializable()
    @castTo.model('ExternalTechnicArticle')
    externalArticle: ApiModel<'ExternalTechnicArticle'>

    @serializable()
    avatar: string
}

export default defineModel(Resource)
