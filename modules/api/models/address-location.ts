import { defineModel, serializable } from '../model'

/**
 * AddressLocation
 *
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/address/AddressLocation.yaml
 */
class AddressLocation {
    @serializable()
    zipCode: string

    @serializable()
    city: string

    @serializable()
    cityDistrict: string

    @serializable()
    country: string

    @serializable()
    street: string

    @serializable()
    streetnumber: string

    @serializable()
    suffix: string

    /**
     * Return a display name that can be uses in select lists
     *
     * Example: Leipzig Connewitz, Meusdorfer Straße
     */
    get displayName() {
        return [this.city, this.cityDistrict, this.street].filter((part) => !!part).join(' ')
    }
}

export default defineModel(AddressLocation)
