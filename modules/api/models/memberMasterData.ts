import { defineModel, serializable, castings as castTo } from '../model'

/**
 * MemberMasterdataFull
 *
 * – https://gitlab.drkserver.org/drkserver/documentation/api-documentation/-/blob/master/models/members/MemberMasterdataFull.yaml
 */
class Masterdata {
    @castTo.model('MemberData')
    @serializable()
    basicData: ApiModel<'MemberData'>

    @serializable()
    apprenticeships: Object

    @serializable()
    availabilities: Object

    @castTo.model('Communications')
    @serializable()
    communications: ApiModel<'Communications'>[]

    @serializable()
    contacts: Object

    @castTo.model('DriverLicense')
    @serializable()
    driverLicenses: ApiModel<'DriverLicense'>[]

    @serializable()
    examinationProofs: Object

    @serializable()
    foreignLanguages: Object

    @serializable()
    honours: Object

    @castTo.model('Membership')
    @serializable()
    memberships: ApiModel<'Membership'>

    @serializable()
    operationAvailabilities: Object

    @serializable()
    operationFormations: object

    @castTo.model('OperationQualification')
    @serializable()
    operationQualifications: ApiModel<'OperationQualification'>[]

    @serializable()
    optionals: Object

    @serializable()
    teachingLicenses: Object

}

export default defineModel(Masterdata)
