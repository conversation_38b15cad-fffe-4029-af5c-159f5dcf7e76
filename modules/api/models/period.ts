import { defineModel, serializable, castings as castTo } from '../model'

/**
 * Period
 *
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/general/Period.yaml
 */
class Period {
    /**
     * Start time
     *
     */
    @castTo.date()
    @serializable()
    start: Date

    /**
     * End time
     *
     */
    @castTo.date()
    @serializable()
    end: Date
}

export default defineModel(Period)
