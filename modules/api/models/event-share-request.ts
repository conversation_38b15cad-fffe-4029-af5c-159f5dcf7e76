import { castings as castTo, defineModel, serializable } from '../model'
import Community from '~~/enums/community'
import EventShareRequestStatus from '~~/enums/event-share-request-status'

class EventShareRequests {
    @serializable()
    id: number

    @serializable()
    permissions: any

    @serializable()
    @castTo.model('OrganizationWithParent')
    targetOrganisation: ApiModel<'OrganizationWithParent'>

    @serializable()
    targetGemeinschaft: Community

    @serializable()
    lastShared: Date

    @serializable()
    objectHash: string

    @serializable()
    status: EventShareRequestStatus
}

export default defineModel(EventShareRequests)
