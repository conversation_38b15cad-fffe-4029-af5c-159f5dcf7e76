import { defineModel, serializable, castings as castTo } from '../model'

/**
 * This class represents complex address contacts
 *
 * @see https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/address/ComplexAddressContact.yaml
 */
class ComplexAddressContact {
    @castTo.model('AddressContact')
    @serializable()
    addressContact: ApiModel<'AddressContact'>

    @castTo.model('AddressLocation')
    @serializable()
    addressLocation: ApiModel<'AddressLocation'>

    @castTo.model('MemberData')
    @serializable()
    member: ApiModel<'MemberData'>

    @serializable()
    custom: string

    @castTo.collection('ComplexAddressContact')
    subContact: ComplexAddressContact[]

    /**
     * Get type of this object
     */
    get type() {
        if (!!this.addressContact) {
            return 'contact'
        }

        if (!!this.addressLocation) {
            return 'location'
        }

        if (!!this.member) {
            return 'member'
        }

        if (!!this.custom) {
            return 'custom'
        }

        return null
    }

    get isAddressContact() {
        return this.type === 'contact'
    }

    get isLocationContact() {
        return this.type === 'location'
    }

    get isMemberContact() {
        return this.type === 'member'
    }

    get isCustomContact() {
        return this.type === 'custom'
    }

    get isEmptyContact() {
        if (this.isCustomContact) {
            return this.custom.length === 0
        }

        // When no type is set: then this is an empty object
        return !this.type
    }
}

export default defineModel(ComplexAddressContact)
