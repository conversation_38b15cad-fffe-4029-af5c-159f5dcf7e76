import { defineModel, serializable, castings as castTo } from '../model'

/**
 * Member Data
 *
 * Combines MemberMasterdataShort and MemberMasterdata
 *
 * – https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/members/MemberMasterdataShort.yaml
 * – @https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/members/MemberMasterdata.yaml
 */
class MemberData {
    /**
     * First name
     *
     * example: Max
     */
    @serializable()
    firstname: string

    /**
     * Last name
     *
     * example: Musterfrau
     */
    @serializable()
    lastname: string

    /**
     * Personnel number
     *
     * example: 2015010803
     */
    @serializable()
    personnelNumber: string

    /**
     * Leading organisation
     */
    @castTo.model('Organization')
    @serializable()
    leadingOrganisation: ApiModel<'Organization'>

    /**
     * Prefix
     *
     * example: Dr.
     */
    @serializable()
    prefix: string

    /**
     * Suffix
     *
     * example: B. A.
     */
    @serializable()
    suffix: string

    /**
     * Additional first name
     *
     * Example: U. or Ulf
     */
    @serializable()
    additionalFirstname: string

    /**
     * Birthname
     *
     * example: Mustermann
     */
    @serializable()
    birthname: string

    /**
     * Place of birth
     *
     * Example: Köln
     */
    @serializable()
    placeOfBirth: string

    /**
     * Gender
     *
     * - M: male
     * - W: female
     * - D: divers
     * - UNDEFINED
     */
    @serializable()
    gender: 'M' | 'W' | 'D' | 'UNDEFINED'

    /**
     * Nationality
     *
     * https://gitlab.drkserver.org/drkserver/api-documentation/-/blob/master/models/members/MemberAttribute.yaml
     */
    @serializable()
    nationality: Object

    /**
     * Letter salutation
     */
    @castTo.model('CodeEntry')
    @serializable()
    letterSalution: ApiModel<'CodeEntry'>

    /**
     * Salutation
     */
    @castTo.model('CodeEntry')
    @serializable()
    salution: ApiModel<'CodeEntry'>

    /**
     * Address
     */
    @castTo.model('AddressLocation')
    @serializable()
    address: ApiModel<'AddressLocation'>

    /**
     * Birthday
     */
    @castTo.date()
    @serializable()
    birthday: Date

    /**
     * Age
     */
    @serializable()
    age: number

    /**
     * Entry date
     */
    @castTo.date()
    @serializable()
    entryDate: Date

    /**
     * Avatar
     */
    @serializable()
    avatar: string

    /**
     * This user is part of this organisations
     *
     */
    @castTo.collection('Membership')
    @serializable()
    memberships: ApiModels<'Membership'>
}

export default defineModel(MemberData)
