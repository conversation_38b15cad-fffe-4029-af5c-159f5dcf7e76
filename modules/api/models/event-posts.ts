import { defineModel, serializable, castings as castTo } from '../model'

class EventPost {
    @serializable()
    id: number

    @serializable()
    permissions: any

    @serializable()
    parentEventPost: any

    @serializable()
    eventPostAssignment: any

    @serializable()
    isOccupiedByExternalStaff: boolean

    @serializable()
    remarkOfExternal: any

    @serializable()
    description: string

    @castTo.date()
    @serializable()
    dateFrom: Date

    @castTo.date()
    @serializable()
    dateUpTo: Date

    @serializable()
    eventPostResourceType: 'TECHNIC' | 'PERSONAL'

    @serializable()
    calculationOfCosts: true

    @serializable()
    required: boolean

    @serializable()
    extendedPermission: boolean

    @serializable()
    remark: any

    @serializable()
    desiredValue = 1

    @serializable()
    currentValue = 0

    @serializable()
    operationAddressLocation: any

    @serializable()
    eventLocation: any

    @serializable()
    addressFreeText: any

    @serializable()
    sortOrder: number

    @serializable()
    age: number

    @serializable()
    driverLicenseCriteriaList: number[] = []

    @serializable()
    qualifications: number[] = []

    @serializable()
    technicModules: number[] = []

    /**
     * Modul?
     */
    @serializable()
    technicArticleTypes: number[] = []

    /**
     * @todo: Check: This does not exist, doesn't it?
     */
    @serializable()
    technicArticleCategories: number[] = []

    /**
     * Art?
     */
    @serializable()
    technicArticleUnitTypes: number[] = []

    /**
     * Typ?
     */
    @serializable()
    technicArticleUnitCategories: number[] = []

    @serializable()
    capacity: any

    @serializable()
    isAccessible: any

    @serializable()
    criterias: []
}

export default defineModel(EventPost)
