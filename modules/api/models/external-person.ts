import { defineModel, serializable, castings as castTo } from '../model'

class ExternalPerson {
    @serializable()
    firstname: string

    @serializable()
    lastname: string

    @serializable()
    personnelNumber: string

    @serializable()
    telephone: string

    @serializable()
    mail: string

    @serializable()
    @castTo.date()
    availableFrom?: Date

    @serializable()
    @castTo.date()
    availableTo?: Date

    @serializable()
    driverLicenses: string

    @serializable()
    remark?: string

    @serializable()
    @castTo.collection('OperationQualification')
    operationQualifications?: ApiModel<'OperationQualification'>[] = []

    /**
     * A getter for retrieving this persons name as a string
     */
    get fullName() {
        return [this.lastname, this.firstname].join(', ')
    }
}

export default defineModel(ExternalPerson)

export type OperationQualifications = {
    id: number
    kind: ApiModel<'CodeEntry'>
    qualifications: ApiModel<'CodeEntry'>[]
}
