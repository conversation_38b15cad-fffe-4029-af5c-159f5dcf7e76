import { toDate, toModel, toModels } from './helpers'
import { receiveSerializableProperties, recieveTransformations, registerSerializableProperty, registerTransformations } from './factory'

import { uniqueId } from 'lodash-es'

export type Model = {
    id: number

    _uuid?: string

    permissions?: Record<string, any>

    toJSON(...args: any[]): any
}

export function date(...args: Parameters<typeof toDate>): PropertyDecorator {
    return function (target: any, prop: string) {
        registerTransformations(target.constructor, {
            [prop]: toDate(...args)
        })
    }
}

export function model(...args: Parameters<typeof toModel>): PropertyDecorator {
    return function (target: any, prop: string) {
        registerTransformations(target.constructor, {
            [prop]: toModel(...args)
        })
    }
}

export function collection(...args: Parameters<typeof toModels>): PropertyDecorator {
    return function (target: any, prop: string) {
        registerTransformations(target.constructor, {
            [prop]: toModels(...args)
        })
    }
}

export function serializable(): PropertyDecorator {
    return function (target: any, prop: string) {
        registerSerializableProperty(target.constructor, prop)
    }
}

export const castings = {
    date,
    model,
    collection
}

export const decorators = {
    serializable
}

/**
 * Apply the base model via "Mixin Pattern"
 *
 * @param Base
 */
export function defineModel<TBase extends new (...args: any[]) => {}, T = InstanceType<TBase>, TResult = ApiModel<T>>(Base: TBase) {
    class Model extends Base implements Model {
        /**
         * ...
         */
        readonly id: number

        /**
         * ...
         */
        readonly permissions?: {}

        /**
         * This is a fake uuid
         */
        _uuid?: string = null

        // set _uuid(value: string) {
        //     // wont do
        // }

        // forceUuid(value: string) {
        //     this._uuid = value
        // }

        /**
         *
         * @param args
         */
        constructor(...args: any[]) {
            super(...args)
        }

        /**
         * Create a Model and fill all given properties that are marked as "serializable"
         *
         * @param properties
         */
        static create(properties: Partial<TResult>): TResult {
            const instance = new this.prototype.constructor()

            Object.keys(properties)
                .filter((key) => this.serializables.has(key))
                .forEach((key) => {
                    instance[key] = properties[key]
                })

            if (instance.id) {
                instance._uuid = 'existing-' + instance.id
            } else {
                instance._uuid = uniqueId('created-')
            }

            return instance
        }

        /**
         * Transform incoming data to valid properties
         *
         * @param data JSON object
         */
        private static deserialize(data: Record<string, any>) {
            const transformators = this.transformations
            const keys = Object.keys(data)

            return keys.reduce((collection, key) => {
                const value = transformators.hasOwnProperty(key) ? transformators[key](data[key]) : data[key]

                // Don't overwrite default values
                const partial = value !== undefined ? { [key]: value } : {}

                return Object.assign(collection, partial)
            }, {})
        }

        /**
         * Build a collection of <T> Models
         *
         * @param data: Array
         */
        static fromJson(data: any[]): TResult[]
        /**
         * Build a <T> Models
         *
         * @param data: object
         */
        static fromJson(data: object): TResult
        /**
         *
         * Implementation
         */
        static fromJson(data: any[] | object) {
            if (Array.isArray(data)) {
                return data.map((itemData) => {
                    return this.create(this.deserialize(itemData))
                })
            } else {
                return this.create(this.deserialize(data))
            }
        }

        /**
         * Recieve transformations that were registered for the class that's being extended
         */
        static get transformations() {
            return recieveTransformations(super.prototype.constructor)
        }

        /**
         * Recieve properties that can be de/serialized
         */
        static get serializables() {
            return receiveSerializableProperties(super.prototype.constructor)
        }

        /**
         * This prevents the warning:
         * | Cannot stringify arbitrary non-POJOs Event
         * probabyl coming from `devalue` when state is stored for hydration
         */
        toJSON() {
            return { ...this }
        }
    }

    /**
     * Set class name by overwriting static class property
     */
    Object.defineProperty(Model, 'name', {
        value: Base.name,
        configurable: true
    })

    return Model
}

export type ApiModel<T extends {}> = Model & T

export type ApiModelConstructor<T extends {} = {}> = new (...args: any[]) => ApiModel<T>

export type ModelProps<M extends Model = Model> = Partial<{ [Property in keyof M]?: M[Property] }>
