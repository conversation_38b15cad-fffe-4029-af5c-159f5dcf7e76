import { addImports, createResolver, defineNuxtModule } from '@nuxt/kit'
import { createUnimport } from 'unimport'

export interface ModuleOptions {}

export default defineNuxtModule<ModuleOptions>({
    meta: {
        name: '@mogic/drk-api',
        configKey: 'drk-api'
    },

    defaults: {},

    async setup(options, nuxt) {
        const { resolve } = createResolver(import.meta.url)

        nuxt.options.alias = Object.assign(nuxt.options.alias, {
            '#models': resolve('./models/index')
        })

        nuxt.options.alias = Object.assign(nuxt.options.alias, {
            '#api-utils': resolve('./server/utils/index')
        })

        const { getImports } = createUnimport({
            imports: [{ name: 'useModelFactory', from: '#models' }]
        })

        addImports(await getImports())
    }
})
