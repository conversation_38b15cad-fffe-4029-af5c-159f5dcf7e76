<template>
    <component :is="icon" />
</template>

<script lang="ts" setup>
    // import { UiIconName } from '../icon-name'

    import { UiIconName } from '../types'

    // import type { FunctionalComponent } from 'vue'
    // import type * as icons from '#icons/index'

    // type Kebab<T extends string, A extends string = ''> = T extends `${infer F}${infer R}`
    //     ? Kebab<R, `${A}${F extends Lowercase<F> ? '' : '-'}${Lowercase<F>}`>
    //     : A

    // type IconName<T = typeof icons> = {
    //     [K in keyof T]: T[K] extends FunctionalComponent ? (K extends string ? Kebab<Uncapitalize<K>> : never) : never
    // }[keyof T]

    type Props = {
        name: UiIconName
        type?: number
    }

    const props = defineProps<Props>()

    const icon = computed(() => {
        return resolveComponent(`icon-${props.name}`)
    })
</script>
