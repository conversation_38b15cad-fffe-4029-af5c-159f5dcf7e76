import type { FunctionalComponent } from 'vue'
import type * as icons from '#icons/index'

type <PERSON><PERSON>b<T extends string, A extends string = ''> = T extends `${infer F}${infer R}`
    ? Kebab<R, `${A}${F extends Lowercase<F> ? '' : '-'}${Lowercase<F>}`>
    : A

export type UiIconName<T = typeof icons> = {
    [K in keyof T]: T[K] extends FunctionalComponent ? (K extends string ? Kebab<Uncapitalize<K>> : never) : never
}[keyof T]
