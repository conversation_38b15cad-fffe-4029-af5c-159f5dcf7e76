import { addComponent, addComponentsDir, createResolver, defineNuxtModule } from '@nuxt/kit'

import { generateIconComponents } from './build.js'

export * from './types'

export interface ModuleOptions {}

export default defineNuxtModule<ModuleOptions>({
    meta: {
        name: '@mogic/drk-ui',
        configKey: 'drk-ui'
    },

    defaults: {},

    async setup(options, nuxt) {
        const { resolve } = createResolver(import.meta.url)

        const iconsPath = resolve('./icons/')
        const iconComponentsPath = resolve('./generated/icons/')

        nuxt.hook('ready', async () => {
            console.log('Generate app icon in hook "ready"')
            return await generateIconComponents(iconsPath, iconComponentsPath)
        })

        nuxt.options.alias = Object.assign(nuxt.options.alias, {
            '#icons': iconComponentsPath
        })

        await addComponentsDir({
            path: iconComponentsPath,
            prefix: 'Icon',
            prefetch: true,
            preload: false,
            global: true,
            ignore: ['index.js']
        })

        await addComponent({
            name: 'UiIcon',
            filePath: resolve('./components/icon.vue')
        })
    }
})
