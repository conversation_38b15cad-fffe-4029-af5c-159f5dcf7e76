// For reference take a look at the original file:
// https://github.com/tailwindlabs/tailwindcss-forms/blob/master/src/index.js
// noinspection NpmUsedModulesInstalled

const tailwindPlugin = require('tailwindcss/plugin')
const defaultTheme = require('tailwindcss/defaultTheme')
const { icons } = require('./icons-plugin')

const plugin = tailwindPlugin(function ({ addUtilities, addComponents, addBase, theme }) {
    const [baseFontSize, { lineHeight: baseLineHeight }] = defaultTheme.fontSize['base']
    const colors = theme('colors')

    const { spacing } = defaultTheme

    // const spacing = theme('spacing')
    // const colors = theme('colors')
    const borderWidth = theme('borderWidth')
    const borderRadius = theme('borderRadius')
    const fontSize = theme('fontSize')
    // const letterSpacing = theme('letterSpacing')

    const resetCss = {
        'appearance': 'none',
        'background-color': colors.white,
        'print-color-adjust': 'exact',
        '--tw-shadow': '0 0 #0000'
    }

    const focusCss = {
        'outline': ' 2px solid transparent',
        'outline-color': 'transparent',
        'outline-style': 'solid',
        'outline-width': '2px',
        'outline-offset': '2px'
    }

    const borderCss = {
        'border-color': colors.grey[200],
        'border-width': borderWidth['DEFAULT'],
        'border-radius': '2px',
        '&:hover:not(:disabled):not(:focus):not(:focus-within)': {
            'border-color': colors.blue[300]
        },
        '&:focus, &:focus-within': {
            'border-color': colors.blue[500],
            ...focusCss
        }
    }

    const paddingCss = {
        'padding-top': spacing[2],
        'padding-right': spacing[3],
        'padding-bottom': spacing[2],
        'padding-left': spacing[3]
    }

    const placeholderCss = {
        color: colors.grey[500],
        opacity: 1
    }

    const typographyCss = {
        'font-size': baseFontSize,
        'font-weight': defaultTheme.fontWeight['light'],
        'line-height': baseLineHeight,
        'color': colors.grey[900]
    }

    addBase({
        // See https://github.com/tailwindlabs/tailwindcss-forms/issues/95
        [`${[
            '::-webkit-datetime-edit',
            '::-webkit-datetime-edit-year-field',
            '::-webkit-datetime-edit-month-field',
            '::-webkit-datetime-edit-day-field',
            '::-webkit-datetime-edit-hour-field',
            '::-webkit-datetime-edit-minute-field',
            '::-webkit-datetime-edit-second-field',
            '::-webkit-datetime-edit-millisecond-field',
            '::-webkit-datetime-edit-meridiem-field',
            '::-webkit-datetime-edit-fields-wrapper'
        ].join(', ')}`]: {
            'padding-top': 0,
            'padding-bottom': 0
        }
    })

    addUtilities({
        '.input-border': { ...borderCss },
        '.input-focus': { ...focusCss },
        '.input-typography': { ...typographyCss },
        '.input-placeholder': { ...placeholderCss }
    })

    addComponents({
        '.form-input, .form-textarea': {
            ...resetCss,
            ...borderCss,
            ...paddingCss,
            ...typographyCss,
            'text-align': 'left',
            'transition-property': defaultTheme.transitionProperty['colors'] + ', box-shadow',
            'transition-timing-function': defaultTheme.transitionTimingFunction['DEFAULT'],
            'transition-duration': defaultTheme.transitionDuration['DEFAULT'],
            '&:disabled': {
                ...placeholderCss,
                backgroundColor: colors.grey[50],
                opacity: 0.8
            },
            '&::placeholder': {
                ...placeholderCss
            },
            // Special behaviour for date fields. Display grey when invalid (trough required attribute)
            "&[type='date'], &[type='datetime-local']": {
                '&:invalid:not(:focus), &:disabled': {
                    ...placeholderCss
                }
            }
        },
        '.form-select': {
            ...resetCss,
            ...borderCss,
            ...paddingCss,
            ...typographyCss,
            'text-align': 'left',
            'background-image': `svg-load(${icons['chevron-down']}, fill=none, stroke=${colors['grey']['500']})`,
            'background-position': `right ${spacing['1.5']} center`,
            'background-repeat': `no-repeat`,
            'background-size': `${spacing[4]} ${spacing[4]}`,
            'padding-right': spacing[10],
            '&:disabled': {
                ...placeholderCss,
                backgroundColor: colors.grey[50],
                opacity: 0.8
            }
        },

        '.form-input-has-control': {
            'position': 'relative',
            'padding-right': spacing[10],

            "& > [role='button'], & ~ button, & > button, & ~ [role='button']": {
                position: 'absolute',
                display: 'flex',
                alignItems: 'center',
                top: 0,
                bottom: 0,
                right: spacing['1.5']
            },

            '& .form-input-control, &  ~ button .form-input-control': {
                'width': spacing['8'],
                'height': spacing['8'],
                'color': colors.grey[300],
                'transition-property': defaultTheme.transitionProperty['colors'],
                'transition-timing-function': defaultTheme.transitionTimingFunction['DEFAULT'],
                'transition-duration': defaultTheme.transitionDuration['DEFAULT'],
                '&:hover': {
                    color: colors.sky[500]
                }
            },

            '&:hover': {
                '& .form-input-control, &  ~ button .form-input-control': {
                    color: colors.sky[500]
                }
            },

            '&:focus-within': {
                "& .form-input-control ,& ~ button .form-input-control, & ~ [role='button'] .form-input-control": {
                    'backgroundColor': colors.grey[50],
                    'color': colors.sky[500],
                    '&:active': {
                        backgroundColor: colors.grey[50],
                        color: colors.sky[500]
                    }
                }
            }
        },

        '.form-input-control': {
            'padding': spacing[1],
            'border-radius': borderRadius['full'],
            'pointer-events': 'auto',
            '&:hover': {
                backgroundColor: colors.grey[100]
            },

            '&:active': {
                backgroundColor: colors.grey[200]
            }
        },

        '.form-checkbox': {
            ...resetCss,
            ...borderCss,
            'padding': '0',
            'display': 'inline-block',
            'vertical-align': 'middle',
            'background-origin': 'border-box',
            'background-size': `100% 100%`,
            'background-position': `center`,
            'background-repeat': `no-repeat`,
            'user-select': 'none',
            'flex-shrink': '0',
            'height': spacing[4],
            'width': spacing[4],
            'cursor': 'pointer',
            'border-color': colors.grey[400],

            '&:hover:not(:disabled):not(:focus):not(:focus-within)': {
                'border-color': colors.grey[500]
            },
            '&:focus, &:focus-within': {
                'border-color': colors.gray[500]
            },
            '&:checked': {
                backgroundImage: `svg-load(${icons['check']}, fill=none, stroke=${colors.softred[500]})`
            },
            '&:hover:not(:checked)': {
                backgroundImage: `none`,
                '@screen sm': {
                    // Add hover background for bigger screens
                    backgroundImage: `svg-load(${icons['check']}, fill=none, stroke=${colors.softred[200]})`,
                },
            }
        },

        '.form-radio': {
            ...borderCss,
            'border-radius': borderRadius['full'],
            'height': spacing[4],
            'width': spacing[4],
            'cursor': 'pointer',

            '&:checked': {
                'accent-color': `${colors.softred[500]}`
            },
            '&:hover:not(:checked)': {
                'accent-color': `${colors.softred[200]}`
            },
            '&:focus': {
                'outline': '1px solid transparent',
                'outline-offset': '2px',
                'border-color': colors.blue[500],
                'box-shadow': `0 0 0 2px ${colors.blue[500]}`
            }
        },

        '.form-button': {
            'font-size': `var(--form-button-font-size, ${fontSize['sm'][0]})`,
            'font-weight': defaultTheme.fontWeight['normal'],
            'line-height': `var(--form-button-leading, ${baseLineHeight})`,
            'letterSpacing': '0.4px',
            'padding-top': `var(--form-button-padding-y, ${spacing[2]})`,
            'padding-right': `var(--form-button-padding-x, ${spacing[4]})`,
            'padding-bottom': `var(--form-button-padding-y, ${spacing[2]})`,
            'padding-left': `var(--form-button-padding-x, ${spacing[4]})`,
            'color': `var(--form-button-text-color, ${colors.sky[500]})`,
            'background-color': 'var(--form-button-background-color, transparent)',
            'border-color': 'var(--form-button-border-color, transparent)',
            'border-width': '1px',
            'column-gap': spacing[2],
            'cursor': 'pointer',
            'display': 'flex',
            'align-items': 'center',
            'transition-property': defaultTheme.transitionProperty['colors'] + ', box-shadow',
            'transition-timing-function': defaultTheme.transitionTimingFunction['DEFAULT'],
            'transition-duration': defaultTheme.transitionDuration['DEFAULT'],
            '&:hover': {
                'color': `var(--form-button-text-color, ${colors.sky[500]})`,
                // This is intentionally blue[50] instead of sky[50]
                'background-color': `var(--form-button-background-color, ${colors.blue[50]})`,
                // This is intentionally blue[50] instead of sky[50]
                'border-color': `var(--form-button-border-color, ${colors.blue[50]})`
            },
            '&:active': {
                'color': `var(--form-button-text-color, ${colors.sky[500]})`,
                // This is intentionally blue[100] instead of sky[100]
                'background-color': `var(--form-button-background-color, ${colors.blue[100]})`,
                // This is intentionally blue[100] instead of sky[100]
                'border-color': `var(--form-button-border-color, ${colors.blue[100]})`
            },
            '&:disabled': {
                'opacity': 0.4,
                'pointer-events': 'none'
            }
        }
    })

    addUtilities({
        '.button-outline': {
            '--form-button-text-color': colors.sky[500],
            '--form-button-background-color': 'transparent',
            '--form-button-border-color': colors.sky[500],
            '&:hover': {
                '--form-button-text-color': colors.sky[600],
                '--form-button-background-color': 'transparent',
                '--form-button-border-color': colors.sky[600],
                'box-shadow': `inset 0px 0px 0px 1px ${colors.sky[600]}`
            },
            '&:active': {
                '--form-button-text-color': colors.sky[700],
                '--form-button-background-color': 'transparent',
                '--form-button-border-color': colors.sky[700],
                'box-shadow': `inset 0px 0px 0px 1px ${colors.sky[700]}`
            }
        },
        '.button-secondary': {
            '--form-button-text-color': colors.grey[700],
            '&:hover': {
                '--form-button-text-color': colors.grey[900],
                '--form-button-background-color': colors.grey[50],
                '--form-button-border-color': 'transparent'
            },
            '&:active': {
                '--form-button-text-color': colors.grey[900],
                '--form-button-background-color': colors.grey[50],
                '--form-button-border-color': 'transparent'
            }
        },
        '.button-contained': {
            '--form-button-text-color': colors.white,
            '--form-button-background-color': colors.sky[500],
            '--form-button-border-color': colors.sky[500],
            '&:hover': {
                '--form-button-text-color': colors.white,
                '--form-button-background-color': colors.sky[600],
                '--form-button-border-color': colors.sky[600]
            },
            '&:active': {
                '--form-button-text-color': colors.white,
                '--form-button-background-color': colors.sky[700],
                '--form-button-border-color': colors.sky[700]
            }
        },
        '.button-contained-secondary': {
            '--form-button-text-color': colors.grey[700],
            '--form-button-background-color': colors.grey[100],
            '--form-button-border-color': colors.grey[100],
            '&:hover': {
                '--form-button-text-color': colors.grey[900],
                '--form-button-background-color': colors.grey[200],
                '--form-button-border-color': colors.grey[200]
            },
            '&:active': {
                '--form-button-text-color': colors.grey[900],
                '--form-button-background-color': colors.grey[300],
                '--form-button-border-color': colors.grey[300]
            }
        }
    })

    addUtilities({
        '.button-xs': {
            '--form-button-font-size': fontSize['xs'][0],
            '--form-button-leading': '1rem',
            '--form-button-padding-y': spacing['0.5'],
            '--form-button-padding-x': spacing['2']
        },
        '.button-sm': {
            '--form-button-font-size': fontSize['sm'][0],
            '--form-button-leading': '1.5rem',
            '--form-button-padding-y': spacing[1],
            '--form-button-padding-x': spacing['2.5']
        },
        '.button-lg': {
            '--form-button-font-size': fontSize['base'][0],
            '--form-button-leading': '1.5rem',
            '--form-button-padding-y': spacing['2.5'],
            '--form-button-padding-x': spacing['4.5']
        },
        '.button-xl': {
            '--form-button-font-size': fontSize['base'][0],
            '--form-button-leading': '1.5rem',
            '--form-button-padding-y': spacing[3],
            '--form-button-padding-x': spacing['5']
        }
    })
})

module.exports = { plugin }
