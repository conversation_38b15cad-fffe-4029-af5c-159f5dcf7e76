// noinspection NpmUsedModulesInstalled

const tailwindPlugin = require('tailwindcss/plugin.js')
const { flatten, map } = require('lodash')
const flattenColorPalette = require('tailwindcss/lib/util/flattenColorPalette').default

const glob = require('glob')
const path = require('path')

const icons = glob.sync(__dirname + '/../icons/*.svg').reduce((accumulator, filePath) => {
    accumulator[path.basename(filePath, '.svg')] = path.resolve(filePath)
    return accumulator
}, {})

const plugin = tailwindPlugin(function ({ addUtilities, theme }) {
    const colors = flattenColorPalette(theme('colors'))

    const backgrounds = flatten(
        map(icons, (filePath, key) => {
            return map(colors, (colorValue, colorModifier) => {
                return {
                    [`.icon-${key}-${colorModifier}`]: {
                        backgroundImage: `svg-load(${filePath}, fill=none, stroke=${colorValue})`
                    }
                }
            })
        })
    )
    addUtilities(backgrounds)
})

module.exports = {
    plugin,
    icons
}
