const fs = require('fs').promises
const camelcase = require('camelcase')
const { promisify } = require('util')
const rimraf = promisify(require('rimraf'))
const { compile: compileVue } = require('@vue/compiler-dom')
const { dirname } = require('path')

let transform = (svg) => {
    let { code } = compileVue(svg, {
        mode: 'module'
    })

    return code.replace('export function', 'export default function')
}

async function getIcons(inDir) {
    let files = await fs.readdir(inDir)
    return Promise.all(
        files.map(async (file) => ({
            svg: await fs.readFile(`${inDir}/${file}`, 'utf8'),
            componentName: `${camelcase(file.replace(/\.svg$/, ''), {
                pascalCase: true
            })}`
        }))
    )
}

function exportAll(icons, includeExtension = true) {
    return icons
        .map(({ componentName }) => {
            let extension = includeExtension ? '.js' : ''
            return `export { default as ${componentName} } from './${componentName}${extension}'`
        })
        .join('\n')
}

async function ensureWrite(file, text) {
    await fs.mkdir(dirname(file), { recursive: true })
    await fs.writeFile(file, text, 'utf8')
}

async function ensureWriteJson(file, json) {
    await ensureWrite(file, JSON.stringify(json, null, 2))
}

async function buildIcons(inDir, outDir) {
    // let outDir = `./modules/ui/generated/icons`
    let icons = await getIcons(inDir)

    await Promise.all(
        icons.flatMap(async ({ componentName, svg }) => {
            let content = transform(svg)
            let types = `import type { FunctionalComponent, HTMLAttributes, VNodeProps } from 'vue';\ndeclare const ${componentName}: FunctionalComponent<HTMLAttributes & VNodeProps>;\nexport default ${componentName};\n`

            // noinspection ES6MissingAwait
            return [ensureWrite(`${outDir}/${componentName}.js`, content), ...(types ? [ensureWrite(`${outDir}/${componentName}.d.ts`, types)] : [])]
        })
    )
    await ensureWrite(`${outDir}/index.js`, exportAll(icons, false))

    await ensureWrite(`${outDir}/index.d.ts`, exportAll(icons, false))
}

export const generateIconComponents = async function (inputFolder, outputFolder) {
    await rimraf(outputFolder + '/*')
    await buildIcons(inputFolder, outputFolder)
    console.log(`Finished building icons package.`)
}
