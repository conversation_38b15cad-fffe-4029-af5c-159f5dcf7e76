<template>
    <div>
        <slot v-bind="{ name, organisation, avatar }">
            <div v-bind="$attrs" class="flex items-center leading-tight">
                <UiAvatar :name="name" :image="avatar" class="mr-[0.5em]" />
                <span>
                    {{ name }}<br />
                    <span v-text="organisation" class="text-gray-500" />
                </span>
            </div>
        </slot>
    </div>
</template>

<script lang="ts" setup>
    const props = defineProps<{
        member: ApiModel<'MemberData'> | ApiModel<'ExternalPerson'>
    }>()

    const { $fullName } = useNuxtApp()

    const name = computed(() => {
        // if member is deleted form the system then firstName and lastName properties are null
        // then we want to return deleted person
        const nameToCheck = $fullName(props.member)
        return nameToCheck.trim() !== "" ? nameToCheck : "Gelöschte Person"
    })

    const organisation = computed(() => {
        return props.member instanceof useModelFactory('MemberData') ?
            props.member.leadingOrganisation?.name :
            props.member ?
                'Extern Mitwirkende*r' :
                ''
    })

    const avatar = computed(() => {
        if(props.member instanceof useModelFactory('MemberData')){
            const { avatar } = props.member
            return !!avatar ? `data:image/png;base64, ${avatar}` : null
        }

        return null
    })
</script>
