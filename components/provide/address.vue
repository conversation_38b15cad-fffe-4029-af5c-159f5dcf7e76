<template>
    <div>
        <slot v-bind="{ name1, name2, name3, organisation, avatar }">
            <div v-bind="$attrs" class="flex items-center leading-tight text-sm">
                <UiAvatar v-if="avatar" :name="name1" :image="avatar" class="mr-[0.5em]" />
                <UiIcon v-if="!avatar" class="mr-[0.5em] h-6 w-6 min-w-[1.5rem]" name="address-book" />
                <span>
                    <p v-if="name1">{{name1}}</p>
                    <p v-if="name2">{{name2}}</p>
                    <p v-if="name3">{{name3}}</p>
                    <span v-text="organisation" class="text-gray-500" />
                </span>
            </div>
        </slot>
    </div>
</template>

<script lang="ts" setup>
    const props = defineProps<{
        address: ApiModel<'AddressContact'>
    }>()

    const name1 = computed(() => {
        return props.address.name1
    })

    const name2 = computed(() => {
        return props.address.name2
    })

    const name3 = computed(() => {
        return props.address.name3
    })
    const organisation = computed(() => {
        return props.address.organisation.name
    })

    const avatar = computed(() => {
        const { avatar } = props.address
        return !!avatar ? `data:image/png;base64, ${avatar}` : null
    })
</script>
