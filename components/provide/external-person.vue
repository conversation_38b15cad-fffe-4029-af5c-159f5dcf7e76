<template>
    <slot v-bind="{ name, organisation, telephone, email, driverLicenses, qualifications }">
        <div v-bind="$attrs" class="flex items-center leading-tight">
            <span>
                {{ name }}<br />
                <span v-text="organisation" class="text-gray-500" />
            </span>
        </div>
    </slot>
</template>

<script lang="ts" setup>
    const props = defineProps<{
        externalPerson: ApiModel<'ExternalPerson'>
    }>()

    const { $fullName } = useNuxtApp()

    const name = computed(() => {
        // if externalPerson is deleted form the system then firstName and lastName properties are null
        // then we want to return deleted person
        const nameToCheck = $fullName(props.externalPerson)
        return nameToCheck.trim() !== "" ? nameToCheck : "Gelöschte Person"
    })

    const organisation = ref<string>('Extern Mitwirkende*r')

    const telephone = ref<string>(props.externalPerson.telephone)

    const email = ref<string>(props.externalPerson.mail)

    const driverLicenses = ref<string>(props.externalPerson.driverLicenses)

    const qualifications = computed<string[]>(() => {
        // Gather all relevant values based on gender
        const allValues: string[] = (props.externalPerson.operationQualifications ?? []).flatMap(operation =>
            operation.qualifications.flatMap((qualification: ApiModel<'CodeEntry'>) => {
                if(qualification.value2 && qualification.value2.trim()) {
                    return [qualification.value2];
                }
                return [];
            })
        );

        // Remove duplicates and sort in ascending order
        const uniqueSortedValues = Array.from(new Set(allValues)).sort((a, b) => a.localeCompare(b));

        return uniqueSortedValues;
    });

</script>
