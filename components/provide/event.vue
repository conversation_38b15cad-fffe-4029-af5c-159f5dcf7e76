<template>
    <slot v-bind="{ title, icon, description, linkedAssigment }">
        <div v-bind="$attrs" class="flex items-center leading-tight">
            <span>
                {{ title }}<br />
                <span v-text="description" class="text-gray-500" />
            </span>
        </div>
    </slot>
</template>

<script lang="ts" setup>
    import { UiIconName } from '~~/modules/ui'

    enum EventType {
        Apprenticeship,
        Service
    }

    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const type = computed<EventType>(() => {
        return !!props.event.apprenticeshipType ? EventType.Apprenticeship : EventType.Service
    })

    const icon = computed<UiIconName>(() => {
        return type.value === EventType.Apprenticeship ? 'education' : 'truck-outlined'
    })

    const title = computed<string>(() => {
        return type.value === EventType.Apprenticeship ? props.event.apprenticeshipType.name : props.event.description?.value2
    })

    const description = computed<string>(() => {
        return props.event.extendedDescription
    })

    const linkedAssigment = computed<string>(()=>{
        return props.event.operation?.name
    })
</script>
