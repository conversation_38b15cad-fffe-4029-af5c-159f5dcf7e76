<template>
    <slot />

    <template v-if="dialog.editEvent.isRevealed.value">
        <EventEditDialog :controller="dialog.editEvent" :event="event" />
    </template>

    <template v-if="dialog.copyEvent.isRevealed.value">
        <EventCopyDialog :controller="dialog.copyEvent" :event="event" />
    </template>

    <template v-if="dialog.cancelEvent.isRevealed.value">
        <EventCancelDialog :controller="dialog.cancelEvent" />
    </template>

    <template v-if="dialog.closeEvent.isRevealed.value">
        <EventCloseDialog :controller="dialog.closeEvent" :event="event" />
    </template>

    <template v-if="dialog.createPrintableDocument.isRevealed.value">
        <EventPrintDialog :event="event" :controller="dialog.createPrintableDocument" />
    </template>

    <template v-if="dialog.createPrintableReport.isRevealed.value">
        <EventReportCreate :event="event" :controller="dialog.createPrintableReport" />
    </template>

    <template v-if="dialog.internalPublicationShowInvitations.isRevealed.value">
        <EventInternalPublicationShowInvitationsDialog :controller="dialog.internalPublicationShowInvitations" :event="event" />
    </template>

    <template v-if="dialog.internalPublicationInviteHelp.isRevealed.value">
        <EventInternalPublicationInviteHelpDialog :controller="dialog.internalPublicationInviteHelp" :publication="internalPublication" :event="event" />
    </template>

    <template v-if="dialog.viewResources.isRevealed.value">
        <EventResourcesComposer :controller="dialog.viewResources" :event="event" />
    </template>

    <UiConfirmDialog title="Ereignis erneut öffnen" :controller="dialog.openEvent" confirm-with="Erneut öffnen" cancel-with="Abbrechen">
        Du bist dabei dieses Ereignis erneut zu öffnen. Wenn du das machst, kannst du es wieder bearbeiten. Der drkserver entfernt die Dienstzeiten
        dieses Ereignisses aus den Personalakten aller Teilnehmenden. Denk daran, das Ereignis wieder abzuschließen, wenn du soweit bist.
    </UiConfirmDialog>

    <UiConfirmDialog
        title="Ereignis wieder aktivieren"
        :controller="dialog.reactivateEvent"
        confirm-with="Ereignis wieder aktivieren"
        cancel-with="Abbrechen">
        Du bist dabei, dieses Ereignis wieder zu aktivieren. Das Ereignis kann dann wieder bearbeitet werden.
    </UiConfirmDialog>

    <UiConfirmDialog title="Ereignis abschließen" :controller="dialog.canNotCloseEvent" :withoutConfirmation="true" cancel-with="Zurück">
        Du kannst diese Ausbildung nicht abschließen. Dir fehlt vermutlich das entsprechende Recht "Ereignisse vom Typ 'Ausbildung' abschließen" in
        deinen Benutzerrollen. Wende dich bei Bedarf an deine drkserver-Administratorin oder deinen drkserver-Administrator. Mehr zum Abschließen von
        Ereignissen siehst du
        <a
            href="https://www.drkserver.org/drkserver/funktionen/funktionen/ereignismanagement/basis-abschliessen.html"
            target="blank"
            rel="noopener noreferrer"
            class="text-blue-500 underline hover:text-blue-800">
            hier </a
        >.
    </UiConfirmDialog>

    <UiConfirmDialog title="Ereignis löschen" :controller="dialog.removeEvent" confirm-with="Löschen" cancel-with="Abbrechen">
        Du bist dabei, dieses Ereignis zu löschen. Es ist dann weg. Du kannst es nicht wieder herstellen.
    </UiConfirmDialog>

    <UiConfirmDialog title="Einladung löschen" :controller="dialog.removeInternalPublication" confirm-with="Löschen" cancel-with="Abbrechen">
        Du bist dabei, diese Karteikarte zu entfernen. Wer dieses Ereignis nur wegen <span class="underline">dieser</span> Einladung sieht, sieht es
        danach nicht mehr.
    </UiConfirmDialog>
</template>

<script lang="ts" setup>
    import { useRunWithProgressNotification } from '~/composables/notifications';
    import { provideEventActions } from '~~/composables/event-actions'
    import { differenceInMilliseconds, addMilliseconds } from 'date-fns'
    import { useQueryClient } from '@tanstack/vue-query';
    import { useCopyOperations } from '~~/composables/copy-operations'

    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const queryClient = useQueryClient()
    const commit = useCommitEventMutation(props.event)
    const runWithNotification = useRunWithNotification()
    const { schedule } = useNotifications()
    const runWithProgressNotification = useRunWithProgressNotification()
    const { createEvent } = useCreateEvent(true)
    const { startCopyOperation, completeCopyOperation } = useCopyOperations()

    const dialog = {
        editEvent: useDialogController('editEvent'),
        closeEvent: useDialogController('closeEvent'),
        openEvent: useDialogController('confirmation'),
        cancelEvent: useDialogController('cancelEvent'),
        reactivateEvent: useDialogController('confirmation'),
        removeEvent: useDialogController('confirmation'),
        internalPublicationInviteHelp: useDialogController('internalPublicationInviteHelp'),
        createPrintableDocument: useDialogController('confirmation'),
        internalPublicationShowInvitations: useDialogController('emptyDialog'),
        removeInternalPublication: useDialogController('confirmation'),
        canNotCloseEvent: useDialogController('confirmation'),
        viewResources: useDialogController('viewResources'),
        copyEvent: useDialogController('copyEvent'),
        createPrintableReport: useDialogController('createPrintableReport')
    }

    const route = useRoute()
    const router = useRouter()

    const internalPublication = ref<ApiModel<'InternalPublication'>>(null)

    provideEventActions({
        /**
         * Edit base data of an event
         */
        async editEvent() {
            const { isCanceled, data: event } = await dialog.editEvent.reveal()

            if (!isCanceled) {
                return runWithNotification(() => commit('updateBasicData', event), {
                    pending: 'Das Ereignis wird gespeichert.',
                    error: 'Das Ereignis konnte nicht gespeichert werden.'
                })
            }
        },

        async copyEvent() {
            const { data: copyEventData, isCanceled } = await dialog.copyEvent.reveal()

            const newEventsIdArray: number[] = [copyEventData.event.id]
            const commonTag = useModelFactory('CodeEntry').create({
                value2: 'SE-' + props.event.id
            })

            if(!isCanceled) {
                const operationId = startCopyOperation()

                const controllers: AbortController[] = []

                const cancelCopying = ref(false)
                const cancel = () => {
                    cancelCopying.value = true
                    controllers.forEach(c => c.abort())
                    completeCopyOperation(operationId)
                }

                const startCopyMessage = schedule()
                const cancelMessage = schedule()
                if(copyEventData.eventsToCopy.length > 1) {
                    startCopyMessage.show('Ereignis wird gerade mehrfach kopiert.', 'cancel', true)
                    startCopyMessage.setCallback(cancel)
                }

                watch(cancelCopying, ()=>{
                    startCopyMessage.hide()
                    const eventNumber = controllers.length
                    cancelMessage.show(`Du hast das Kopieren abgebrochen. Die Kopie mit der Nummer ${eventNumber} wird noch erstellt. Alle bereits kopierten Ereignisse bleiben bestehen.`, 'waiting', true)
                })

                    const { results } = await runWithProgressNotification(
                    copyEventData.eventsToCopy,
                    async (event: ApiModel<'Event'>, index) => {

                    if(cancelCopying.value) {
                        return null
                    }

                    const controller = new AbortController()
                    controllers.push(controller)


                    if(index === copyEventData.eventsToCopy.length-1) {
                        startCopyMessage.hide()
                    }

                    try{
                        const oldEventStartTime = copyEventData.event.dateFrom
                        const newEvent = await createEvent({ data: event.toJSON(), signal: controller.signal })
                        newEventsIdArray.push(newEvent.id)

                        try {
                            copyEventData.dresscodes.length > 0 ? await $fetch<object[]>(`/api/events/${newEvent.id}/dresscodes`, { body: copyEventData.dresscodes, method: 'PUT' }) : null
                            copyEventData.caterings.length > 0 ? await $fetch<object[]>(`/api/events/${newEvent.id}/caterings`, { body: copyEventData.caterings, method: 'PUT' }) : null
                            copyEventData.locations.length > 0 ? await $fetch<object>(`/api/events/${newEvent.id}/locations`, {body: copyEventData.locations[0], method: 'POST'}) : null
                            copyEventData.organizer ? await $fetch<object>(`/api/events/${newEvent.id}/organizer`, { body: copyEventData.organizer, method: 'PUT' }) : null

                            if (copyEventData.canCopyResponsiblePersons) {
                                for (const person of copyEventData.responsiblePersons) {
                                    await $fetch<object>(`/api/events/${newEvent.id}/responsibles`, {
                                        body: person,
                                        method: 'POST',
                                    })
                                }
                            }

                            // We need to calculate new meeting time according to new event start time
                            const differenceBetweenOldEventStartAndNewEventStart = differenceInMilliseconds(newEvent.dateFrom, oldEventStartTime)
                            const newMeetingTime = copyEventData.event.meetingpointTime ? addMilliseconds(copyEventData.event.meetingpointTime, differenceBetweenOldEventStartAndNewEventStart) : null

                            await $fetch<object>(`/api/events/${newEvent.id}`, {
                                body: {...newEvent, meetingpointTime: newMeetingTime},
                                method: 'PATCH'
                            })

                            if(copyEventData.canCopyInvitations) {
                                for (const publication of copyEventData.internalPublications) {
                                    await $fetch<ApiModel<'InternalPublication'>>(`/api/events/${newEvent.id}/internal-publication`, {
                                        body: publication,
                                        method: 'POST'
                                    })
                                }
                            }

                            // We need to calculate time periods for every new eventPost according to new event dates
                            const newEventPostArray = []

                            for (const eventPost of copyEventData.eventPost) {
                                const eventPostStartTime = eventPost.dateFrom
                                const eventPostEndTime = eventPost.dateUpTo
                                const eventPostDuration = differenceInMilliseconds(eventPostEndTime, eventPostStartTime)
                                const differenceBetweenEventStartAndEventPostStart = differenceInMilliseconds(eventPostStartTime, oldEventStartTime)
                                const newEventPostStartTime = addMilliseconds(newEvent.dateFrom, differenceBetweenEventStartAndEventPostStart)
                                const newEventPostEndTime = addMilliseconds(newEventPostStartTime, eventPostDuration)

                                if(copyEventData.canCopyEventPosts) {
                                    const newEventPost = await $fetch<object>(`/api/events/${newEvent.id}/event-posts`, {
                                        body: {
                                            ...eventPost,
                                            dateUpTo: newEventPostEndTime,
                                            dateFrom: newEventPostStartTime
                                        },
                                        method: 'POST'
                                    })
                                    newEventPostArray.push(newEventPost)
                                }
                            }

                            if(copyEventData.canCopyRegistrators) {
                                for (const registrator of copyEventData.registrators) {
                                    await $fetch<object>(`/api/events/${newEvent.id}/registrators`, {
                                        body: registrator,
                                        method: 'post'
                                    })
                                }
                            }
                        } catch (err: any) {
                            const error = new Error('Additional data requests error')
                            error.name = 'CopyAdditionalDataError'
                            throw error
                        }
                        return newEvent
                    } catch (err: any) {
                        if (err.name === 'AbortError') {
                            return null
                        }
                        throw err
                    }
                },
                {
                    progress: (done, total) => `Ereignis ${done} von ${total} wird gerade kopiert.`,
                    error: (done, total) => `Du wolltest das Ereignis ${total}-mal kopieren. Das hat nicht geklappt.`,
                    canceled: (done, total) => `Du hast das Kopieren abgebrochen: Ereignis ${done} von ${total} wurde nicht kopiert.`
                })

                if(copyEventData.eventsToCopy.length > 1) {
                    startCopyMessage.hide()
                    cancelMessage.hide()
                }

                if (results.length > 0) {
                    router.push({ name: 'events-id-details', params: { id: results[0].id } })
                }

                completeCopyOperation(operationId)

                queryClient.invalidateQueries({
                    queryKey: queries.events.list._def,
                    refetchType: 'active',
                    exact: false
                })

                for (const eventId of newEventsIdArray) {
                    await $fetch<ApiModel<'CodeEntry'>[]>(`/api/events/${eventId}/tags`, { body: commonTag, method: 'PUT' })
                }
            }
        },

         /**
         * Open view resources composer
         */
        async viewResources() {
            await dialog.viewResources.reveal()
        },

        /**
         * Cancel an event
         */
        async cancelEvent() {
            const { isCanceled, data: reason } = await dialog.cancelEvent.reveal()

            if (!isCanceled) {
                return runWithNotification(() => commit('cancelEvent', reason), {
                    pending: 'Das Ereignis wird abgesagt.',
                    success: 'Das hat geklappt: Du hast das Ereignis abgesagt.',
                    error: 'Du wolltest das Ereignis absagen. Das hat nicht geklappt.'
                })
            }
        },

        /**
         * Create internal publication
         */
        async internalPublicationInviteHelp(publication?: ApiModel<'InternalPublication'>) {
            internalPublication.value = publication

            const { data, isCanceled } = await dialog.internalPublicationInviteHelp.reveal()

            if (!isCanceled && !data.id) {
                return runWithNotification(() => commit('addInternalPublication', data), {
                    pending: 'Helfende werden eingeladen.',
                    success: 'Das hat geklappt: Du hast Helfende zu dem Ereignis eingeladen.',
                    error: 'Du wolltest Helfende zu dem Ereignis einladen. Das hat nicht geklappt.'
                })
            }

            if (!isCanceled && !!data.id) {
                return runWithNotification(() => commit('updateInternalPublication', data), {
                    pending: 'Helfende werden eingeladen.',
                    success: 'Das hat geklappt: Du hast Helfende zu dem Ereignis eingeladen.',
                    error: 'Du wolltest Helfende zu dem Ereignis einladen. Das hat nicht geklappt.'
                })
            }
        },

        /**
         * Show all internal publicationinvitations
         */
        async internalPublicationShowInvitations() {
            await dialog.internalPublicationShowInvitations.reveal()
        },

        async removeInternalPublication(publication: ApiModel<'InternalPublication'>) {
            const { isCanceled } = await dialog.removeInternalPublication.reveal()

            if (!isCanceled) {
                runWithNotification(() => commit('removeInternalPublication', publication), {
                    pending: `Die Einladung wird entfernt.`,
                    error: `Du wolltest eine Einladung zu dem Ereignis löschen. Das hat nicht geklappt.`,
                    success: `Das hat geklappt: Du hast eine Einladung zu dem Ereignis gelöscht.`
                })
            }
        },

        /**
         * Reactivate an event
         */
        async reactivateEvent() {
            const { isCanceled } = await dialog.reactivateEvent.reveal()

            if (!isCanceled) {
                return runWithNotification(() => commit('reactivateEvent'), {
                    pending: 'Ereignis wird wieder aktiviert.',
                    success: 'Das hat geklappt: Das Ereignis ist wieder aktiv.',
                    error: 'Beim Reaktivieren des Ereignisses ist ein Fehler aufgetreten.'
                })
            }
        },

        /**
         * Close an event
         */
        async closeEvent() {
            const { isCanceled, data: members } = await dialog.closeEvent.reveal()

            if (!isCanceled) {
                return runWithNotification(() => commit('closeEvent', members), {
                    pending: 'Das Ereignis wird abgeschlossen.',
                    success: `Das hat geklappt: Du hast das Ereignis abgeschlossen.`,
                    error: `Du wolltest das Ereignis abgeschließen. Das hat nicht geklappt.`
                })
            }
        },

        /**
         * Can not close event. canEdit == true && canClose == false
         */
        async canNotCloseEvent() {
            await dialog.canNotCloseEvent.reveal()
        },

        /**
         * Reopen an event
         */
        async openEvent() {
            const { isCanceled } = await dialog.openEvent.reveal()

            if (!isCanceled) {
                return runWithNotification(() => commit('openEvent'), {
                    pending: 'Das Ereignis wird erneut geöffnet.',
                    success: 'Das hat geklappt: Du hast das Ereignis erneut geöffnet.',
                    error: 'Du wolltest das Ereignis erneut öffnen. Das hat nicht geklappt.'
                })
            }
        },

        async removeEvent() {
            const { isCanceled } = await dialog.removeEvent.reveal()

            if (!isCanceled) {
                const result = runWithNotification(() => commit('removeEvent', props.event), {
                    pending: `Das Ereignis ${props.event.extendedDescription}" wird gelöscht.`,
                    success: `Das hat geklappt: Du hast das Ereignis "${props.event.extendedDescription}" gelöscht.`,
                    error: `Du wolltest das Ereignis "${props.event.extendedDescription}" löschen. Das hat nicht geklappt.`
                })

                if (result instanceof Error) {
                    return
                }

                const onDetailsPage = route.matched.findIndex(({ name }) => name === 'events-id') > -1

                if (onDetailsPage) {
                    router.replace({ name: 'events' })
                }
            }
        },

        /**
         * Download a printable PDF document
         *
         * @todo: Ask for type in dialog then fetch and download from here
         */
        async downloadPrintableDocument() {
            const { data, isCanceled } = await dialog.createPrintableDocument.reveal()
        },

        /**
         * Create and download a ICS file
         */
        async downloadCalendarEntry() {
            const { fetch, name } = useICalendar([props.event])
            const { trigger } = useDownloadButton(name, fetch)

            trigger()
        },

        /**
         * Download reportings
         */
        async downloadReports() {
            const { data, isCanceled } = await dialog.createPrintableReport.reveal()
        }
    })
</script>
