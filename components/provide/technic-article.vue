<template>
    <slot v-bind="{ icon, name, warehouse, avatar }">
        <div v-bind="$attrs" class="flex items-center leading-tight">
            <UiAvatar v-if="avatar"  :image="avatar" :name="name" class="mr-[0.5em]" />
            <UiIcon v-if="!avatar" class="mr-2 h-6 w-6 min-w-[1.5rem] bg-brack" :name="icon" />

            <span>
                {{ name }}<br />
                <span v-text="warehouse" class="text-gray-500" />
            </span>
        </div>
    </slot>
</template>

<script lang="ts" setup>
    import { UiIconName } from '~~/modules/ui'

    const props = defineProps<{
        technicArticle: ApiModel<'TechnicArticle'>
    }>()

    const { $fullName } = useNuxtApp()

    const { getIcon } = useSigningUpsIcons()

    const name = computed(() => {
        return props.technicArticle.identification
    })

    const icon = computed(()=> {
        return getIcon(props.technicArticle.type?.name)
    })

    const warehouse = computed(() => {
        return props.technicArticle.warehouse?.name
    })

    const avatar = computed(() => {
        const { avatar } = props.technicArticle
        return !!avatar ? `data:image/png;base64, ${avatar}` : null
    })
</script>
