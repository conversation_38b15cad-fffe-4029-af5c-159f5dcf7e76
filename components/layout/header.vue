<template>
    <nav class="border-grey-100 z-30 flex-none border-b bg-white pb-3 xl:pb-0">
        <div class="mx-auto max-w-7xl px-4  pt-2 xl:pt-0">
            <div class="flex flex-wrap lg:flex-nowrap xl:h-16 items-center">
                <NuxtLink to="/" class="flex mr-4 basis-full lg:basis-[unset] my-2 lg:my-0">
                    <img alt="drkserver - Millionen Potentiale" src="/logo-drkserver.svg" class="h-4 w-auto" />
                </NuxtLink>

                <div v-if="$isAuthenticated" class="flex flex-wrap gap-8 items-center w-full">
                    <template v-if="!pending">
                        <MainMenu class="hidden lg:flex xl:ml-16 "/>
                        <UserMenu class="flex " :user="user" />
                    </template>
                </div>
                <div v-else class="flex flex-1 justify-end">
                    <NuxtLink :to="`${baseUrl}/login`" :external="true">Login</NuxtLink>
                </div>
            </div>
        </div>
    </nav>
</template>

<script lang="ts" setup>
    const { data: user, pending } = await useFetch('/api/user', { server: false })

    const {
        public: { baseUrl }
    } = useRuntimeConfig()
</script>
