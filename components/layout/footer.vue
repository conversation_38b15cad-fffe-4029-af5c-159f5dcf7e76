<template>
    <footer class="flex-0 bg-grey-100 mx-auto w-full text-teal-300">
        <div class="text-2xs mx-auto max-w-7xl px-4 py-2 sm:px-6 lg:px-1">
            <div v-if="isSupported && memory" class="flex gap-4">
                <template v-if="memory">
                    <div>Used: {{ size(memory.usedJSHeapSize) }}</div>
                    <div>Allocated: {{ size(memory.totalJSHeapSize) }}</div>
                    <div>Limit: {{ size(memory.jsHeapSizeLimit) }}</div>
                </template>
            </div>
            <div v-else>Your browser does not support performance memory API</div>
        </div>
    </footer>
</template>

<script lang="ts" setup>
    // This component is not used, but can be left as is for easy debbuging in future
    import { useMemory } from '@vueuse/core'

    const size = (v: number) => {
        const kb = v / 1024 / 1024
        return `${kb.toFixed(2)} MB`
    }
    const { isSupported, memory } = useMemory()
</script>
