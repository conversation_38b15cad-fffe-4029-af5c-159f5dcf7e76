<template>
    <UiFormField :label="label">
        <input type="text" v-model="value" class="form-input w-full" :placeholder="placeholder" :list="dataListId" />
        <datalist :id="dataListId">
            <template v-if="value?.length > 0">
                <option v-for="entry in entries" :key="entry.id">{{ entry.value2 }}</option>
            </template>
        </datalist>
    </UiFormField>
</template>

<script setup lang="ts">
    import ListType from '~~/enums/listType'
    import { useVModel } from '@vueuse/core'
    import { uniqueId } from 'lodash-es'
    import { useCodeEntries } from '~~/queries/code-entries'

    const props = defineProps<{
        listType: ListType | keyof typeof ListType
        label: string
        modelValue?: string
        placeholder?: string
    }>()

    /**
     * We can initalize this component with an enum value or its key
     */
    const codeEntryType = (Number.isInteger(props.listType) ? props.listType : ListType[props.listType]) as ListType

    const emit = defineEmits<{
        (e: 'update:modelValue', value: string): void
    }>()

    const dataListId = uniqueId('data-list-element')

    const value = useVModel(props, 'modelValue', emit)

    const { data: entries } = useCodeEntries(codeEntryType)
</script>

<style scoped lang="pcss">
    input[type='text']::-webkit-calendar-picker-indicator {
        display: none !important;
    }
</style>
