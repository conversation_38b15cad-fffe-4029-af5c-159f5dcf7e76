<template>
    <div>
        <UiComboxInput v-if="isEntriesLoaded" v-model="value" :options="options" :label="label" displaySummary />
        <div v-else class="text-grey-900 mb-1 block text-xs leading-4">{{ label }} is Loading...</div>
    </div>
</template>

<script setup lang="ts">
    import { useVModel } from '@vueuse/core'
    import ListType from '~~/enums/listType'
    import { useCodeEntries } from '~~/queries/code-entries'

    type Props = {
        label?: string
        listType: ListType
        filter?: Function
        modelValue: ApiModel<'CodeEntry'>[] | number[]
    }

    type Emits = {
        (e: 'update:modelValue', value: ApiModel<'CodeEntry'>[]): void
    }

    const props = defineProps<Props>()

    const emit = defineEmits<Emits>()

    const selected = useVModel(props, 'modelValue', emit)

    const { data: entries } = useCodeEntries(props.listType)

    const isEntriesLoaded = computed(() => {
        return entries.value !== undefined
    })

    const options = computed(() => {
        let filteredOptions = props.filter ? props.filter(entries.value) : unref(entries.value || [])

        return (filteredOptions || []).map((entry: ApiModel<'CodeEntry'>) => ({
            id: entry.id,
            label: entry.value2
        }))
    })
    /**
     * Wrapper that transforms an option into a model value and vice versa
     */
    const value = computed<Option[]>({
        get: () => {
            const selectedIds: number[] =
                typeof props.modelValue[0] === 'number'
                    ? (props.modelValue as number[])
                    : (props.modelValue as ApiModel<'CodeEntry'>[]).map((v) => v.id)
            return options.value.filter((entry: ApiModel<'CodeEntry'>) => selectedIds.includes(entry.id))
        },
        set: (value) => {
            const selectedIds = new Set(value.map((v) => v.id))
            selected.value = (entries.value || []).filter((entry) => selectedIds.has(entry.id))
        }
    })
</script>
