<template>
    <div class="relative">
        <UiFormField :label="label">
            <Combobox v-model="value">
                <ComboboxInput @change="value = $event.target.value" class="form-input w-full" />
                <ComboboxOptions
                    :class="
                        filteredEntries.length > 0
                            ? 'z-[99] absolute mt-1 max-h-72 w-full overflow-auto rounded-md bg-white text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none'
                            : ''
                    ">
                    <ComboboxOption
                        v-for="entry in filteredEntries"
                        :key="entry.id"
                        :value="entry.value2"
                        class="listbox-option">
                        {{ entry.value2 }}
                    </ComboboxOption>
                </ComboboxOptions>
            </Combobox>
        </UiFormField>
    </div>
</template>

<script setup lang="ts">
    import ListType from '~~/enums/listType'
    import { useVModel } from '@vueuse/core'
    import { useCodeEntries } from '~~/queries/code-entries'

    import { Combobox, ComboboxInput, ComboboxOptions, ComboboxOption } from '@headlessui/vue'

    const props = defineProps<{
        listType: ListType | keyof typeof ListType
        label: string
        modelValue?: string
        placeholder?: string
    }>()

    /**
     * We can initalize this component with an enum value or its key
     */
    const codeEntryType = (Number.isInteger(props.listType) ? props.listType : ListType[props.listType]) as ListType

    const emit = defineEmits<{
        (e: 'update:modelValue', value: string): void
    }>()
    const query = ref('')
    const value = useVModel(props, 'modelValue', emit)

    const { data: entries } = useCodeEntries(codeEntryType)

    const filteredEntries = computed(() => {
        return value.value === '' ? entries.value : entries.value.filter((entry) => entry.value2.toLowerCase().includes(value.value.toLocaleLowerCase()))
    })
</script>


<style lang="pcss" scoped>
    .listbox-option {
        @apply text-grey-800 relative cursor-default select-none py-2 pl-3 pr-9 leading-tight font-light first-of-type:rounded-t-md last:rounded-b-md;
    }

    .listbox-option[data-headlessui-state~=active] {
        @apply bg-yellow-50
    }

    .listbox-option[data-headlessui-state~=selected] {
        @apply underline decoration-blue-500
    }
</style>

