<template>
    <UiListbox v-model="selectedId">
        <template #button>
            <span class="truncate" :class="{ italic: !modelValue }">{{ modelValue?.name ?? 'Bitte auswählen' }} </span>
        </template>

        <template #default>
            <UiListboxOption v-for="item in options" :key="item.id" :value="item.id">
                {{ item.name }}
            </UiListboxOption>
        </template>
    </UiListbox>

</template>

<script setup lang="ts">
    import { watchOnce } from '@vueuse/core'

    const modelValue = defineModel<ApiModel<'Organization'>>()

    const {
        $user: { organisationsWithPermissionToCreateEvents }
    } = useNuxtApp()

    /**
     * Wrapper that transforms an option into a model value and vice versa
     */
    const selectedId = computed({
        get: () => {
            return modelValue.value?.id || null
        },
        set: (value) => {
            modelValue.value = organisationsWithPermissionToCreateEvents.value.find((option) => option.id === value) || null
        }
    })

    const options = computed(() => {
        return organisationsWithPermissionToCreateEvents.value ?? []
    })

    /**
     * DEL-358: Preselect value when there is only one option
     */
    watchOnce(
        options,
        () => {
            if (options.value?.length === 1) {
                modelValue.value = options.value.at(0)
            }
        },
        {
            immediate: true
        }
    )
</script>
