<template>
    <UiUniversalSelect
        v-model="selectedQualifications"
        :options="qualifications"
        label-prop="value2"
        category-prop="category"
        sortBy="label"
        :isLoading="isFetching"
        class="my-2" />
</template>

<script lang="ts" setup>
    import { useVModel } from '@vueuse/core'
    import ListType from '~/enums/listType'
    import { useCodeEntries } from '~/queries/code-entries'

    const props = defineProps<{
        modelValue: ApiModel<'OperationQualification'>[]
    }>()

    const emit = defineEmits<{
        (e: 'update:modelValue', value: ApiModel<'EventPost'>): void
    }>()

    const modelValue = useVModel(props, 'modelValue', emit)

    const { operationQualifications } = useListType()

    const { data: codeEntries, isFetching } = useCodeEntries([...operationQualifications, ListType.QualificationType])

    const { qualifications, selectedQualifications } = useOperationQualifications(modelValue, codeEntries)
</script>
