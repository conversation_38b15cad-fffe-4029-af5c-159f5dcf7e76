<template>
    <UiUniversalSelect v-model="modelValue" :options="data" label-prop="value2" :placeholder="placeholder" :is-loading="isFetching" />
</template>

<script setup lang="ts">
    import ListType from '~~/enums/listType'
    import { useCodeEntries } from '~~/queries/code-entries'

    const { list, placeholder = 'Bitte auswählen' } = defineProps<{
        list: ListType | keyof typeof ListType
        placeholder?: string
    }>()

    const modelValue = defineModel<ApiModel<'CodeEntry'>>()

    /**
     * We can initalize this component with an enum value or its key
     */
    const codeEntryType = (Number.isInteger(list) ? list : ListType[list]) as ListType
    const { data, isFetching } = useCodeEntries(codeEntryType)
</script>
