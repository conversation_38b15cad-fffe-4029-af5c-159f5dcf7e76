<script setup lang="ts">
    const stack = useComposerStack()
</script>

<template>
    <template :key="id" v-for="{ component, id, props, controls } in stack">
        <component
            :is="component"
            v-bind="props"
            :is-revealed="controls.isRevealed"
            @confirm="(data) => controls.confirm(data)"
            @cancel="(data) => controls.cancel(data)" />
    </template>
</template>
