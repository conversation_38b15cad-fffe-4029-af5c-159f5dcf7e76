<script lang="ts" setup>
    import { Popover, PopoverButton, PopoverPanel } from '@headlessui/vue'
    import { UiIconName } from '~~/modules/ui'

    const links = useSolutionLinks()

    const route = useRoute()
    const router = useRouter()

    function goto(route: any, close: () => any) {
        if (route.href) {
            window.open(route.href, '_blank')
        } else {
            router.push(route)
        }

        close()
    }

    const currentSection = computed(() => {
        return route.meta.title || 'Überblick'
    })
</script>

<template>
    <Popover v-slot="{ open }" class="relative">
        <PopoverButton
            :class="{ 'text-opacity-90': open }"
            class="focus:ring-offset-grey-100 group inline-flex items-center rounded-md border px-2 py-2 lg:px-3 lg:py-2 text-base font-base hover:text-opacity-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
            <span class="text-grey-500">{{ currentSection }}</span>
            <IconChevronDown
                :class="{ 'rotate-180 text-opacity-90': open }"
                class="text-grey-500 xl:ml-2 h-5 w-5 transition duration-150 ease-in-out group-hover:text-opacity-80"
                aria-hidden="true" />
        </PopoverButton>

        <!-- @See bug-report https://github.com/tailwindlabs/headlessui/issues/1483-->

        <Transition
            enter-active-class="transition duration-200 ease-out"
            enter-from-class="translate-y-1 opacity-0 z-10"
            enter-to-class="translate-y-0 opacity-100 z-10"
            leave-active-class="transition duration-150 ease-in"
            leave-from-class="translate-y-0 opacity-100 z-10"
            leave-to-class="translate-y-1 opacity-0 z-10">
            <div v-if="open">
                <PopoverPanel as="div" class="absolute z-10 w-screen max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg origin-top-left" v-slot="{ close }">
                    <div
                        class="relative grid gap-8 overflow-hidden rounded-lg bg-white p-7 shadow-lg ring-1 ring-black ring-opacity-5 md:grid-cols-2">
                        <!--
              @see https://headlessui.com/vue/popover#closing-popovers-manually
              To close a popover manually when clicking a child of its panel, render that child as a PopoverButton. You can use the :as prop to customize which element is being rendered.
            -->
                        <PopoverButton
                            v-for="link in links"
                            :key="link.title"
                            @click="goto(link.route, close)"
                            class="hover:bg-grey-50 -m-3 flex items-center rounded-lg p-2 transition duration-150 ease-in-out focus:outline-none focus-visible:ring focus-visible:ring-orange-500 focus-visible:ring-opacity-50">
                            <div class="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-md bg-red-100 p-2">
                                <UiIcon :name="link.icon as UiIconName" class="h-auto w-full text-red-500" />
                            </div>
                            <div class="ml-4">
                                <p class="text-grey-900 block text-left text-sm font-medium">
                                    {{ link.title }}
                                </p>
                                <p v-if="link.description" class="text-grey-500 text-sm">
                                    {{ link.description }}
                                </p>
                            </div>
                        </PopoverButton>
                    </div>
                </PopoverPanel>
            </div>
        </Transition>
    </Popover>
</template>
