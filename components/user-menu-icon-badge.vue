<script lang="ts" setup>
    import { UiIconName } from '~~/modules/ui'

    const props = defineProps<{
        href: string
        label: string
        icon: UiIconName
        count: number
    }>()

    const visibleCount = computed(() => (props.count > 99 ? '99+' : props.count))
</script>

<template>
    <a :href="href" target="_blank" class="form-button button-sm button-secondary relative rounded p-1 transition-colors">
        <UiIcon :name="icon" class="h-5 w-5" aria-hidden="true" />
        <span class="sr-only">{{ label }}</span>
        <div
            v-if="count"
            class="absolute -top-[2px] left-[17px] min-w-[1rem] rounded-md bg-red-400 p-1 text-center text-[0.65rem] font-bold leading-none text-white">
            {{ visibleCount }}
        </div>
    </a>
</template>
