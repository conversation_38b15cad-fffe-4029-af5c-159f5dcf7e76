<script lang="ts" setup>
    import { Menu, MenuButton, MenuItems, MenuItem } from '@headlessui/vue'
    import { useFetchBadgeCount } from '~/queries/badge-count'

    defineProps({
        user: {
            required: true,
            type: Object
        }
    })

    const {
        public: { baseUrl, drkLegacy }
    } = useRuntimeConfig()

    const { data: avatar, getMemberProfileImage } = useGetMemberProfileImage()

    const { $user } = useNuxtApp()

    if($user.basicData) {
        getMemberProfileImage($user.basicData.id)
    }

    const { data: badgeCount } = useFetchBadgeCount()

    const notificationCount = computed(() => badgeCount?.value?.notificationsCount || 0)
    const messagesCount = computed(() => badgeCount?.value?.messagesCount || 0)
    const todosCount = computed(() => badgeCount?.value?.todosCount || 0)
</script>

<template>
    <div class="flex flex-col-reverse lg:flex-row items-center mt-2 lg:mt-0 lf:mt-0 gap-x-2 flex-1 w-full lg:gap-4">
        <HeaderSearch class="flex-1 w-full py-2" />
        <MainMenu class="flex lg:hidden w-full lg:w-[unset]"/>
        <div class="flex items-center justify-between mb-2 lg:mb-0 sm:justify-[unset] gap-x-1 w-full lg:flex lg:w-[unset] ">
            <div class="flex items-center gap-x-2 pr-3">
                <UserMenuIconBadge :href="drkLegacy + '/msg'" label="Posteingang" icon="mail" :count="messagesCount" />
                <UserMenuIconBadge :href="drkLegacy + '/msg/drknachrichten--tab/todos'" label="Todos" icon="clipboard-check" :count="todosCount" />
                <UserMenuIconBadge :href="drkLegacy + '/msg/drknachrichten--tab/deadlines'" label="Termine" icon="calendar-outlined" :count="0" />
                <UserMenuIconBadge
                    :href="drkLegacy + '/msg/drknachrichten--tab/info'"
                    label="Benachrichtigungen"
                    icon="bell"
                    :count="notificationCount" />
            </div>

            <Menu as="div" class="relative inline-block text-left">
                <MenuButton
                    class="text-grey-900 bg-grey-100 hover:bg-grey-50 focus:ring-offset-grey-100 inline-flex w-full items-center justify-center rounded-md px-4 py-2 font-medium focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                    <UiAvatar class="mr-2" :image="avatar" :name="user?.name" />
                    <span class="hidden sm:flex text-base truncate max-w-xs">{{ user?.name }}</span>
                    <IconChevronDown class="ml-2 -mr-1 h-5 w-5" aria-hidden="true" />
                </MenuButton>
                <Transition
                    enter-active-class="transition duration-100 ease-out"
                    enter-from-class="transform scale-95 opacity-0"
                    enter-to-class="transform scale-100 opacity-100"
                    leave-active-class="transition duration-75 ease-in"
                    leave-from-class="transform scale-100 opacity-100"
                    leave-to-class="transform scale-95 opacity-0">
                    <MenuItems
                        class="absolute right-0 mt-1 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                        <MenuItem v-slot="{ active }">
                            <a
                                class="block px-4 py-2 text-sm"
                                :class="{ 'bg-blue-100': active }"
                                :href="drkLegacy + '/mv/drkadminmember-mitglied/' + user?.sub"
                                target="_blank">
                                Meine Akte
                            </a>
                        </MenuItem>
                        <MenuItem v-slot="{ active }">
                            <a class="block px-4 py-2 text-sm" :class="{ 'bg-blue-100': active }" :href="`${baseUrl}/logout`"> Logout </a>
                        </MenuItem>
                    </MenuItems>
                </Transition>
            </Menu>
        </div>
    </div>
</template>
