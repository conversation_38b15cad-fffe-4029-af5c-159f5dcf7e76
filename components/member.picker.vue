<template>
    <Combobox as="div" :model-value="null" class="combobox relative" v-slot="{activeOption}">
        <div ref="reference" class="relative flex items-center combobox-input gap-x-2" >
            <ComboboxInput
            @change="query = $event.target.value"
            @keydown.enter ="select(activeOption)"
            @keydown.esc ="query =''"
            :placeholder="placeholder"
            class="w-full p-0 text-base outline-none" />
            <IconRefresh v-if="isFetching" class="w-4 h-4 text-gray-500 animate-spin" />
        </div>

        <div ref="popper" class="absolute z-50 w-full" :class="{ 'max-w-sm': !fullWidthPanel, 'max-w-[528px]': fullWidthPanel, 'transformX': customPosition }">
            <ComboboxOptions v-if="showOptions" ref="combobox" class="combobox-options">
                <div v-if="allowedScopes.length > 1" class="mx-3 flex items-center justify-end border-b bg-white pb-0.5 pt-2 text-xs text-gray-500">
                    <UiMenu>
                        <template #button>
                            <span class="flex items-center text-xs gap-x-2">
                                <template v-if="scope === 'GLOBAL_MEMBERS'"> <UiIcon name="globe-alt" class="w-3 h-3" />Gesamtes DRK </template>
                                <template v-else> <UiIcon name="collection" class="w-3 h-3" />Meine Gliederung(en) </template>
                            </span>
                        </template>
                        <UiMenuItem v-if="scope !== 'GLOBAL_MEMBERS'" icon="globe-alt" @click="scope = 'GLOBAL_MEMBERS'">Gesamtes DRK</UiMenuItem>
                        <UiMenuItem v-if="scope !== 'MY_MEMBERS'" icon="collection" @click="scope = 'MY_MEMBERS'">Meine Gliederung(en)</UiMenuItem>
                    </UiMenu>
                </div>
                <template v-if="(isFetching && !isFetchingNextPage)">
                   <div v-if="!members.length && isFetching" class="px-3 py-2 text-sm font-light text-grey-400">
                        <UiIcon name="refresh" class="inline-block w-4 h-4 mr-2 animate-spin" />
                        Suche nach <span class="italic text-grey-600">{{ query }}</span>
                    </div>
                </template>
                <template v-else-if="!members.length && isFetched">
                    <div class="px-3 py-2 text-sm font-light align-baseline text-grey-400">
                        <UiIcon name="ban" class="inline-block w-4 h-4 mr-2" />
                        Keine Vorschläge für <span class="italic text-grey-600">{{ query }}</span>
                    </div>
                </template>
                <template v-else>
                    <UiScrollarea  class="max-h-64">
                        <ComboboxOption
                            v-for="option in members"
                            :key="option.id"
                            :value="option"
                            :disabled="isDisabled(option)"
                            @click="select(option)"
                            class="relative combobox-option ui-disabled:grayscale ui-disabled:opacity-40">
                            <ProvideMember :member="option" class="text-sm" />
                        </ComboboxOption>
                        <div v-if="hasNextPage || isFetchingNextPage" class="flex justify-center py-2 mx-3 border-t">
                            <button @click="() => fetchNextPage()" :disabled="isFetchingNextPage" class="form-button button-xs">
                                <span v-if="isFetchingNextPage">Weitere werden geladen</span>
                                <span v-else-if="hasNextPage" class="flex items-center gap-x-1">
                                    <UiIcon name="plus" class="w-3 h-3" />
                                    Weitere laden
                                </span>
                            </button>
                        </div>
                    </UiScrollarea>

                </template>
            </ComboboxOptions>
        </div>
    </Combobox>
</template>

<script lang="ts" setup>
    import { Combobox, ComboboxInput, ComboboxOptions, ComboboxOption } from '@headlessui/vue'
    import { useInfiniteMemberSuggestions } from '~/composables/suggestions'
    import { onClickOutside } from '@vueuse/core'

    const props = withDefaults(
        defineProps<{
            allowedScopes: ('MY_MEMBERS' | 'GLOBAL_MEMBERS')[]
            popperPosition?: 'bottom-start' | 'bottom-end' | 'top-start' | 'auto-start'
            placeholder?: string
            fullWidthPanel?: boolean
            customPosition?: boolean
            excludeMembers?: ApiModel<'MemberData'>[]
        }>(),
        {
            customPosition: false,
            popperPosition: 'bottom-start',
            placeholder: 'Für Vorschläge tippen',
            fullWidthPanel: false
        }
    )


    const { query, scope, members, isFetching, isFetched, isFetchingNextPage, throttledOrDebouncedInput, fetchNextPage, hasNextPage } = useInfiniteMemberSuggestions(true)

    const showOptions = computed(() => {
    return (
            !!query.value &&
            (
                isFetching.value || // still loading next data
                throttledOrDebouncedInput.value === query.value // result is fresh
            )
        )
    })

    const waitUntil = (condition: () => boolean): Promise<void> => {
        return new Promise((resolve) => {
            const check = () => {
                if (condition()) {
                    resolve()
                } else {
                    requestAnimationFrame(check)
                }
            }
            check()
        })
    }

    const select = async (activeOption) => {

        // Give time for fetch to trigger. This value is the same as in useInfiniteMemberSuggestions() for debaunce
        await new Promise(resolve => setTimeout(resolve, 500))

        await waitUntil(() => !isFetching.value)

        if (!activeOption) {
            activeOption = members.value.find(member => !isDisabled(member))
        }

        if (!activeOption || isDisabled(activeOption)) return

        emit('select', activeOption)
        query.value = ''
    }

    const emit = defineEmits<{
        (event: 'select', value: ApiModel<'MemberData'>): any
    }>()


    if (!!props.allowedScopes.length) {
        scope.value = props.allowedScopes.at(0)
    }

    function isDisabled(member: ApiModel<'MemberData'>) {
        return !!props.excludeMembers?.find(({ id }) => id === member.id)
    }

    const reference = ref()
    const popper = ref()
    usePopper(reference, popper, {
        placement: props.popperPosition,
        modifiers: [
            {
                name: 'offset',
                options: {
                    offset: [0, 4]
                }
            },
            {
                name: 'preventOverflow'
            }
        ],
    })

    const combobox = ref()
    onClickOutside(popper, () =>  query.value = "")
</script>

<style scoped lang="pcss">
    .combobox {
        @apply relative
    }
    .combobox-input {
        @apply w-full form-input pl-7;
        @apply icon-search-blue-500 bg-[left_0.5rem_center] bg-[length:1rem_1rem] bg-no-repeat
    }
    .combobox-options {
        /* @apply absolute z-30 w-full mt-1 overflow-auto text-base bg-white rounded-md shadow-xl max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none; */
        @apply overflow-auto empty:hidden text-base bg-white rounded-md shadow-xl ring-1 ring-black ring-opacity-5 focus:outline-none;
    }

    .combobox-option {
        @apply  py-2 pl-3 cursor-default select-none pr-9 text-grey-800 ui-active:bg-gray-50;

        &[data-headlessui-state~="active"] {
            @apply icon-plus-slate-500 bg-no-repeat bg-[length:1em_1em] bg-[right_1em_center]
        }

    }

    .transformX {
       transform: translate(0px, 42px) !important;
    }
</style>
