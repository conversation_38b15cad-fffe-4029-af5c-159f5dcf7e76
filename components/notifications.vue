<script lang="ts" setup>
    const { stack: notifications, dismiss } = useNotifications()
</script>

<template>
    <div class="pointer-events-none fixed bottom-2 z-30 flex w-full max-w-7xl flex-col gap-y-2 pb-2 sm:px-6 lg:px-8">
        <UiNotification
            v-for="notification in notifications"
            @dismissed="(_event) => dismiss(notification.id)"
            @callback="() => notification.cb?.()"
            :content="unref(notification.content)"
            :type="unref(notification.type)"
            :is-final="!!unref(notification.isFinal)"
            :key="notification.id"
            class="pointer-events-auto max-w-3xl" />
    </div>
</template>
