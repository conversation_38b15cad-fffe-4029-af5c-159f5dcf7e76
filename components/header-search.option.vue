<template>
  <div>
    <div class="p-2 font-bold">{{ title }}</div>
    <div v-if="isFetching && !isFetchingNextPage">
      <div class="px-3 py-2 text-sm font-light text-grey-400">
        <UiIcon name="refresh" class="inline-block w-4 h-4 mr-2 animate-spin" />
        Suche nach <span class="italic text-grey-600">{{ query }}</span>
      </div>
    </div>
    <div v-else>
      <div v-if="options.length">
        <ul>
          <li
            v-for="(option, index) in options"
            :key="option.id"
            @click="selectOption(option)"
            @mouseover="handleHover(index)"
            class="header_search-option"
            :class="{ 'ui-active': index + indexOffset === activeIndex }"
          >
            <slot :option="option">
            </slot>
          </li>
        </ul>
      </div>
      <div v-if="!options.length && !isFetching" class="px-3 py-2 text-sm font-light align-baseline text-grey-400">
        <UiIcon name="ban" class="inline-block w-4 h-4 mr-2" />
        Keine Vorschläge für <span class="italic text-grey-600">{{ query }}</span>
      </div>
    </div>
    <div v-if="hasNextPage || isFetchingNextPage" class="flex justify-center py-2 mx-3 border-b">
      <button @click="fetchNextPage" :disabled="isFetchingNextPage" class="form-button button-xs">
        <span v-if="isFetchingNextPage">Weitere werden geladen</span>
        <span v-else class="flex items-center gap-x-1">
          <UiIcon name="plus" class="w-3 h-3" />
          Weitere laden
        </span>
      </button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { PropType, defineEmits } from 'vue';

const props = defineProps({
  title: String,
  options: Array as PropType<Array<any>>,
  isFetching: Boolean,
  hasNextPage: Boolean,
  fetchNextPage: Function as PropType<() => void>,
  query: String,
  isFetchingNextPage: Boolean,
  activeIndex: Number,
  indexOffset: Number,
});

const emit = defineEmits(['select', 'hover']);

function selectOption(option: any) {
  emit('select', option);
}

function handleHover(index: number) {
  emit('hover', index + props.indexOffset);
}
</script>

<style scoped>
  .header_search-option {
    @apply py-2 pl-3 cursor-default select-none pr-9 text-grey-800 ui-active:bg-gray-50;
  }
  .ui-active {
    @apply bg-gray-50;
  }
</style>

