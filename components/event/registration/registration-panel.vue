<template>
    <div>
        <div class="mb-8 flex flex-wrap gap-2 sm:gap-0 sm:flex-nowrap justify-start sm:justify-[unset] items-start">
            <h2 class="text-md font-semibold">Registrierte Personen</h2>
            <div class="sm:ml-auto flex items-center transition-all" :class="{ 'pointer-events-none opacity-50 grayscale': isLoading || isMutating }">
                <button v-if="canManageRegistrations" @click="replaceWithRessourceSettings" class="form-button button-xs py-2 px-4  sm:button-sm button-contained ml-auto mr-4">
                    Planung übernehmen
                </button>
                <button v-if="canManageRegistrations" @click="replaceWithSigningUps" class="form-button button-xs py-2 px-4 sm:button-sm button-contained">
                    Rückmeldungen übernehmen
                </button>
            </div>
        </div>
        <div class="flex flex-col sm:flex-row gap-2 items-start justify-between my-2">
            <input class="search-input w-3/4 sm:w-1/2 md:w-1/3" v-model="eventRegistrationFilter.descriptionContains" placeholder="Registrierung durchsuchen" />
            <div class="flex flex-col sm:flex-col items-center w-full sm:items-start sm:-mt-2 sm:w-[unset]">
                <div class="flex gap-3 items-center w-full justify-end">
                    <span>nur Anwesende anzeigen</span><UiSwitch v-model="eventRegistrationFilter.isEndDateSet" class="-mr-3"/>
                </div>

                <UiMenu button-size="sm" class="w-full flex justify-end">
                    <template #button>
                        <UiIcon :name="eventRegistrationSorter.icon" class="h-auto w-3" />
                    {{ eventRegistrationSorter.label }}
                    </template>
                    <UiMenuItem v-for="{ key, label, set } in eventRegistrationSorter.alternatives" :key="key" @click="set">
                        {{ label }}
                    </UiMenuItem>
                </UiMenu>
            </div>
        </div>

        <div v-if="isLoading" class="flex min-h-[170px] w-full animate-pulse items-center justify-center bg-gray-50">
            <span class="inline-flex items-center gap-x-2 font-light">
                <IconRefresh class="h-4 w-4 animate-spin text-gray-500" />
                Registrierte Personen werden geladen.
            </span>
        </div>
        <div v-else>
            <div class="mb-2 flex w-full items-center px-2 sm:px-4 py-2" :class="{ 'bg-gray-50': !!selected.length }">
                <div class="flex-0 mr-6">
                    <input
                        v-if="!!sortedEventRegistrations.length"
                        :disabled="!canManageRegistrations"
                        type="checkbox"
                        v-model="selectorState"
                        :indeterminate="!!selected.length && selected.length !== sortedEventRegistrations.length"
                        class="form-checkbox indeterminate:icon-minus-green-600 h-5 w-5 rounded-full" />
                </div>

                <div v-if="!selected.length || !canManageRegistrations" class="grid flex-1 grid-cols-12 gap-x-2 sm:gap-x-4">
                    <div class="col-span-6 lg:col-span-5 text-xs font-semibold">Name</div>
                    <div class="col-span-6 lg:col-span-7 text-xs font-semibold">Anwesend von / bis</div>
                </div>

                <div v-else class="flex items-center gap-2">
                    <button @click="removeSelectedRegistrations" class="form-button button-xs text-gray-900">
                        <UiIcon name="trash" class="h-4 w-4 text-red-500" />
                        Löschen
                    </button>

                    <button @click="updateMultipleRegistrations" class="form-button button-xs text-gray-900">
                        <UiIcon name="pencil" class="h-4 w-4 text-red-500" />
                        Zeiten ändern
                    </button>
                </div>
            </div>

             <TransitionGroup
                    enter-active-class="transition"
                    enter-from-class="translate-y-1 opacity-0"
                    enter-to-class="translate-y-0 opacity-100"
                    name="list" tag="div" class="">
                <div
                    v-for="registration in sortedEventRegistrations"
                    :key="registration._uuid"
                    class="group/registration flex w-full items-center border-b px-2 sm:px-4 py-2 first:border-t hover:bg-gray-50"
                    :class="{ 'bg-gold-50 hover:!bg-gold-100 border-b-gold-200': selected.includes(registration.id) }">
                    <div class="flex-0 mr-4 sm:mr-6 pb-9 md:pb-12 lg:pb-0">
                        <input
                            type="checkbox"
                            v-model="selected"
                            :disabled="!registration.id || !canManageRegistrations"
                            :value="registration.id"
                            class="form-checkbox h-5 w-5 rounded-full" />
                    </div>

                    <EventRegistrationListItem
                        :event="event"
                        :registration="registration"
                        @update:registration="updateRegistration"
                        @remove:registration="removeRegistration"
                        class="flex-1" />
                </div>
                <div v-if="!sortedEventRegistrations.length" class="border-y px-4 pb-12 pt-4" key="___empty___">
                    <template v-if="!isMutating">
                        <span class="">Die Liste ist leer</span>
                        <p v-if="canManageRegistrations" class="text-sm font-light">
                            Füge Teilnehmende hinzu. Klicke dazu entweder auf einen der Buttons über dieser Box. Oder gib einen Namen in das Feld
                            unter dieser Box ein.
                        </p>
                        <p v-else class="text-sm font-light">Du kannst momentan keine Registrierungen hinzufügen.</p>
                    </template>
                    <template v-else>
                        <span class="flex items-center gap-x-2">
                            <IconRefresh class="h-5 w-5 animate-spin text-gray-500" />
                            Die Liste wird erstellt
                        </span>
                        <p class="text-sm font-light">Das kann einen kleinen Moment dauern.</p>
                    </template>
                </div>
            </TransitionGroup>
        </div>
        <EventRegistrationCreateForm
            v-if="canManageRegistrations"
            :event="event"
            @submit="createRegistration"
            @registerExternalPerson="registerExternalPersonDialog"
            class="mt-4"
            :class="{ 'pointer-events-none opacity-25': isLoading || isMutating }" />
    </div>

    <UiConfirmDialog
        title="Planung übernehmen"
        :controller="dialog.replaceWithRessourceSettings"
        confirm-with="Okay"
        cancel-with="Abbrechen">
        Willst du die Liste der Teilnehmenden überschreiben?
    </UiConfirmDialog>

    <UiConfirmDialog
        title="Rückmeldungen übernehmen"
        :controller="dialog.replaceWithSigningUps"
        confirm-with="Okay"
        cancel-with="Abbrechen">
        Willst du die Liste der Teilnehmenden überschreiben?
    </UiConfirmDialog>

    <UiConfirmDialog
        title="Aus der Liste entfernen"
        :controller="dialog.removeRegistration"
        confirm-with="Registrierung entfernen"
        cancel-with="Abbrechen">
        Teilnehmende, die du entfernst, kannst du später einfach wieder hinzufügen. Für extern Mitwirkende gilt das nicht. Deren Daten gibst du erneut ein.
    </UiConfirmDialog>

    <UiConfirmDialog
        title="Mehrere Registrierungen enfernen"
        :controller="dialog.removeSelectedRegistrations"
        confirm-with="Registrierungen entfernen"
        cancel-with="Abbrechen">
        Teilnehmende, die du entfernst, kannst du später einfach wieder hinzufügen. Für extern Mitwirkende gilt das nicht. Deren Daten gibst du erneut ein.
    </UiConfirmDialog>

    <EventRegistrationUpdateMultipleDialog :controller="dialog.updateMultipleRegistrations" />
    <EventRegistratorRegisterExternalPersonComposer :controller="registerExternalPerson" />
</template>

<script lang="ts" setup>
    import { useConfirmDialog, useToggle, watchDebounced } from '@vueuse/core'
    import { useQueryClient } from '@tanstack/vue-query'

    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const queryClient = useQueryClient();

    /**
     * Recieve the needed permissions
     */
    const { canManageRegistrations } = inject(EventPermissionsKey)

    /**
     * Load registrations and apply sorting because otherwise the entries jump around
     */
    const { data: _registeredPersons, isLoading } = useEventQuery('registrations')

    const { eventRegistrationFilter, visibleRegistrations } = useEventRegistrationFilter(_registeredPersons)
    const { eventRegistrationSorter, sortedEventRegistrations } = useEventRegistrationSorter(visibleRegistrations)

    let intervalId: ReturnType<typeof setInterval>;

    onMounted(() => {
        intervalId = setInterval(() => {
           eventRegistrationFilter.currentDate = new Date()
        }, 1000);
    });

    onUnmounted(() => {
        clearInterval(intervalId);
        intervalId = null
    });

    /**
     * We'll need some commit actions in order to actually mutate data
     */
    const commit = useCommitEventMutation(props.event)
    const runWithNotification = useRunWithNotification()
    const { push: notify } = useNotifications()

    /**
     * The needed confirmation dialogs
     */
    const dialog = {
        removeRegistration: useConfirmDialog(),
        replaceWithSigningUps: useConfirmDialog(),
        replaceWithRessourceSettings: useConfirmDialog(),
        removeSelectedRegistrations: useConfirmDialog(),
        updateMultipleRegistrations: useDialogController('updateMultipleRegistrations')
    }

    const [isMutating, setMutationStatus] = useToggle(false)

    /**
     * Create a single registration
     *
     * Thanks to optimistic updates we don't need feedback in the form of a notification here. But:
     *
     * "Es können nur neue Registrierungen angelegt werden, wenn es für die Person keine "offene" Registrierung gibt.
     *  Offen bedeutet, dass start angegeben wurde, aber end noch leer ist."
     *
     * In diesem Fall gibt es die Fehlermeldung: Die gewählte Person wurde bereits registriert und ist derzeit aktiv.
     */
    async function createRegistration(registration: ApiModel<'EventRegistration'>) {
        try {
            await commit('createRegistration', registration)
        } catch (error) {
            notify({
                type: 'error',
                content:
                    'Die Registrierung konnte nicht angelegt werden. Möglicherweise liegt für die betreffende Person noch eine offene Registrierung vor.'
            })
        }
    }

    /**
     * Update a single registration
     *
     * Thanks to optimistic updates we don't need feedback in the form of a notification here
     */
    async function updateRegistration(registration: ApiModel<'EventRegistration'>) {
        commit('updateRegistration', registration)
    }

    /**
     * Remove a single registration
     *
     * Thanks to optimistic updates we don't need feedback in the form of a notification here
     */
    async function removeRegistration(registration: ApiModel<'EventRegistration'>) {
        const { isCanceled } = await dialog.removeRegistration.reveal()

        if (!isCanceled) {
            commit('removeRegistration', registration)
        }
    }

    async function removeSelectedRegistrations() {
        const { isCanceled } = await dialog.removeSelectedRegistrations.reveal()

        if (!isCanceled) {
            runWithNotification(
                () =>
                    Promise.all(
                        selected.value.map((id) => {
                            const registration = sortedEventRegistrations.value.find((registration) => id === registration.id)
                            return commit('removeRegistration', registration)
                        })
                    ),
                {
                    pending: 'Ausgewählte Registrierungen werden gelöscht.',
                    error: 'Du wolltest mehrere Regsitrierungen löschen. Das hat nicht geklappt.'
                }
            )
        }
    }

    /**
     * Replace the whole list with registrations based in signing ups
     */
    async function replaceWithSigningUps() {
        const { isCanceled } = await dialog.replaceWithSigningUps.reveal()

        if (!isCanceled) {
            setMutationStatus(true)
            await runWithNotification(() => commit('replaceRegistrations', 'SIGNING'), {
                pending: 'Die Liste der Teilnehmenden wird erstellt.',
                error: 'Offenbar hat sich noch niemand für dieses Ereignis zurückgemeldet. Sobald das passiert ist, kannst du diese Menschen hier registrieren.'
            })
            setMutationStatus(false)
        }
    }

    /**
     * Replace the whole list with registrations based on resource settings
     */
    async function replaceWithRessourceSettings() {
        const { isCanceled } = await dialog.replaceWithRessourceSettings.reveal()

        if (!isCanceled) {
            setMutationStatus(true)
            await runWithNotification(() => commit('replaceRegistrations', 'PLANNING'), {
                pending: 'Die Liste der Teilnehmenden wird erstellt.',
                error: 'Hast du schon Planstellen angelegt und diese besetzt? Erst dann können hier in der Liste die Menschen auftauchen, die du verplant hast.'
            })
            setMutationStatus(false)
        }
    }

    /**
     * Update start/end of multiple registrations
     */
    async function updateMultipleRegistrations() {
        if (!selected.value.length) {
            return
        }

        const selectedRegistrations = selected.value.map((id) => {
            return sortedEventRegistrations.value.find((registration) => id === registration.id)
        })

        const { data: updatedRegistrations, isCanceled } = await dialog.updateMultipleRegistrations.reveal(selectedRegistrations)

        if (!isCanceled) {
            runWithNotification(
                () => {
                    return Promise.all(
                        updatedRegistrations.map((registration) => {
                            return commit('updateRegistration', registration)
                        })
                    )
                },
                {
                    pending: `${updatedRegistrations.length} Registrierung(en) werden aktualisiert.`,
                    error: 'Du wolltest die Zeiten von ${updatedRegistrations.length} Registrierung(en) ändern. Das hat nicht geklappt.'
                }
            )
        }
    }

    /**
     * Store IDs of selected event registrations
     */
    const selected = ref<number[]>([])

    /**
     * This is the toggle behind the "select all" checkbox
     */
    const selectorState = computed({
        get() {
            return !!sortedEventRegistrations.value.length && selected.value.length === sortedEventRegistrations.value.length
        },
        set(newState) {
            if (!!newState) {
                selected.value = sortedEventRegistrations.value.map(({ id }) => id)
            } else {
                selected.value = []
            }
        }
    })

    const registerExternalPerson = useConfirmDialog()

    const {isLoading: isExternalLoading, createRegistrationExternal } = useCreateRegistrationExternal()

    async function registerExternalPersonDialog() {
        const { data, isCanceled } =  await registerExternalPerson.reveal()

        if (!isCanceled) {

            createRegistrationExternal({
                registrationCard: data.person,
                start: data.start,
                end: data.end,
                eventId: props.event.id
            })
            watch(isExternalLoading, ()=>{
                queryClient.refetchQueries(['events','detail',props.event.id,'registrations'])
            })
        }
    }


    /**
     * Adjust selection: Whenever the list of registration changes we have to clean the list of selected ids
     */
    watch(sortedEventRegistrations, () => {
        const registrationIds = sortedEventRegistrations.value.map(({ id }) => id)
        selected.value = selected.value.filter((id) => registrationIds.includes(id))
    })
</script>

<style lang="pcss" scoped>
    .list-move,  .list-enter-active, .list-leave-active {
        transition: all 0.3s ease;
    }

    .list-enter-from, .list-leave-to {
        opacity: 0;
        transform: translateX(16px);
    }

    /* ensure leaving items are taken out of layout flow so that moving
        animations can be calculated correctly. */
    .list-leave-active {
        position: absolute;
        display: none;
    }

    .search-input {
        @apply form-input py-[.25rem] text-sm leading-6 basis-full sm:basis-[unset];
        @apply pl-[1.5rem] bg-no-repeat icon-search-sky-500 bg-[length:1rem_1rem] bg-[.25rem_center]
    }
</style>
