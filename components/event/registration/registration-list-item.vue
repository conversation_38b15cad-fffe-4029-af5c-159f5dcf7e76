<template>
    <div class="relative grid grid-cols-12 text-sm md:gap-4" ref="popoverReference">
        <div class="group col-span-6 self-center lg:col-span-5">
            <ProvideMember class="flex cursor-pointer items-center text-sm" aria-label="member" :member="member">
                <template #default="{ name, avatar, organisation }">
                    <div class="flex items-center p-0.5 text-sm">
                        <div
                            class="pointer-events-auto flex cursor-pointer items-center text-sm hover:bg-blue-50 hover:ring-4 hover:ring-blue-50"
                            @click="openMemberDataDialog(member)">
                            <UiAvatar :name="name" :image="avatar" size="base" class="mr-[0.5em]" />
                            <span class="flex flex-col leading-none">
                                <span class="flex flex-row items-center gap-1">
                                    {{ name }} <UiIcon name="user" class="h-4 w-4 opacity-0 transition-opacity group-hover:opacity-100" /> <br />
                                </span>
                                <span v-text="organisation" class="text-xs text-gray-500" />
                            </span>
                        </div>
                    </div>
                </template>
            </ProvideMember>
        </div>

        <div class="col-span-6 justify-start self-center sm:col-span-6">
            <Popover class="relative -ml-2">
                <PopoverButton
                    class="form-button flex items-start disabled:opacity-100 md:gap-x-2 md:px-2 md:py-1"
                    :disabled="!canManageRegistrations">
                    <span v-if="registration.end" class="inline-block text-left leading-tight text-gray-900">
                        {{ $formatDateWithAbbreviatedDay(registration.start, 'short', '2-digit', '2-digit', '2-digit') }}
                        {{ $formatTime(registration.start) }} - <br class="md:hidden" />
                        {{ $formatDateWithAbbreviatedDay(registration.end, 'short', '2-digit', '2-digit', '2-digit') }}
                        {{ $formatTime(registration.end) }} <br />
                        <span class="text-xs font-light">
                            {{ $formatDuration(registration.start, registration.end) }}
                        </span>
                    </span>

                    <span v-else class="inline-block text-left leading-tight text-gray-900">
                        Offen seit: {{ $formatDateWithAbbreviatedDayAndTime(registration.start) }}<br />
                        <span class="text-xs font-light">
                            Voraussichtlich anwesend: {{ $formatDuration(registration.start, dateNow) || 'unbekannt' }}
                        </span>
                    </span>
                    <UiIcon
                        v-if="canManageRegistrations"
                        name="pencil"
                        class="h-4 w-4 transition-opacity lg:opacity-0 lg:group-hover/registration:opacity-100" />
                </PopoverButton>

                <div ref="popoverPanel" class="z-10 w-screen p-2 md:w-[740px] md:p-0">
                    <PopoverPanel class="rounded-md border bg-white p-4 shadow-xl">
                        <h3 class="mb-2 font-semibold">{{ $fullName(member) }}</h3>
                        <fieldset class="mb-6 flex flex-col gap-y-3 sm:flex-row sm:gap-x-4 sm:gap-y-0">
                            <div class="flex-1">
                                <UiFormField label="Anwesend von:">
                                    <UiDateTimePicker
                                        v-model="updateable.start"
                                        :min="startRange.min"
                                        :max="startRange.max"
                                        :disabled="isDateFromNow"
                                        class="w-full" />
                                </UiFormField>
                                <UiCheckInput ref="checkboxIsDateFromNow" label="kommt jetzt" v-model="setDateFromNow" class="mt-2" />
                            </div>
                            <div class="flex-1">
                                <UiFormField label="Anwesend bis:">
                                    <UiDateTimePicker
                                        v-model="updateable.end"
                                        :min="endRange.min"
                                        :max="endRange.max"
                                        :disabled="isDateUntilNow"
                                        class="w-full" />
                                </UiFormField>
                                <UiCheckInput ref="checkboxIsDateUntilNow" label="geht jetzt" v-model="setDateUntilNow" class="mt-2" />
                            </div>
                        </fieldset>

                        <div class="flex items-center justify-start gap-x-4">
                            <span v-text="$formatDuration(updateable.start, updateable.end) || 'Ungültige Angaben'" />
                            <PopoverButton
                                class="form-button button-sm ml-auto"
                                @click="
                                    () => {
                                        setDateFromNow = false
                                        setDateUntilNow = false
                                    }
                                "
                                >Abbrechen</PopoverButton
                            >
                            <PopoverButton
                                as="button"
                                @click="emit('update:registration', updateable)"
                                :disabled="v$.$invalid"
                                class="form-button button-contained button-sm">
                                Übernehmen
                            </PopoverButton>
                        </div>
                    </PopoverPanel>
                </div>
            </Popover>
        </div>

        <div class="col-span-12 inline-flex justify-end lg:col-span-1" x-class="transition-opacity opacity-0 group-hover/registration:opacity-100">
            <button
                v-if="canManageRegistrations"
                @click="emit('remove:registration', registration)"
                class="form-button rounded-sm"
                title="Person entfernen">
                <UiIcon name="trash" class="h-4 w-4" />
            </button>
        </div>
    </div>
</template>

<script lang="ts" setup>
    import { Popover, PopoverButton, PopoverPanel } from '@headlessui/vue'
    import { syncRef } from '@vueuse/core'
    import useVuelidate from '@vuelidate/core'
    import { required, helpers } from '@vuelidate/validators'
    import { ViewMemberData } from 'composables/members'

    /**
     * Recieve the needed permissions
     */
    const { canManageRegistrations } = inject(EventPermissionsKey)

    const props = defineProps<{
        registration: ApiModel<'EventRegistration'>
        event: ApiModel<'Event'>
    }>()

    const openRegistrationDuration = ref<number | null>(null)
    const dateNow = ref<Date>(new Date())

    onMounted(() => {
        if (openRegistrationDuration.value == null) {
            openRegistrationDuration.value = window.setInterval(() => {
                dateNow.value = new Date()
            }, 1000)
        }
    })

    onUnmounted(() => {
        if (openRegistrationDuration.value !== null) {
            clearInterval(openRegistrationDuration.value)
        }
    })

    const emit = defineEmits<{
        (event: 'remove:registration', value: ApiModel<'EventRegistration'>): any
        (event: 'update:registration', value: ApiModel<'EventRegistration'>): any
    }>()

    function clone(model: ApiModel<'EventRegistration'>) {
        return useModelFactory('EventRegistration').create({
            ...model
        })
    }

    const registration = toRef(props, 'registration')
    const updateable = ref<ApiModel<'EventRegistration'>>(null)

    const member = computed(() =>
        registration.value.resource.external ? registration.value.resource.externalPerson : registration.value.resource.member
    )

    syncRef(registration, updateable, {
        direction: 'ltr',
        transform: {
            ltr: (input) => clone(input)
        }
    })

    const isDateFromNow = ref<boolean>(false)

    const setDateFromNow = ref(false)
    watch(setDateFromNow, (value) => {
        if (value) {
            updateable.value.start = new Date()
        } else {
            updateable.value.start = registration.value.start
        }
        isDateFromNow.value = value
    })

    const isDateUntilNow = ref<boolean>(false)
    const setDateUntilNow = ref(false)
    watch(setDateUntilNow, (value) => {
        if (value) {
            updateable.value.end = new Date()
        } else {
            updateable.value.end = registration.value.end
        }
        isDateUntilNow.value = value
    })

    const checkboxIsDateFromNow = ref()
    const checkboxIsDateUntilNow = ref()

    watch([checkboxIsDateFromNow, checkboxIsDateUntilNow], () => {
        if (checkboxIsDateFromNow.value === null) {
            isDateFromNow.value = false
        }
        if (checkboxIsDateUntilNow.value === null) {
            isDateUntilNow.value = false
        }
    })

    const popoverReference = ref()
    const popoverPanel = ref()

    usePopper(popoverReference, popoverPanel, {
        placement: 'auto',
        modifiers: [
            {
                name: 'offset',
                options: {
                    offset: [0, -8]
                }
            },
            {
                name: 'preventOverflow'
            }
        ]
    })

    const startRange = computed(() => {
        return { min: null, max: updateable.value.end }
    })

    const endRange = computed(() => {
        return { min: updateable.value.start, max: null }
    })

    const v$ = useVuelidate<ApiModel<'EventRegistration'>>(
        {
            resource: { required },
            start: { required },
            end: {
                minValue: (value: Date, siblings: any) => {
                    return !helpers.req(value) || siblings.start <= value
                }
            }
        },
        updateable
    )

    // const { dialog, isBackgroundVisible, reveal: openDialog } = openMemberDataDialog()
    const { openMemberDataDialog } = inject<ViewMemberData>('viewMemberData')
</script>
