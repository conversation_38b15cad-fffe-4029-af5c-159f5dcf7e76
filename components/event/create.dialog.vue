<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel">
        <template #title>Ereignis anlegen</template>

        <template v-if="isRevealed">
            <div class="flex w-full flex-col">
                <UiFormField label="Anlegen für Gliederung *" class="mb-4">
                    <MyOrganisationSelect v-model="event.organisation" />
                </UiFormField>
                <EventBasicDataForm v-model="event" />
            </div>
        </template>

        <template #footer>
            <button class="form-button button-contained-secondary" @click="cancel">Abbrechen</button>
            <button class="form-button button-contained" :disabled="validator.$invalid" @click="submit">Speichern</button>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import ListType from '~~/enums/listType'
    import EventOperationType from '~~/enums/event-operation-type'
    import useVuelidate from '@vuelidate/core'
    import { required } from '@vuelidate/validators'
    import { useCodeEntries } from '~~/queries/code-entries'
    import { DialogController } from '~~/composables/dialog-controller'
    import { isBefore } from 'date-fns'

    const props = defineProps<{
        controller: DialogController<'createEvent'>
    }>()

    const { isRevealed, confirm, cancel, onReveal } = props.controller

    const event = ref<ApiModel<'Event'>>(useModelFactory('Event').create({}))

    onReveal(() => {
        event.value = useModelFactory('Event').create({
            extendedDescription: '', // Quick fix for prop type check in code-entry.input.vue
            category: operationTypes.value[0] // Set default category of event to "Einsatz/Übung/Dienst"
        })
    })

    const { data: operationTypes } = useCodeEntries(ListType.OperationType)

    const isBeforeDateUpTo = () => isBefore(event.value.dateFrom, event.value.dateUpTo)

    const rules = computed(() => {
        const _rules: Record<string, any> = {
            type: { required },
            organisation: { required },
            category: { required },
            extendedDescription: { required },
            dateFrom: { required, isBeforeDateUpTo },
            dateUpTo: { required }
        }

        if (event.value?.type === EventOperationType.Operation) {
            _rules.description = { required }
        }

        if (event.value?.type === EventOperationType.Apprenticeship) {
            _rules.apprenticeshipType = { required }
        }

        return _rules
    })

    const validator = useVuelidate(rules, event)

    function submit() {
        confirm(event.value)
    }
</script>
