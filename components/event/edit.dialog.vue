<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel">
        <template #title><PERSON><PERSON><PERSON><PERSON> bearbeiten</template>

        <template v-if="isRevealed">
            <EventBasicDataForm v-model="eventModel" />
        </template>

        <template #footer>
            <button class="form-button button-contained-secondary" @click="cancel">Abbrechen</button>
            <button class="form-button button-contained" @click="submit" :disabled="validator.$invalid">Speichern</button>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import useVuelidate from '@vuelidate/core'
    import { required } from '@vuelidate/validators'
    import { syncRef } from '@vueuse/core'
    import { DialogController } from '~~/composables/dialog-controller'
    import EventOperationType from '~~/enums/event-operation-type'
    import { isBefore } from 'date-fns'

    const props = defineProps<{
        controller: DialogController<'editEvent'>
        event: ApiModel<'Event'>
    }>()

    const { isRevealed, confirm, cancel } = props.controller

    const eventModel = ref<ApiModel<'Event'>>(null)

    syncRef(toRef(props, 'event'), eventModel, {
        direction: 'ltr',
        transform: {
            ltr: (event) => useModelFactory('Event').fromJson(JSON.parse(JSON.stringify(event)) as object)
        }
    })

    const isBeforeDateUpTo = () => isBefore(eventModel.value.dateFrom, eventModel.value.dateUpTo)

    const rules = computed(() => {
        const _rules: Record<string, any> = {
            type: { required },
            organisation: { required },
            category: { required },
            extendedDescription: { required },
            dateFrom: { required, isBeforeDateUpTo },
            dateUpTo: { required }
        }

        if (eventModel.value?.type === EventOperationType.Operation) {
            _rules.description = { required }
        }

        if (eventModel.value?.type === EventOperationType.Apprenticeship) {
            _rules.apprenticeshipType = { required }
        }

        return _rules
    })

    const validator = useVuelidate(rules, eventModel)

    function submit() {
        confirm(eventModel.value)
    }
</script>
