<template>
    <div class="signing-up-item">
         <EventPlanningSigningUpProvider :signing-up="signingUp" v-slot="{ icon, avatar, organisation, displayName, assignment }">
            <UiAvatar v-if="avatar && signingUp.resource.type === 'TECHNIC'"  :name="displayName" :image="avatar" class="mr-4" />
            <UiIcon v-if="!avatar && signingUp.resource.type === 'TECHNIC'" class="mr-4 h-6 w-6 min-w-[1.5rem] bg-brack" :name="icon" />

            <div class="flex-auto text-xs leading-tight">
                <div
                    class="flex flex-row justify-between mb-1"
                    :class="signingUp.resource.type === 'PERSONAL' ? 'group' : ''"
                >
                    <div
                        class="pr-1"
                        :class="signingUp.resource.type === 'PERSONAL' ? 'hover:bg-blue-50 hover:ring-blue-50 hover:ring-4 group-hover:cursor-pointer' : ''"
                    >
                        <span
                            class="flex items-center gap-1"
                            @click="signingUp.resource.type === 'PERSONAL' ? openMemberDataDialog(memberOrExternalPerson) : null"
                        >
                            <UiAvatar v-if="signingUp.resource.type === 'PERSONAL'" :name="displayName" :image="avatar" class="mr-4" />
                            <span>
                                <div
                                    class="text-xs font-semibold"
                                    :class="{'cursor-pointer': signingUp.resource.type === 'PERSONAL'}"
                                >
                                    <span class="flex items-center gap-1">
                                        {{ displayName }}
                                        <UiIcon name="user" class="w-4 h-4 hidden md:block opacity-0 transition-opacity group-hover:opacity-100" />
                                    </span>
                                </div>
                                <div class="text-grey-800 text-xs font-light">{{ organisation }}</div>
                                <span
                                    v-if="signingUp.dateOfResponse"
                                    class="text-grey-500 font-light"
                                >
                                    gemeldet am {{ $formatDateTime(signingUp.dateOfResponse) }}
                                </span>
                            </span>
                        </span>
                    </div>
                    <div class="flex content-start pointer-events-none">
                        <EventPlanningSigningUpChangeStatus
                            :signing-up="signingUp"
                            :event="event"
                            class="aria-disabled:grayscale">
                            <template #default="{ status }">
                                <EventSigningUpStatus data-size="lg" data-no-label :status="status" />
                            </template>
                        </EventPlanningSigningUpChangeStatus>

                        <button :disabled="true" class="flex flex-wrap h-min form-button button-sm rounded-md px-1 !opacity-100">
                            <EventAssignmentStatus class="min-w-[24px]" :value="assignment" data-size="lg" data-no-label />
                        </button>
                    </div>
                </div>
            </div>
        </EventPlanningSigningUpProvider>
    </div>

</template>

<script lang="ts" setup>
import { ViewMemberData } from 'composables/members';


    const props = defineProps<{
        signingUp: ApiModel<'SigningUp'>
        event: ApiModel<'Event'>
    }>()

    const memberOrExternalPerson = computed<ApiModel<'MemberData'> | ApiModel<'ExternalPerson'>>(()=>{
        return props.signingUp.resource.external ? props.signingUp.resource.externalPerson: props.signingUp.resource.member
    })

    const  { openMemberDataDialog }  = inject<ViewMemberData>('viewMemberData')

</script>

<style lang="pcss" scoped>

    .signing-up-item {
        @apply flex items-center justify-between rounded-md border border-slate-100 p-3 shadow-sm transition-colors bg-white;
    }

</style>
