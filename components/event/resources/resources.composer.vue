<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel">
        <template #title>Meldungen zum Ereignis</template>
        <div v-if="isRevealed" class="w-full">
            <div class="relative flex h-full flex-col bg-white">
                <div class="flex flex-1 flex-col sm:px-6">
                    <TabGroup :selected-index="mode" @change="changeMode">
                        <TabList class="mb-8 flex">
                            <Tab class="tab">Personal</Tab>
                            <Tab class="tab">Material</Tab>
                        </TabList>
                        <TabPanels>
                            <TabPanel>
                                <EventResourcesSigningUpList resource-type="PERSONAL" :event="event"/>
                            </TabPanel>
                            <TabPanel>
                                <EventResourcesSigningUpList resource-type="TECHNIC" :event="event"/>
                            </TabPanel>
                        </TabPanels>
                    </TabGroup>
                </div>
            </div>
        </div>

        <template #footer>
            <button class="form-button button-contained" @click="cancel">Schl<PERSON>ßen</button>
        </template>
    </UiComposer>

</template>

<script lang="ts" setup>

    import { DialogController } from '~~/composables/dialog-controller'
    import { TabGroup, TabList, Tab, TabPanels, TabPanel } from '@headlessui/vue'

     const props = defineProps<{
        controller: DialogController<'viewResources'>
        event: ApiModel<'Event'>

    }>()

    const { isRevealed, cancel} = props.controller

    /**
     * The available tabs
     */
    enum Mode {
        PERSONAL = 0,
        TECHNIC = 1
    }

    /**
     * We show the "Personal hinzufügen" tab by default
     */
    const mode = ref<Mode>(Mode.PERSONAL)

    /**
     * Control which tab is active
     */
    function changeMode(value: Mode) {
        mode.value = value
    }

</script>

<style scoped lang="pcss">
    .tab {
        @apply px-4 w-1/2 py-2 text-center !mr-1 text-sm font-normal border-b border-b-sky-200 text-sky-500 whitespace-nowrap hover:border-b-sky-500;
        @apply ui-selected:font-medium ui-selected:border-b-2 ui-selected:border-b-sky-500
    }
</style>
