<template>
    <div>
        <div class="grid grid-cols-2 gap-2 sm:flex sm:items-center mb-4 leading-4">
            <UiLabel size="xs" :is-active="resourceFilter === null" clickable @click="setFilters(null, false)">
                Alle
            </UiLabel>

            <UiLabel size="xs" :is-active="resourceFilter === true" clickable @click="setFilters(true, false)"> Zugeordnet </UiLabel>

            <UiLabel size="xs" :is-active="resourceFilter === false" clickable @click="setFilters(false, false)"> Nicht zugeordnet </UiLabel>

            <UiMenu button-size="xs" class="sm:ml-auto col-span-1">
                <template #button>
                    <UiIcon :name="sorting.icon" class="w-3 h-auto" />
                    {{ sorting.label }}
                </template>
                <UiMenuItem v-for="{ sortKey, label } in sorting.alternatives" :key="sortKey" @click="selectedSortKey = sortKey">
                    {{ label }}
                </UiMenuItem>
            </UiMenu>
        </div>
        <UiLoader :is-loading="!isFetched" class="mt-8">
            <div v-if="signingUps.length > 0">
                <TransitionGroup tag="div" name="list" class="flex flex-col gap-2 mb-2 pr-3 overflow-x-hidden overflow-y-hidden">
                    <template v-for="signingUp in sortedSigningUps" :key="signingUp._uuid">
                        <div>
                            <slot v-bind="{ signingUp }">
                                <EventResourcesSigningUpItem as="div" :signing-up="signingUp" :event="event"/>
                            </slot>
                        </div>
                    </template>
                </TransitionGroup>
            </div>
        </UiLoader>
    </div>
</template>

<script lang="ts" setup>
    import orderBy from 'just-order-by'

    const props = defineProps<{
        resourceType: ApiModel<'Resource'>['type']
        event: ApiModel<'Event'>
    }>()

    const eventQueries = useEventQueries(props.event.id)

    const { data , isFetched } = eventQueries.signingUps()

    function signingUpsByResourceType(data: Ref<ApiModel<'SigningUp'>[]>, type: MaybeRef<ApiModel<'Resource'>['type']>) {
        return computed(() => {
            return data.value?.filter(({ resource }) => resource.type === unref(type)) || []
        })
    }

    const signingUps = signingUpsByResourceType(data, props.resourceType)

    /* Filter Rescources (Persons) by state */

    const resourceFilter = ref<boolean>(null)

    function setFilters(resource: boolean | null, favorite: boolean | null) {
        resourceFilter.value = resource

    }

    const filteredSigningUps = computed(() => {
        if (resourceFilter.value !== null) {
            return signingUps.value?.filter((signingUp) => signingUp.isAssigned === resourceFilter.value) || []
        }

        return signingUps.value || []
    })

    /* Sorting signingUps */

    const { retrieveStatus } = useSigningUpStates()

    const { $resourceName } = useNuxtApp()

    const sortingTypes = {
        'a-z': { label: 'Name A-Z', property: 'alphabetical', direction: 'asc' },
        'z-a': { label: 'Name Z-A', property: 'alphabetical', direction: 'desc' },
        'attendance': { label: 'Rückmeldung', property: 'attendance', direction: 'desc' }
    } as const

    type SortKey = keyof typeof sortingTypes

    // We have different sort options depending on resource type
    const allowedSortOptions = computed(() => {
        switch (props.resourceType) {
            case 'TECHNIC': {
                return ['a-z', 'z-a'] as const
            }

            case 'PERSONAL': {
                return ['attendance', 'a-z', 'z-a'] as const
            }
        }
    })

    // This is the variable that contains the actual user input
    // On startup it'll be set to the first entry of allowed sortOptions
    const selectedSortKey = ref<SortKey>(allowedSortOptions.value[0])

    // This contains all data needed in template
    const sorting = computed(() => {
        const sorter = sortingTypes[selectedSortKey.value]

        // "Calculate" icon name
        const icon = sorter.direction === 'asc' ? ('sort-ascending' as const) : ('sort-descending' as const)

        // Collect all other available options
        const alternatives = allowedSortOptions.value
            .filter((sortKey) => sortKey !== selectedSortKey.value)
            .map((sortKey) => {
                const sortOption = sortingTypes[sortKey]

                return {
                    ...sortOption,
                    sortKey
                }
            })

        return {
            ...sorter,
            icon,
            alternatives
        }
    })

    const sortedSigningUps = computed(() => {
        return orderBy(filteredSigningUps.value, [
            {
                property(signingUp) {
                    switch (sorting.value.property) {
                        case 'alphabetical': {
                            return $resourceName(signingUp.resource)
                        }

                        case 'attendance': {
                            // We'll have EventSignungUpStatus wich is a "numeric" enum
                            return retrieveStatus(signingUp)
                        }
                    }
                },
                order: sorting.value.direction
            }
        ])
    })
</script>

<style lang="pcss" scoped>
    .list-move,  .list-enter-active, .list-leave-active {
        transition: all 0.3s ease;
    }

    .list-enter-from, .list-leave-to {
        opacity: 0;
        transform: translateX(16px);
    }

    /* ensure leaving items are taken out of layout flow so that moving
        animations can be calculated correctly. */
    .list-leave-active {
        position: absolute;
        display: none;
    }
</style>
