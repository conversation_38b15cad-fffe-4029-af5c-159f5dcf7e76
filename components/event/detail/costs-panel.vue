<template>
    <div class="group">
        <h3 class="mb-4 text-base">
            Übernachtungen und Kosten
            <button @click="reveal" class="xl:opacity-0 group-hover:opacity-100 focus:opacity-100">
                <UiIcon @click="reveal" name="pencil-alt" v-if="canManageData && !isRevealed" class="edit-icon" />
            </button>
        </h3>

        <div v-if="isRevealed">
            <div class="mb-4">
                <UiSwitch v-model="eventModel.hasNightQuarters" class="mb-4" label="Übernachtung verfügbar" />

                <fieldset v-if="eventModel.hasNightQuarters" class="mb-6 flex flex-col md:grid w-full md:grid-cols-5 gap-4">
                    <legend class="text-grey-600 col-span-4 mb-1 w-full text-xs">Benötigte Übernachtungen</legend>

                    <div class="number-input">
                        <label for="requiredFemaleQuarters">W</label>
                        <input type="number" v-model.number="eventModel.requiredFemaleQuarters" id="requiredFemaleQuarters" min="0" max="99" />
                    </div>

                    <div class="number-input">
                        <label for="requiredMaleQuarters">M</label>
                        <input type="number" v-model.number="eventModel.requiredMaleQuarters" id="requiredMaleQuarters" min="0" max="99" />
                    </div>

                    <div class="number-input">
                        <label for="requiredDiverseQuarters">D</label>
                        <input type="number" v-model.number="eventModel.requiredDiverseQuarters" id="requiredDiverseQuarters" min="0" max="99" />
                    </div>

                    <div class="number-input">
                        <label for="requiredNoinfoQuarters">&mdash;</label>
                        <input type="number" v-model.number="eventModel.requiredNoinfoQuarters" id="requiredNoinfoQuarters" min="0" max="99" />
                    </div>

                    <div class="flex items-center justify-center bg-gray-50 text-sm w-fit p-4 md:p-0 md:w-full">Gesamt: {{ totalNightQuarters }}</div>
                </fieldset>
            </div>

            <div class="mb-4">
                <UiSwitch v-model="eventModel.withCosts" label="Teilnahme kostenpflichtig" class="mb-8 md:mb-0" />

                <div v-if="eventModel.withCosts" class="mt-2 flex w-full items-center justify-start gap-x-4">
                    <label class="flex items-center gap-x-2 text-sm">
                        <input type="radio" class="form-radio" v-model="eventModel.reimbursementOfCosts" :value="true" /> mit Kostenerstattung
                    </label>

                    <label class="flex items-center gap-x-2 text-sm">
                        <input type="radio" class="form-radio" v-model="eventModel.reimbursementOfCosts" :value="false" /> ohne Kostenerstattung
                    </label>
                </div>
            </div>

            <div class="flex items-center justify-end gap-x-2">
                 <button @click="cancel" class="form-button button-contained-secondary button-sm">Abbrechen</button>
                <button @click="confirm" class="form-button button-contained button-sm">Speichern</button>
            </div>
        </div>

        <div v-else class="grid w-full grid-cols-1 sm:grid-cols-2 gap-4">
            <UiFormField label="Übernachtungen verfügbar">
                <span class="text-sm text-gray-600">
                    {{ displayObject.hasNightQuarters ? `${totalNightQuarters} insgesamt` : 'Nein' }}
                </span>
            </UiFormField>

            <UiFormField label="Teilnahme kostenpflichtig">
                <span v-if="displayObject.withCosts" class="text-sm text-gray-600">
                    <template v-if="displayObject.reimbursementOfCosts"> Ja. Kosten werden erstattet </template>
                    <template v-else> Ja. Kosten werden <span class="underline decoration-blue-500">nicht</span> erstattet </template>
                </span>
                <span v-else class="text-sm text-gray-600"> Nein </span>
            </UiFormField>
        </div>
    </div>
</template>

<script lang="ts" setup>
    import { useConfirmDialog, syncRef } from '@vueuse/core'
    import { pick } from 'lodash-es'

    const { canManageData } = inject(EventPermissionsKey)

    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const eventModel = ref<ApiModel<'Event'>>(null)

    syncRef(toRef(props, 'event'), eventModel, {
        direction: 'ltr',
        transform: {
            ltr: (event) => useModelFactory('Event').fromJson(JSON.parse(JSON.stringify(event)) as object)
        }
    })

    const { reveal, confirm, cancel, isRevealed, onConfirm } = useConfirmDialog()

    const commit = useCommitEventMutation(props.event)

    onConfirm(() => {
        commit('updateBasicData', eventModel.value)
    })

    const displayObject = computed(() => {
        // Switch between prop and model based on current status
        return isRevealed.value ? eventModel.value : props.event
    })

    // Typesave pick
    type NightQuarterProp = 'requiredFemaleQuarters' | 'requiredMaleQuarters' | 'requiredDiverseQuarters' | 'requiredNoinfoQuarters'

    const totalNightQuarters = computed(() => {
        const numbers = Object.values(
            pick<Record<NightQuarterProp, number>>(displayObject.value, [
                'requiredFemaleQuarters',
                'requiredMaleQuarters',
                'requiredDiverseQuarters',
                'requiredNoinfoQuarters'
            ])
        )

        return numbers.reduce((total, current) => {
            total += typeof current === 'number' ? current : 0
            return total
        }, 0)
    })
</script>

<style lang="pcss" scoped>
    input[type='number']::-webkit-outer-spin-button,
    input[type='number']::-webkit-inner-spin-button,
    input[type='number'] {
        appearance: none;
        -webkit-appearance: none;
        margin: 0;
        -moz-appearance: textfield !important;
    }

    .edit-icon {
        @apply -mt-1 inline h-6 w-6 cursor-pointer text-blue-500
    }

    .number-input {
        @apply form-input flex items-center gap-1 focus-within:bg-blue-50;

        input[type=number] {
            @apply w-full pl-2 text-center outline-none bg-transparent
        }
    }
</style>
