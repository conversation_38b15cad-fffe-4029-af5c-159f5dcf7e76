<template>
    <div class="group">
        <h3 class="mb-4 text-base">
            Anmerkungen
            <button @click="reveal" class="xl:opacity-0 group-hover:opacity-100 focus:opacity-100">
                <UiIcon
                    @click="reveal"
                    name="pencil-alt"
                    v-if="canManageData && !isRevealed"
                    class="-mt-1 inline h-6 w-6 cursor-pointer text-blue-500" />
            </button>
        </h3>

        <div class="grid w-full grid-cols-1 sm:grid-cols-2 gap-4">
            <UiFormField class="col-span-2 sm:col-span-1">
                <template #label>
                    <span class="flex items-center">
                        Allgemeine Anmerkungen
                        <UiTooltip content="Was du hier einträgst, steht später unter „Dienstnachweise“ in den Personalakten." />
                    </span>
                </template>

                <textarea v-if="isRevealed" v-model="eventModel.remark" class="form-textarea w-full" />
                <span v-else class="mt-2 block whitespace-pre-line break-words text-sm text-gray-600">{{
                    event.remark || 'Nichts eingetragen'
                }}</span>
            </UiFormField>

            <UiFormField class="col-span-2 sm:col-span-1">
                <template #label>
                    <span class="flex items-center">
                        Interne Bemerkungen
                        <UiTooltip content="Nur sichtbar für Ereignismanager*innen und Ereignisverantwortliche." />
                    </span>
                </template>

                <textarea v-if="isRevealed" v-model="eventModel.internalEventRemark" class="form-textarea w-full" />
                <span v-else class="mt-2 block whitespace-pre-line break-words text-sm text-gray-600">{{
                    event.internalEventRemark || 'Nichts eingetragen'
                }}</span>
            </UiFormField>

            <div v-if="isRevealed" class="col-span-2 flex items-center justify-end gap-x-2">
                <button @click="cancel" class="form-button button-contained-secondary button-sm">Abbrechen</button>
                <button @click="confirm" class="form-button button-contained button-sm">Speichern</button>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
    import { useConfirmDialog, syncRef } from '@vueuse/core'

    const { canManageData } = inject(EventPermissionsKey)

    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const eventModel = ref<ApiModel<'Event'>>(null)

    syncRef(toRef(props, 'event'), eventModel, {
        direction: 'ltr',
        transform: {
            ltr: (event) => useModelFactory('Event').fromJson(JSON.parse(JSON.stringify(event)) as object)
        }
    })

    const { reveal, confirm, cancel, isRevealed, onConfirm } = useConfirmDialog()
    const commit = useCommitEventMutation(props.event)

    onConfirm(() => {
        commit('updateBasicData', eventModel.value)
    })
</script>
