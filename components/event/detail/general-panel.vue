<template>
    <div class="group">
        <h3 class="mb-4 text-base">
            Details
            <button @click="reveal" class="xl:opacity-0 group-hover:opacity-100 focus:opacity-100">
                <UiIcon
                    name="pencil-alt"
                    v-if="canManageData && !isFormEnabled"
                    class="-mt-1 inline h-6 w-6 cursor-pointer text-blue-500"
                />
            </button>
        </h3>

        <div class="grid grid-cols-2 gap-4">
            <UiFormField label="Treffpunkt: Wann?" class="col-span-2 md:col-span-1">
                <UiDateTimePicker v-if="isFormEnabled" v-model="eventModel.meetingpointTime" class="w-full" />
                <span v-else class="text-sm text-gray-600">
                    {{ $formatDateWithAbbreviatedDayAndTime(event.meetingpointTime) || 'Nichts eingetragen' }}
                </span>
            </UiFormField>

            <UiFormField label="Treffpunkt: Wo?" class="col-span-2 md:col-span-1">
                <input v-if="isFormEnabled" type="text" v-model="eventModel.meetingpoint" class="form-input w-full" />
                <span v-else class="block whitespace-pre-line break-words text-sm text-gray-600">{{
                    event.meetingpoint || 'Nichts eingetragen'
                }}</span>
            </UiFormField>

            <UiFormField label="Kleiderordnung" class="col-span-2 md:col-span-1">
                <UiUniversalSelect
                    v-if="isFormEnabled"
                    v-model="dresscodesModel"
                    :multiple="true"
                    :options="dresscodeOptions"
                    :is-loading="isFetchingDresscodes"
                    label-prop="value2" />

                <span v-else class="text-sm text-gray-600">
                    <template v-if="!!dresscodes?.length">
                        {{ dresscodes?.map(({ value2 }) => value2).join(', ') }}
                    </template>
                    <template v-else>Nichts eingetragen</template>
                </span>
            </UiFormField>

            <UiFormField label="Verpflegung" class="col-span-2 md:col-span-1">
                <UiUniversalSelect
                    v-if="isFormEnabled"
                    v-model="cateringsModel"
                    :multiple="true"
                    :options="cateringOptions"
                    :is-loading="isFetchingCaterings"
                    label-prop="value2"
                    @addValue="createNewEventTag"
                />

                <span v-else class="text-sm text-gray-600">
                    <template v-if="!!caterings?.length">
                        {{ caterings?.map(({ value2 }) => value2).join(', ') }}
                    </template>
                    <template v-else>Nichts eingetragen</template>
                </span>
            </UiFormField>

            <UiFormField label="Ereignis-Nummer">
                <input v-if="isFormEnabled" type="text" :value="event.id" class="form-input w-full" disabled />
                <span v-else class="text-sm text-gray-600">{{ event.id || 'Nichts eingetragen' }}</span>
            </UiFormField>

            <UiFormField label="Kennzeichen">
                <input v-if="isFormEnabled" type="text" v-model="eventModel.number" class="form-input w-full" />
                <span v-else class="break-words text-sm text-gray-600">{{ event.number || 'Nichts eingetragen' }}</span>
            </UiFormField>

            <UiFormField label="Tags" class="col-span-2 md:col-span-1">
                <UiUniversalSelect
                    v-if="isFormEnabled"
                    v-model="eventTagsModel"
                    :multiple="true"
                    :options="tagsOptionsModel"
                    :is-loading="isFetchingTags"
                    label-prop="value2"
                    :withAddValue="true"
                    addValueLabel="Tag hinzufügen"
                    @addValue="createNewEventTag"
                />

                <span v-else class="text-sm text-gray-600">
                    <template v-if="!!eventTagsModel?.length">
                        {{ eventTagsModel?.map(({ value2 }) => value2).join(', ') }}
                    </template>
                    <template v-else>Nichts eingetragen</template>
                </span>
            </UiFormField>

            <div v-if="isFormEnabled" class="col-span-2 flex items-center justify-end gap-x-2">
                <button @click="cancel" class="form-button button-contained-secondary button-sm">Abbrechen</button>
                <button @click="confirm" class="form-button button-contained button-sm">Speichern</button>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
    import ListType from '~~/enums/listType'
    import { syncRef, useConfirmDialog } from '@vueuse/core'
    import { useCodeEntries } from '~/queries/code-entries'

    const { canManageData } = inject(EventPermissionsKey)

    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const { reveal, confirm, cancel, isRevealed: isFormEnabled, onConfirm, onReveal } = useConfirmDialog()

    const commit = useCommitEventMutation(props.event)

    const eventModel = ref<ApiModel<'Event'>>(null)
    const { data: tagsOptions, isFetching: isFetchingTags } = useCodeEntries(ListType.Tags)

    const eventTagsModel = ref<ApiModel<'CodeEntry'>[]>(props.event.eventTags)
    const tagsOptionsModel = ref<ApiModel<'CodeEntry'>[]>([])

    watch(tagsOptions, () => {
        tagsOptionsModel.value = tagsOptions.value
    })

    function createNewEventTag(name: string) {
        if(!name) return
        const newTag = useModelFactory('CodeEntry').create({
            id: Math.random(),
            listId: ListType.Tags,
            value2: name
        })
        tagsOptionsModel.value = [...tagsOptionsModel.value, newTag]
        eventTagsModel.value = [...eventTagsModel.value, newTag]
    }

    syncRef(toRef(props, 'event'), eventModel, {
        direction: 'ltr',
        transform: {
            ltr: (event) => useModelFactory('Event').fromJson(JSON.parse(JSON.stringify(event)) as object)
        }
    })

    const { data: dresscodes } = useEventQuery('dresscodes')
    const dresscodesModel = ref<ApiModel<'CodeEntry'>[]>(null)
    const { data: dresscodeOptions, isFetching: isFetchingDresscodes } = useCodeEntries(ListType.Dresscode)

    syncRef(dresscodes, dresscodesModel, {
        direction: 'ltr',
        transform: {
            ltr: (dresscodes) => [...dresscodes]
        }
    })

    const { data: caterings } = useEventQuery('caterings')
    const cateringsModel = ref<ApiModel<'CodeEntry'>[]>(null)
    const { data: cateringOptions, isFetching: isFetchingCaterings } = useCodeEntries(ListType.EventCatering)

    syncRef(caterings, cateringsModel, {
        direction: 'ltr',
        transform: {
            ltr: (caterings) => [...caterings]
        }
    })

    onConfirm(async () => {
        eventTagsModel.value = [...eventTagsModel.value.map(({value2}) => { return useModelFactory('CodeEntry').create({value2}) })]

        await commit('updateBasicData', eventModel.value)
        await commit('updateDresscodes', dresscodesModel.value)
        await commit('updateCaterings', cateringsModel.value)
        await commit('updateTags', eventTagsModel.value)
    })
</script>
