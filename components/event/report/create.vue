<template>
  <UiComposer :is-revealed="isRevealed" @cancel="cancel">
    <template #title>Listen & Übersichten</template>

    <form class="mb-4 flex w-full flex-col p-2">
      <div class="flex-1">
        <div class="mb-10">
          <h3 class="text-grey-900 mb-4 font-medium leading-4">Welche Liste oder Übersicht möchtest du erstellen?</h3>
          <UiListboxInput
            v-model="selectedReport"
            :options="ReportType"
            class="h-12"
          />
        </div>

        <div v-if="selectedReport?.value === 'PRINT_EVENTS'" class="mb-10">
          <h3 class="text-grey-900 mb-4 font-medium leading-4">Welche Informationen über das Ereignis möchtest du drucken?</h3>
          <UiListboxInput
            v-model="currentReportOption"
            :options="PrintType"
            class="h-12"
          />
        </div>

        <fieldset
          v-if="showReportOptions"
          class="mb-10 flex flex-col gap-y-4 pl-4"
        >
          <legend class="text-grey-900 mb-6 font-medium -ml-4 block leading-4">Wer soll auf der Liste stehen?</legend>
          <UiRadioInput v-for="reportSortOption in ReportSortOption"
          :key="reportSortOption.value" name="dataSource"
          :label="reportSortOption.label"
          v-model="currentReportSortOption"
          :value="reportSortOption.value"
          >
            <span class="text-sx">{{reportSortOption.note}}</span>
          </UiRadioInput>
        </fieldset>

        <div v-if="selectedReport?.value === 'SORTED_EXPORT'" class="mb-10">
          <h3 class="text-grey-900 mb-4 font-medium leading-4">Wie möchtest du die Registrierungen sortieren?</h3>
          <UiListboxInput
            v-model="currentRegistrationOrderedExport"
            :options="RegistrationOrderedExport"
            class="h-12"
          />
        </div>
      </div>

      <p v-if="showReportOptions" class="mb-6 font-medium ">In welchem Format möchtest du die Datei herunterladen?</p>
      <div class="flex gap-2" v-if="showReportOptions" >
        <button
          type="button"
          v-for="fileExt in fileExtensions"
          :key="fileExt"
          class="mb-2 mr-2 rounded-full bg-gray-50 px-2 py-1 text-sm text-gray-500 hover:cursor-pointer hover:bg-gray-200"
          @click="setFileType(fileExt)"
          :class="{ '!bg-gold-100': currentFileExtension === fileExt }"
        >
          {{ fileExt.toUpperCase() }}
      </button>
      </div>
    </form>

    <template #footer>
      <div class="flex w-full justify-end">
        <button class="form-button button-contained-secondary" @click="cancel">Abbrechen</button>
        <button class="form-button button-contained mr-6" :disabled="v$.$invalid" @click="confirm">
          Datei herunterladen
        </button>
      </div>
    </template>
  </UiComposer>
</template>


<script lang="ts" setup>
    import { DialogController } from '~~/composables/dialog-controller'
    import useVuelidate from '@vuelidate/core'
    import { required } from '@vuelidate/validators'
    import type { ReportOption } from '~/composables/report'

    const props = defineProps<{
      event: ApiModel<'Event'>
      controller: DialogController<'createPrintableReport'>
    }>()

    const PrintType: Option[] = [
      {id: 1, value: 'BASICS', label: 'Stammdaten'},
      {id: 2, value: 'POSTS', label: 'Stammdaten und Planstellenliste'},
      {id: 3, value: 'EMPTY_POSTS', label: 'Gesamte Planung ohne Ressourcen'},
      {id: 4, value: 'TOTAL', label: 'Gesamte Planung mit Ressourcen'}
    ]

    const RegistrationOrderedExport: Option[] = [
      {id: 1, value: 'PRESENT_FROM', label: 'nach Von-Zeit'},
      {id: 2, value: 'PRESENT_TO', label: 'nach Bis-Zeit'},
      {id: 3, value: 'FIRSTNAME', label: 'nach Vorname'},
      {id: 4, value: 'LASTNAME', label: 'nach Nachname'},
      {id: 5, value: 'ORGANISATION', label: 'nach Gliederung'}
    ]

    const ReportType: Option[] = [
      {id: 1, value: 'PRINT_EVENTS', label: 'Ereignis-Infos'},
      {id: 2, value: 'COMMUNICATION_LIST', label: 'Telefonnummern und E-Mail-Adressen'},
      {id: 3, value: 'NAME_LIST', label: 'Namensliste'},
      {id: 4, value: 'PARTICIPANTS_LIST', label: 'Teilnehmendenliste'},
      {id: 5, value: 'SORTED_EXPORT', label: 'Registrierungsliste'}
    ]

    const ReportSortOption = [
      {value: 'AVAILABLE', label: 'Alle gemeldeten', note: 'Liste mit allen Helfenden, die sich verfügbar oder teilweise verfügbar gemeldet haben'},
      {value: 'ASSIGNED', label: 'Alle zugeordneten', note: 'Liste mit allen Helfenden, die auf einer Planstelle stehen'},
      {value: 'REGISTERED', label: 'Alle registrierten',note: 'Liste mit allen Helfenden, die im Reiter „Registrierungen“ in der Tabelle stehen'}
    ]

    enum FileFormat {
      PDF = 'pdf',
      XLSX = 'xlsx',
      XLS = 'xls',
      CSV = 'csv'
    }

    const { getDocument } = useCreateReport()
    const { download } = useDownloadBase64()

    const { isRevealed, confirm: _confirm, cancel } = props.controller

    const selectedReport = ref<Option | null>(null)
    const currentReportOption = ref<Option | null>(null)
    const currentRegistrationOrderedExport = ref<Option | null>(null)
    const currentReportSortOption = ref<string>(null)

    const payload = computed<ReportOption>(() => {
      let fileOption: string | null = null

      if (selectedReport.value?.value === 'PRINT_EVENTS') {
        fileOption = currentReportOption.value?.value || null
      } else if (selectedReport.value?.value === 'SORTED_EXPORT') {
        fileOption = currentRegistrationOrderedExport.value?.value || currentReportSortOption.value
      } else if (showReportOptions.value) {
        fileOption = currentReportSortOption.value
      }

      return {
        fileType: selectedReport.value?.value || null,
        fileOption,
        fileExtension: currentFileExtension.value || 'pdf'
      }
    })

    watch(selectedReport, (value) => {
      currentReportOption.value = null
      currentReportSortOption.value = null

      if (value?.value === 'SORTED_EXPORT') {
        currentRegistrationOrderedExport.value = RegistrationOrderedExport[0]
      } else {
        currentRegistrationOrderedExport.value = null
      }
    })

    const showReportOptions = computed(() => {
      if(!selectedReport.value) return false
      return (selectedReport.value.value !== 'PRINT_EVENTS' && selectedReport.value.value !== 'SORTED_EXPORT')
    })



    const fileExtensions = Object.keys(FileFormat) as Array<keyof typeof FileFormat>;
    const currentFileExtension = ref<string>('PDF')

    function setFileType(fileExt: string) {
        currentFileExtension.value = fileExt
    }

    async function downloadDocument() {
      const document = await getDocument(props.event.id, payload.value)

      if (typeof document === 'string') {
        const fileName = selectedReport.value?.label
        download(document, `drkserver-Ereignis_${props.event.id}_${fileName}.${payload.value.fileExtension.toLowerCase()}`)
      }
    }

    const v$ = useVuelidate(
    {
        fileType: { required },
        fileOption: { required },
    },
       payload
    )

    async function confirm() {
        _confirm()
        await downloadDocument()
    }

</script>
