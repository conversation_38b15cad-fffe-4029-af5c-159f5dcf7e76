<template>
    <div
        class="flex p-4 shadow"
        :class="{
            'bg-gray-50': reportOption.value && !reportOption.fileType,
            'bg-gold-50': reportOption.value && reportOption.fileType
        }">
        <UiCheckInput :label="props.label" v-model="reportOption.value" />

        <div v-if="props.multiple && reportOption.value" class="ml-auto">
            <div class="flex" v-if="!reportOption.fileType">
                <div
                    v-for="fileExt in fileExtensions"
                    class="mr-2 rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-500 hover:cursor-pointer hover:bg-gray-200"
                    @click="setFileType(fileExt)"
                    :class="{ 'rounded-full bg-gray-200': reportOption.fileType === fileExt }">
                    {{ fileExt.toUpperCase() }}
                </div>
            </div>
            <div
                v-else
                class="hover:bg-gold-200 ml-auto rounded-full px-2 py-1 text-xs text-gray-500 hover:cursor-pointer"
                @click="setFileType(null)">
                {{ reportOption.fileType.toUpperCase() }}
            </div>
        </div>
        <div v-else-if="!props.multiple" class="ml-auto px-2 py-1 text-xs text-gray-500 hover:cursor-default">
            {{ reportOption.fileType.toUpperCase() }}
        </div>
    </div>
</template>

<script lang="ts" setup>
    import { useVModel } from '@vueuse/core'

    type ReportOption = {
        fileType: string
        value: boolean | null
    }

    type Props = {
        modelValue: ReportOption
        label: string
        multiple?: boolean
        customType?: string
    }

    type Emit = {
        (e: 'update:modelValue', value: string): void
    }

    const props = defineProps<Props>()

    const emit = defineEmits<Emit>()

    const reportOption = useVModel(props, 'modelValue', emit)

    const fileExtensions = ['pdf', 'xlsx', 'xls', 'csv']

    function setFileType(fileExt: string | null) {
        reportOption.value.fileType = fileExt
    }
</script>
