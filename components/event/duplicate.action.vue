<script lang="ts" setup>
    const props = defineProps<{
        event: ApiModel<'Event'>
        asMenuItem?: boolean
    }>()

    const { canDuplicate } = inject(EventPermissionsKey)

    async function triggerAction() {
        alert('Duplication has TBD')
    }
</script>

<template>
    <template v-if="canDuplicate">
        <UiMenuItem v-if="!!asMenuItem" icon="document-duplicate" @click="($event) => triggerAction()"> Duplizieren </UiMenuItem>

        <template v-else>
            <slot :on-trigger="triggerAction" />
        </template>
    </template>
</template>
