<template>
    <div v-if="sharedByMe || sharedWithMe" class="group mr-2 inline-flex" @mouseenter="showPopperHandler" @mouseleave="hidePopperHandler">
        <IconShare class="mr-2 h-6 w-6 text-gray-400" ref="iconElement" />
        <span v-if="withLabel">geteilt</span>
        <div
            v-if="!withLabel"
            class="pointer-events-none absolute z-10 rounded-lg bg-white p-2 text-sm text-gray-900 opacity-0 shadow-lg transition-opacity group-hover:opacity-100"
            :class="{ 'hidden': !popperIsShown, 'inline-block': popperIsShown }"
            ref="tooltipElement">
            <div v-if="sharedByMe">Get<PERSON>t von "meiner" Gliederung</div>
            <div v-if="sharedWithMe">Geteilt mit "meiner" Gliederung</div>
        </div>
    </div>
</template>

<script lang="ts" setup>
    import { PropType } from 'vue'

    const props = defineProps({
        event: {
            required: true,
            type: Object as PropType<ApiModel<'Event'>>
        },
        withLabel: {
            required: false,
            type: Boolean
        }
    })

    const sharedByMe = computed(() => {
        return props.event.shareRequests?.length > 0
    })

    const sharedWithMe = computed(() => {
        return props.event.shareRequestsReceived?.length > 0
    })

    const iconElement = ref<HTMLElement>(null)
    const tooltipElement = ref<HTMLElement>(null)
    const popperIsShown = ref(true)

    const popperInstance = usePopper(iconElement, tooltipElement, { placement: 'top' })

    const showPopperHandler = () => {
        popperIsShown.value = true
        popperInstance.value.update()
    }

    const hidePopperHandler = () => (popperIsShown.value = false)
</script>
