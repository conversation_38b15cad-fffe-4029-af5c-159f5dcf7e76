<template>
    <div class="flex flex-col sm:flex-row items-start gap-y-2 sm:gap-y-0 sm:items-center gap-x-4">
        <span class="font-light text-sky-500">{{ selectedEvents.length }} Ereignisse:</span>
        <button ref="calendarDownload" class="form-button button-outline button-xs">
            <IconCalendarOutlined class="" /> in den Kalender eintragen (ICS Export)
        </button>
    </div>
</template>

<script lang="ts" setup>
    const props = defineProps<{
        selectedEvents: ApiModel<'Event'>[]
    }>()

    const { fetch, name } = useICalendar(toRef(props, 'selectedEvents'))

    const { button: calendarDownload } = useDownloadButton(name, fetch)
</script>
