<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel" class="relative">
        <template #title> Extern Mitwirkende hinzufügen </template>
        <div class="mb-4 text-sm">
            <UiInfo
                message="Eine mündliche Vereinbarung oder ein schriftlicher Vertrag mit der externen Person genügen, um ihre Daten zu erheben. Grundlage ist Art. 6, Abs. 1b) der EU-DSGVO." />
        </div>
        <div class="mb-4 flex flex-col gap-4">
            <UiFormField label="Vorname *" class="col-span-2">
                <input type="text" v-model="person.firstname" class="form-input box-border w-full" />
            </UiFormField>

            <UiFormField label="Nachname *" class="col-span-2">
                <input type="text" v-model="person.lastname" class="form-input box-border w-full" />
            </UiFormField>

            <UiFormField label="Geburtstag *">
                <UiDatePicker v-model="person.birthday" />
            </UiFormField>

            <fieldset class="flex flex-col gap-y-3">
                <div class="flex-1">
                    <UiFormField label="Anwesend von: *">
                        <UiDateTimePicker
                            v-model="start"
                            :disabled="setDateFromNow"
                            class="w-full"
                            :class="{ 'border-red-500': validation$.start.$error }" />
                        <UiCheckInputReset label="kommt jetzt" v-model="setDateFromNow" class="mt-2" />
                    </UiFormField>
                </div>
                <div class="flex-1">
                    <UiFormField label="Anwesend bis: *">
                        <UiDateTimePicker
                            v-model="end"
                            :disabled="setDateUntilNow"
                            class="w-full"
                            :class="{ 'border-red-500': validation$.end.$error }" />
                        <UiCheckInputReset label="geht jetzt" v-model="setDateUntilNow" class="mt-2" />
                    </UiFormField>
                </div>
            </fieldset>
            <p v-if="!isStartLessThenEnd" class="mb-4 text-sm font-light text-red-500">Das Enddatum muss nach dem Startdatum liegen</p>
            <p v-else class="mb-4 text-sm font-light">Dauer: {{ $formatDuration(start, end) || 'unbekannt' }}</p>

            <UiFormField label="Geschlecht" class="col-span-2">
                <UiUniversalSelect :options="genderOptions" labelProp="label" v-model="selectedGender" />
            </UiFormField>

            <UiFormField label="Nationalität">
                <input type="text" v-model="person.nationality" class="form-input box-border w-full" />
            </UiFormField>

            <UiFormField label="Straße, Hausnummer">
                <input type="text" v-model="person.street" class="form-input box-border w-full" />
            </UiFormField>

            <UiFormField label="Postleitzahl">
                <input type="text" v-model="person.zipCode" class="form-input box-border w-full" />
            </UiFormField>

            <UiFormField label="Wohnort">
                <input type="text" v-model="person.city" class="form-input box-border w-full" />
            </UiFormField>

            <UiFormField label="Mobiltelefon">
                <input
                    type="text"
                    v-model="validation$.telephone.$model"
                    class="form-input box-border w-full"
                    :class="{ 'border-red-500': validation$.telephone.$error }" />
            </UiFormField>

            <UiFormField label="E-Mail">
                <input
                    type="email"
                    v-model="validation$.mail.$model"
                    class="form-input box-border w-full"
                    :class="{ 'border-red-500': validation$.mail.$error }" />
            </UiFormField>

            <UiFormField label="Kreisverband oder Gliederung">
                <input type="text" class="form-input box-border w-full" v-model="person.districtBranch" />
            </UiFormField>

            <UiFormField label="Organisation / Gemeinschaft">
                <input type="text" class="form-input box-border w-full" v-model="person.redCrossUnit" />
            </UiFormField>

            <UiFormField label="Einsatzformation">
                <input type="text" class="form-input box-border w-full" v-model="person.disasterPreparednessUnit" />
            </UiFormField>
        </div>

        <template #footer>
            <button @click="cancel" class="form-button button-contained-secondary">Abbrechen</button>
            <button :disabled="validation$.$invalid" class="form-button button-contained" @click="submit">Person hinzufügen</button>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import { DialogController } from '~~/composables/dialog-controller'
    import useVuelidate from '@vuelidate/core'
    import { required, email, helpers } from '@vuelidate/validators'
    import { Gender } from '~~/enums/gender'
    import { isBefore, isEqual, isExists } from 'date-fns'

    const props = defineProps<{
        controller: DialogController<'registerExternalPerson'>
    }>()

    const { isRevealed, onReveal, cancel, confirm } = props.controller

    const person = ref<ApiModel<'RegistrationCard'>>()

    const genderOptions = Object.keys(Gender).map((key) => ({
        id: key,
        label: Gender[key as keyof typeof Gender]
    }))

    const selectedGender = ref(null)

    watchEffect(() => {
        if (selectedGender.value) {
            person.value.gender = selectedGender.value.id
        }
    })

    const start = ref<Date | null>(null)
    const end = ref<Date | null>(null)

    const isDateFromNow = ref<boolean>(false)
    const isDateUntilNow = ref<boolean>(false)

    onReveal(() => {
        person.value = useModelFactory('RegistrationCard').create({
            lastname: '',
            telephone: '',
            mail: '',
            birthday: null,
            gender: Gender['Keine Angabe'],
            zipCode: '',
            nationality: '',
            street: '',
            city: '',
            districtBranch: '',
            redCrossUnit: '',
            placeOfAction: '',
            disasterPreparednessUnit: '',
            startOfAction: null,
            endOfAction: null
        });
        (start.value = null), (end.value = null)
        isDateFromNow.value = false
        isDateUntilNow.value = false

    })

    const setDateFromNow = ref(false)
    watch(setDateFromNow , (value) => {
        if (value) {
            start.value = new Date()
        } else {
            start.value = null
        }
        isDateFromNow.value = null
    })

    const setDateUntilNow = ref(false)
    watch(setDateUntilNow, (value) => {
        if (value) {
            end.value = new Date()
        } else {
            end.value = null
        }
        isDateUntilNow.value = null
    })

    function floorToMinute(date: Date) {
        const newDate = new Date(date)
        newDate.setSeconds(0, 0) // Set seconds and milliseconds to zero
        return newDate
    }

    const isStartLessThenEnd = computed<boolean>(() => {
        const startDate = floorToMinute(new Date(start.value))
        const endDate = floorToMinute(new Date(end.value))
        if (!!end.value && !!start.value) {
            return startDate.getTime() < endDate.getTime()
        } else {
            return true
        }
    })

    const validatePhoneNumber = (value: string) => {
        const tester = /^[+]*[(]{0,1}[0-9]{1,3}[)]{0,1}[-.\s/0-9]*$/g
        return !helpers.req(value) || tester.test(value)
    }

    const atLeastOneDate = helpers.withMessage('Either start or end date must be provided.', () => {
        return !!start.value || !!end.value
    })

    const isDateBefore = helpers.withMessage('Start date must be earlier than end date.', () => isBefore(start.value, end.value))

    const validation = {
        firstname: { required },
        lastname: { required },
        birthday: { required },
        mail: { email },
        telephone: { validatePhoneNumber },
        start: { atLeastOneDate, isDateBefore },
        end: { atLeastOneDate }
    }

    const validation$ = useVuelidate<ApiModel<'RegistrationCard'>>(validation, person)

    function submit() {
        confirm({
            person: person.value,
            start: start.value,
            end: end.value
        })
    }
</script>
