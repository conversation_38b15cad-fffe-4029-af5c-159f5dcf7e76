<template>
  <div>
    <div class="grid grid-cols-1 gap-y-2 sm:gap-y-0 sm:grid-cols-5 mb-8">
        <h2 class="col-span-3 font-semibold">Wer darf registrieren?</h2>
        <div  class="col-span-2">
          <MemberPicker
            v-if="canManageRegistrators"
            @select="addRegistrator"
            :allowed-scopes="['MY_MEMBERS']"
            :exclude-members="legitimatePersons"
            :disabled="isLoading"
            placeholder="Person hinzufügen"
            popper-position="bottom-end"
            class="col-span-2 ui-disabled:opacity-25" />
        </div>
    </div>

    <div v-if="isLoading" class="flex min-h-[120px] w-full animate-pulse items-center justify-center bg-gray-50">
      <span class="inline-flex items-center font-light gap-x-2">
        <IconRefresh class="w-4 h-4 text-gray-500 animate-spin" />
        Personen werden geladen.
      </span>
    </div>

    <TransitionGroup v-else tag="div" name="list">
      <div v-for="member in legitimatePersons" :key="member._uuid" class="group flex items-center w-full px-4 py-2 mb-2 shadow relative">
        <span class="flex flex-row gap-1 hover:bg-blue-50">
          <ProvideMember :member="member" class="text-sm cursor-pointer" >
             <template #default="{ name, avatar, organisation }">
                <div class="flex items-center text-sm p-0.5">
                    <div class="group flex items-center text-sm cursor-pointer pointer-events-auto hover:bg-blue-50" @click="openMemberDataDialog(member)">
                        <UiAvatar :name="name" :image="avatar" size="base" class="mr-[0.5em]" />
                        <span class="leading-none flex flex-col">
                            <span class="flex flex-row items-center gap-1">
                                {{ name }} <UiIcon name="user" class="w-4 h-4 opacity-0 transition-opacity group-hover:opacity-100" /> <br />
                            </span>
                            <span v-text="organisation" class="text-xs text-gray-500" />
                        </span>
                    </div>
                </div>
            </template>
          </ProvideMember>
        </span>
        <button
          v-if="canManageRegistrators"
          @click="removeRegistrator(member)"
          class="ml-auto rounded-sm form-button button-sm disabled:opacity-75"
          title="Person entfernen"
          :disabled="deletionProcessingOn != null"
        >
          <UiIcon name="trash" class="w-4 h-4" />
        </button>
        <div class="absolute top-0 left-0 right-0 bottom-0 bg-blue-300 opacity-50 flex items-center justify-center"  v-if="deletionProcessingOn === member.id">
          <IconRefresh class="w-6 h-6 text-blue-500 animate-spin" />
        </div>
      </div>
      <div v-if="!legitimatePersons.length" class="pt-4 pb-12" key="___empty___">
        <span class="">Die Liste ist leer</span>
        <p v-if="canManageRegistrators" class="text-sm font-light">
          Klicke in das Feld über dieser Box und gib einen Namen ein. Diese Person darf Teilnehmende dieses Ereignisses ein- und ausbuchen.
          Du kannst beliebig viele Personen eintragen, die ein- und ausbuchen dürfen.
        </p>
        <p v-else class="text-sm font-light">
          Du kannst dieser Liste momentan keine Personen hinzufügen.
        </p>
      </div>
    </TransitionGroup>
  </div>
</template>

<script lang="ts" setup>
  import { ViewMemberData } from 'composables/members';

  const props = defineProps<{
    event: ApiModel<'Event'>
  }>()

  /**
   * Recieve the needed permissions
   */
  const { canManageRegistrators, canReadRegistrators } = inject(EventPermissionsKey)
  const deletionProcessingOn = ref<number | null>(null);

  /**
   * We'll need some commit actions in order to actually mutate data
   */
  const commit = useCommitEventMutation(props.event)

  const { data: legitimatePersons, isLoading } = useEventQuery('registrators')

  function addRegistrator(registrator: ApiModel<'MemberData'>) {
    commit('createRegistrator', registrator)
  }

  async function removeRegistrator(registrator: ApiModel<'MemberData'>) {
    deletionProcessingOn.value = registrator.id
    commit('removeRegistrator', registrator)
      .finally(
        () => deletionProcessingOn.value = null
      )
  }

  // const { dialog, isBackgroundVisible, reveal: openDialog } = openMemberDataDialog()
  const  { openMemberDataDialog }  = inject<ViewMemberData>('viewMemberData')

</script>

<style scoped lang="pcss">

    .list-move,  .list-enter-active, .list-leave-active {
        transition: all 0.3s ease;
    }

    .list-enter-from, .list-leave-to {
        opacity: 0;
        transform: translateX(16px);
    }

    /* ensure leaving items are taken out of layout flow so that moving
        animations can be calculated correctly. */
    .list-leave-active {
        position: absolute;
        display: none;
    }
</style>
