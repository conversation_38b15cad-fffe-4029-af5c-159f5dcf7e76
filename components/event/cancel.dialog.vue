<script lang="ts" setup>
    import { DialogController } from '~~/composables/dialog-controller'

    defineProps<{
        controller: DialogController<'cancelEvent'>
    }>()

    const data = ref({ reasonOfCancellation: null, remarkOfCancellation: null })
</script>

<template>
    <UiConfirmDialog
        title="Ereignis absagen"
        :controller="controller"
        confirm-with="Absagen"
        cancel-with="Abbrechen"
        :confirm-data="data"
        :is-confirm-disabled="!data.reasonOfCancellation"
        max-width-class="max-w-2xl">
        Du bist dabei, dieses Ereignis abzusagen. Das Ereignis kriegt dann den Status "abgesagt".
        Das sehen auch alle, die du zu dem Ereignis eingeladen hattest.
        Die Ressourcen verschwinden von den Planstellen. Du kannst das Ereignis nicht weiter bearbeiten.

        <UiFormField label="Grund der Absage *" class="my-4">
            <CodeEntrySelect
                v-model="data.reasonOfCancellation"
                list="EventCancelationReason"
                placeholder="W<PERSON>hle aus, warum du dieses Ereignis absagst." />
        </UiFormField>

        <UiFormField label="Anmerkung">
            <textarea
                rows="4"
                v-model="data.remarkOfCancellation"
                type="text"
                class="form-textarea box-border w-full"
                placeholder="Hier kannst du Details ergänzen, warum du das Ereignis absagst."></textarea>
        </UiFormField>
    </UiConfirmDialog>
</template>
