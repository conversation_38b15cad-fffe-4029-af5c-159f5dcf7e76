<template>
    <component :is="asComponent" class="h-full" :is-revealed="isRevealed" @cancel="cancel">
        <template #title> Verfügbaren Zeitraum bearbeiten </template>

        <div v-if="resource">
            <EventPlanningResourceProvider :resource="resource" v-slot="{ name, avatar, isCurrentUser }">
                <div class="mb-6 rounded-sm bg-gray-100 p-4">
                    <div class="mb-2 flex items-center gap-x-2 text-sm font-semibold text-gray-600">
                        <UiAvatar :name="name" :image="avatar" />
                        {{ name }}
                        <span v-if="isCurrentUser" class="bg-softred-100 rounded-md px-2 py-0.5 text-xs font-semibold text-red-600">Du</span>
                    </div>
                    <p class="text-sm">
                        kann beim <PERSON>reignis <span class="font-semibold text-gray-700">{{ event.extendedDescription }}</span> ({{
                            $formatDateRange(event.dateFrom, event.dateUpTo)
                        }}) nicht die ganze Zeit dabei sein. Gib hier die Kann-Zeiten an.
                    </p>
                </div>
            </EventPlanningResourceProvider>

            <h3 class="mb-2">Kann-Zeiten</h3>

            <UiIntervalPicker2 @select="addPeriod" :boundaries="boundaries" class="mb-6" />

            <TransitionGroup tag="div" name="list" class="mb-6 space-y-2">
                <span v-if="periods.length" key="___intro___" class="mt-4 block text-xs"> Die Ressource ist also in diesem Zeitraum verfügbar: </span>

                <span v-for="period in periods" :key="period.start.toISOString()" class="form-input flex items-center">
                    {{ $formatDateRange(period.start, period.end) }}
                    <button @click.prevent="removePeriod(period)" class="form-button button-sm ml-auto">
                        <UiIcon name="trash" class="h-4 w-4" />
                    </button>
                </span>

                <div v-if="!periods.length" key="___empty___" class="mt-4 block text-sm">
                    <h3 class="mb-2 font-semibold">Kein abweichender Zeitraum für Ressource angegeben</h3>
                    <p class="mb-4">
                        Das bedeutet, dass du diese Ressource für die <span class="underline decoration-blue-500">gesamte Dauer</span> des Ereignisses verplanst:
                    </p>
                    <span disabled="disabled" class="form-input pointer-events-none flex items-center">
                        {{ $formatDateRange(event.dateFrom, event.dateUpTo) }}
                    </span>
                </div>
            </TransitionGroup>
        </div>

        <template #footer>
            <button @click="cancel" class="form-button button-contained-secondary">Abbrechen</button>
            <button :disabled="false" class="form-button button-contained" @click="submit">Zeit übernehmen</button>
        </template>
    </component>
</template>

<script lang="ts" setup>
    import { UiBottomSheet, UiComposer } from '#components'
    import { type DialogController } from '~~/composables/dialog-controller'

    const props = defineProps<{
        controller: DialogController<'selectSigningUpPeriods'>
        event: ApiModel<'Event'>
        as?: 'bottom-sheet' | 'composer'
    }>()

    /**
     * This component can be run as 'bottom sheet' or 'composer'
     */
    const asComponent = computed(() => {
        const { as = 'composer' } = props
        if (as === 'bottom-sheet') {
            return UiBottomSheet
        } else {
            return UiComposer
        }
    })

    const { isRevealed, onReveal, cancel, confirm } = props.controller

    const periods = ref<ApiModel<'Period'>[]>([])
    const resource = shallowRef<ApiModel<'Resource'>>(null)

    function addPeriod(interval: { start: Date; end: Date }) {
        const merged = mergeIntervals(...periods.value, interval)
        periods.value = merged.map(({ start, end }) => {
            return useModelFactory('Period').create({
                start,
                end
            })
        })
    }

    function removePeriod(period: ApiModel<'Period'>) {
        periods.value = [...periods.value.filter(({ _uuid }) => _uuid !== period._uuid)]
    }

    onReveal((signingUp) => {
        resource.value = signingUp.resource
        periods.value = signingUp.periods || []
    })

    const boundaries = computed(() => {
        return { start: props.event.dateFrom, end: props.event.dateUpTo }
    })

    function submit() {
        confirm(unref(periods))
    }
</script>

<style lang="pcss" scoped>
    .list-move,  .list-enter-active, .list-leave-active {
        transition: all 0.3s ease;
    }

    .list-enter-from, .list-leave-to {
        opacity: 0;
        transform: translateX(16px);
    }

    /* ensure leaving items are taken out of layout flow so that moving
        animations can be calculated correctly. */
    .list-leave-active {
        position: absolute;
        display: none;
    }
</style>
