<template>
    <slot v-bind="{ icon, displayName, organisation, isAssigned, avatar, status, assignment, reportedAt, remark, accomodation, periods  }" />
</template>

<script lang="ts" setup>
import { UiIconName } from '~~/modules/ui'

    const props = defineProps<{
        signingUp: ApiModel<'SigningUp'>
    }>()

    const { $resourceName } = useNuxtApp()


    const { retrieveStatus, retrieveAssignment } = useSigningUpStates()

    /**
     * The name to display.
     *
     * Rules are defined in plugins/display.ts
     */
    const displayName = computed(() => $resourceName(props.signingUp.resource))

    /**
     * Show date of report .
     */
    const reportedAt = ref<Date>(props.signingUp.dateOfResponse)

    const remark = ref<string>(props.signingUp.remark)

    const accomodation = ref<boolean>(props.signingUp.requiredNightQuarters)

    const periods = ref<ApiModel<'Period'>[]>(props.signingUp.periods)

    const isAssigned = ref<boolean>(props.signingUp.isAssigned)
    /**
     * The organisation
     */
    const organisation = computed(() => {
        if (props.signingUp.resource.external && props.signingUp.resource.type === 'TECHNIC') {
            return 'Externes Material'
        } else if (props.signingUp.resource.external && props.signingUp.resource.type === 'PERSONAL') {
            return 'Extern Mitwirkende*r'
        } else if (props.signingUp.resource.type === 'TECHNIC') {
            return props.signingUp.sendingOrganisation?.name
        } else if (props.signingUp.resource.type === 'PERSONAL') {
            if(props.signingUp.resource.member) {
                return props.signingUp.resource.member.leadingOrganisation?.name
            } else {
                return null
            }
        }
    })

    /**
     * The avatar
     */
    const avatar = computed(() => {
        if (props.signingUp.resource.avatar) {
            return `data:image/png;base64, ${props.signingUp.resource.avatar}`
        }

        return null
    })

    const { getIcon, getSigningUpResourceType } = useSigningUpsIcons()

    /**
     * Retrieve icon by expected resource type
     */
    const icon = computed<UiIconName>(() => {
        if (props.signingUp.resource.type === 'PERSONAL') {
            return 'user-circle'
        }

        if (props.signingUp.resource.type === 'TECHNIC') {
            return getIcon(getSigningUpResourceType(props.signingUp))
        }

        return null
    })

    /**
     * The signing up status
     */
    const status = computed(() => {
        return retrieveStatus(props.signingUp)
    })

    /**
     * The assignment status
     */
    const assignment = computed(() => {
        return retrieveAssignment(props.signingUp)
    })
</script>
