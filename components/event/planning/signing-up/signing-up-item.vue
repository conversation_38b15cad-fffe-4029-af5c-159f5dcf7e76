<template>
    <div ref="draggableRef" class="signing-up-item">
        <EventPlanningSigningUpProvider :signing-up="signingUp" v-slot="{ icon, avatar, organisation, displayName, assignment }">
            <UiAvatar v-if="avatar && signingUp.resource.type === 'TECHNIC'"  :name="displayName" :image="avatar" class="mr-4" />
            <UiIcon v-if="!avatar && signingUp.resource.type === 'TECHNIC'" class="mr-4 h-6 w-6 min-w-[1.5rem] bg-brack" :name="icon" />

            <div class="flex-auto text-xs leading-tight">
                <div
                    class="flex flex-row justify-between mb-1"
                    :class="signingUp.resource.type === 'PERSONAL' ? 'group' : ''"
                >
                    <div
                        class="pr-1"
                        :class="signingUp.resource.type === 'PERSONAL' ? 'hover:bg-blue-50 hover:ring-blue-50 hover:ring-4' : ''"
                    >
                        <span
                            class="flex items-center gap-1"
                            @click="signingUp.resource.type === 'PERSONAL' ? openMemberDataDialog(memberOrExternalPerson) : null"
                        >
                            <UiAvatar v-if="signingUp.resource.type === 'PERSONAL'" :name="displayName" :image="avatar" class="mr-4" />
                            <span>
                                <div
                                    class="text-xs font-semibold"
                                    :class="{'cursor-pointer': signingUp.resource.type === 'PERSONAL'}"
                                >
                                    <span class="flex items-center gap-1">
                                        {{ displayName }}
                                        <UiIcon name="user" class="w-4 h-4 hidden md:block opacity-0 transition-opacity group-hover:opacity-100" />
                                    </span>
                                </div>
                                <div class="text-grey-800 text-xs font-light">{{ organisation }}</div>
                                <span
                                    v-if="signingUp.dateOfResponse"
                                    class="text-grey-500 font-light"
                                >
                                    gemeldet am {{ $formatDateTime(signingUp.dateOfResponse) }}
                                </span>
                            </span>
                        </span>
                    </div>
                    <div class="flex content-start">
                        <EventPlanningSigningUpChangeStatus
                            :signing-up="signingUp"
                            :event="event"
                            @update="(status) => changeSigningUpStatus(signingUp, status)"
                            class="aria-disabled:grayscale">
                            <template #default="{ status }">
                                <EventSigningUpStatus data-size="lg" data-no-label :status="status" />
                            </template>
                        </EventPlanningSigningUpChangeStatus>

                        <button :disabled="true" class="flex flex-wrap h-min form-button button-sm rounded-md px-1 !opacity-100">
                            <EventAssignmentStatus class="min-w-[24px]" :value="assignment" data-size="lg" data-no-label />
                        </button>

                        <button
                            v-if="signingUp.resource.type === 'TECHNIC'"
                            @click="toggleFavorite"
                            :disabled="!isFavorable || isFetching"
                            class="flex flex-wrap h-min content-start  form-button button-sm rounded-md px-1 disabled:grayscale">
                            <IconStar class="h-5 w-5 text-blue-600" :class="{ 'fill-current': isFavorite }" />
                        </button>

                        <UiMenu class="signing-up-item-menu ml-2">
                            <UiMenuItem v-if="signingUp.resource.externalArticle" @click="editExternalMaterial(signingUp)">Ressource bearbeiten</UiMenuItem>
                            <UiMenuItem @click="createEventPost(signingUp)">Neue Planstelle anlegen</UiMenuItem>
                            <UiMenuItem @click="selectEventPost(signingUp)">Bestehende Planstelle auswählen</UiMenuItem>
                            <UiMenuItem v-if="signingUp.isAssigned" @click="removeFromResourceSettings(signingUp)">Von allen Planstellen entfernen</UiMenuItem>
                            <UiMenuItem v-if="!signingUp.isAssigned && !signingUp.userStatus" @click="removeSigningUp(signingUp)">
                                Aus dieser Liste entfernen
                            </UiMenuItem>
                        </UiMenu>
                    </div>
                </div>
            </div>
        </EventPlanningSigningUpProvider>
    </div>

</template>

<script lang="ts" setup>
    import { ViewMemberData } from 'composables/members';

    const props = defineProps<{
        signingUp: ApiModel<'SigningUp'>
    }>()

    const memberOrExternalPerson = computed<ApiModel<'MemberData'> | ApiModel<'ExternalPerson'>>(()=>{
        return props.signingUp.resource.external ? props.signingUp.resource.externalPerson: props.signingUp.resource.member
    })

    const draggableRef = useDraggable(toRef(props, 'signingUp'))

    const { event } = inject(EventPlanningKey)

    const { removeFromResourceSettings } = inject(EventPlanningActions)

    const { createEventPost, removeSigningUp, selectEventPost, changeSigningUpStatus, editExternalMaterial } = inject(EventManageResourcesActions)

    const { addFavorite, removeFavorite, isFavoriteResource, getFavoriteResource, isFavorableResource, isFetching } = inject(FavoriteTechnicArticles)

    async function toggleFavorite() {
        const favorite = getFavoriteResource(props.signingUp.resource)

        if (favorite) {
            await removeFavorite(favorite)
        } else {
            await addFavorite(props.signingUp.resource.article)
        }
    }

    const isFavorite = computed(() => {
        return isFavoriteResource(props.signingUp.resource)
    })

    const isFavorable = computed(() => {
        return isFavorableResource(props.signingUp.resource)
    })

    // const {dialog, isBackgroundVisible, reveal: openDialog } = openMemberDataDialog()
    const  { openMemberDataDialog }  = inject<ViewMemberData>('viewMemberData')


</script>

<style lang="pcss" scoped>

    .signing-up-item {
        @apply flex items-center justify-between rounded-md border border-slate-100 p-3 shadow-sm transition-colors bg-white;
        @apply cursor-grab;

        &[data-ui-draggable-state~=active] {
            @apply animate-pulse;
            @apply cursor-grabbing;

            /* & .signing-up-item-menu {
                @apply invisible
            } */
        }

    }

    .dragging .hidden-dragging {
        @apply hidden;
    }
</style>
