<template>
    <UiMenu :aria-disabled="isDisabled" class="disabled:opacity-40 aria-disabled:pointer-events-none">
        <template #button>
            <slot name="default" v-bind="{ status }">
                <EventSigningUpStatus data-size="xl" :status="status" class="text-xs text-yellow-900" />
            </slot>
        </template>

        <UiMenuItem @click="emit('update', Status.AVAILABLE)" :disabled="status === Status.AVAILABLE" class="menu-item">
            <EventSigningUpStatus data-size="lg" :status="Status.AVAILABLE" />
        </UiMenuItem>

        <UiMenuItem @click="emit('update', Status.PARTIAL)" class="menu-item">
            <EventSigningUpStatus data-size="lg" :status="Status.PARTIAL" />
        </UiMenuItem>

        <UiMenuItem @click="emit('update', Status.UNAVAILABLE)" :disabled="status === Status.UNAVAILABLE" class="menu-item">
            <EventSigningUpStatus data-size="lg" :status="Status.UNAVAILABLE" />
        </UiMenuItem>
    </UiMenu>
</template>

<script lang="ts" setup>
    import Status from '~~/enums/event-signing-up-status'

    const props = defineProps<{
        signingUp: ApiModel<'SigningUp'>
        event: ApiModel<'Event'>
    }>()

    const emit = defineEmits<{
        (event: 'update', value: Status): any
    }>()

    const { retrieveStatus } = useSigningUpStates()

    const status = computed(() => {
        return retrieveStatus(props.signingUp)
    })

    const isDisabled = computed(() => {
        return props.signingUp.resource.external || ['FINISHED', 'CANCELED'].includes(props.event.status)
    })
</script>

<style lang="pcss" scoped>
    .menu-item {
        @apply ui-disabled:opacity-40 ui-disabled:italic text-xs text-yellow-900 ui-disabled:cursor-not-allowed
    }
</style>
