<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel" class="relative">
        <template #title>
            <EventPlanningEventPostProvider :event-post="eventPost" v-slot="{ icon }">
                <div class="flex items-center gap-2 text-sm">
                    <span class="flex items-center justify-center rounded-full border-2 border-white bg-gray-400 p-2">
                        <UiIcon class="w-6 min-w-[1.5rem] text-white" :name="icon" />
                    </span>
                    <div class="text-sm leading-tight">
                        <h3 class="mb-0 font-semibold">{{ eventPost.description }}</h3>
                        <span class="inline-block font-light italic text-gray-800">{{
                            $formatDateRange(eventPost.dateFrom, eventPost.dateUpTo)
                        }}</span>
                    </div>
                </div>
            </EventPlanningEventPostProvider>
        </template>

        <template v-if="isRevealed" >
            <p class="mb-6 text-sm font-light">Klicke auf die gewünschte Ressource. Mit Klick auf "Ressource hinzufügen" setzt du die Ressource auf diese Planstelle.</p>

            <EventPlanningSelectStuffPanel v-if="eventPost?.eventPostResourceType === 'PERSONAL'" as="div" class="mb-3" />
            <EventPlanningSelectMaterialPanel v-if="eventPost?.eventPostResourceType === 'TECHNIC'" as="div" class="mb-3" />

            <RadioGroup v-model="selected">
                <EventPlanningSigningUpList :resource-type="eventPost?.eventPostResourceType" v-slot="{ signingUp }">
                    <EventPlanningSigningUpProvider :signing-up="signingUp" v-slot="{icon, displayName, organisation, avatar, status, assignment }">
                        <div class="flex">
                            <RadioGroupOption as="div" class="group/option flex cursor-pointer items-center" :value="signingUp">
                                <div class="check-icon"></div>
                            </RadioGroupOption>
                            <div
                                class="signing-up-item flex-1"
                                :class="{'cursor-pointer' : signingUp.resource.type === 'PERSONAL'}"
                                @click="signingUp.resource.type === 'TECHNIC' ?
                                    null :
                                    signingUp.resource.external ?
                                        openMemberDataDialog(signingUp.resource.externalPerson) :
                                        openMemberDataDialog(signingUp.resource.member)">
                                <UiAvatar v-if="avatar && signingUp.resource.type === 'TECHNIC'"  :name="displayName" :image="avatar" class="mr-1" />
                                <UiIcon v-if="!avatar && signingUp.resource.type === 'TECHNIC'" class="mr-2 w-6 min-w-[1.5rem] bg-brack" :name="icon" />

                                <div
                                    class="mr-2 flex-auto flex-row text-sm leading-tight"
                                    :class="signingUp.resource.type === 'PERSONAL' ? 'group hover:bg-blue-50 hover:ring-blue-50 hover:ring-4' : ''"
                                >
                                    <span class="flex items-center gap-1">
                                        <UiAvatar v-if="signingUp.resource.type === 'PERSONAL'" :name="displayName" :image="avatar" class="mr-1" />
                                        <span>
                                            <div
                                                class="block text-sm font-semibold"
                                                :class="{'cursor-pointer' : signingUp.resource.type === 'PERSONAL'}"
                                            >
                                                <span class="flex items-center gap-1">
                                                    {{ displayName }} <UiIcon name="user" class="w-4 h-4 hidden md:block opacity-0 transition-opacity group-hover:opacity-100" />
                                                </span>
                                                <span class="text-grey-800 text-sm font-light italic">{{ organisation }}</span>
                                            </div>
                                            <span v-if="signingUp.dateOfResponse" class="text-grey-500 font-light">
                                                gemeldet am {{ $formatDate(signingUp.dateOfResponse) }}
                                            </span>
                                        </span>
                                    </span>
                                </div>

                                <button :disabled="true" class="form-button button-sm rounded-md px-1 !opacity-100">
                                    <EventSigningUpStatus data-size="lg" data-no-label :status="status" />
                                </button>

                                <button :disabled="true" class="form-button button-sm rounded-md px-1 !opacity-100">
                                    <EventAssignmentStatus :value="assignment" data-size="lg" data-no-label />
                                </button>
                            </div>
                        </div>
                    </EventPlanningSigningUpProvider>
                </EventPlanningSigningUpList>
            </RadioGroup>
        </template>

        <template #footer>
            <button @click="cancel" class="form-button button-contained-secondary">Abbrechen</button>
            <button class="form-button button-contained" @click="confirm(selected)" :disabled="!selected">Ressource hinzufügen</button>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import { RadioGroup, RadioGroupLabel, RadioGroupOption } from '@headlessui/vue'
    import { DialogController } from '~~/composables/dialog-controller'
    import { ViewMemberData } from 'composables/members';

    const props = defineProps<{
        controller: DialogController<'selectSigningUp'>
    }>()

    const { isRevealed, confirm, cancel, onReveal } = props.controller

    const eventPost = ref<ApiModel<'EventPost'>>(null)
    const selected = ref<ApiModel<'SigningUp'>>(null)

    onReveal((input) => {
        eventPost.value = input
        selected.value = null
    })

    const  { openMemberDataDialog }  = inject<ViewMemberData>('viewMemberData')
</script>

<style lang="pcss" scoped>
    .signing-up-item {
        @apply flex items-center justify-between rounded-md border border-slate-100 p-3 shadow-sm transition-colors bg-white;
        @apply group-data-[headlessui-state~=checked]/option:border-gray-500;
        @apply group-data-[headlessui-state~=checked]/option:shadow-md;

        &[data-headlessui-state~=checked] {
            @apply border-blue-500;
            @apply icon-check-circle-blue-500
        }

    }

    .check-icon {
        @apply mr-2 h-8 w-8 cursor-pointer items-center rounded-full border ;
        @apply bg-[length:1.5rem_1.5rem] bg-center hover:icon-check-gray-300;

        @apply group-data-[headlessui-state~=checked]/option:icon-check-gray-500
    }
</style>
