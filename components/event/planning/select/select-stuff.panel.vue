<template>
    <component :is="as">
        <UiFormField label="Personal hinzufügen">
            <MemberPicker
                @select="select"
                :full-width-panel="true"
                :allowed-scopes="['MY_MEMBERS']"
                :exclude-members="selectedMembers" />
        </UiFormField>
    </component>
</template>

<script lang="ts" setup>
    defineProps<{
        as: Object | HTMLElement | string
    }>()

    const { signingUpsByResourceType, commit } = inject(EventPlanningKey)

    const signingUps = signingUpsByResourceType('PERSONAL')

    const selectedMembers = computed(() => {
        return signingUps.value?.map((signingUp) => signingUp.resource?.member).filter((member) => !!member)
    })

    const { createSigningUpModel } = useSigningUpStates()

     async function createSigningUp(resource: ApiModel<'Resource'>) {
        await commit('createSigningUp', createSigningUpModel(resource))
    }

    async function select(member: ApiModel<'MemberData'>) {
        const resource = useModelFactory('Resource').create({
            external: false,
            type: 'PERSONAL',
            member
        })

        await createSigningUp(resource)
    }
</script>

<style scoped lang="pcss">
    .combobox {
        @apply relative
    }
    .combobox-input {
        @apply w-full form-input;
    }
    .combobox-options {
        @apply absolute z-30 w-full mt-1 overflow-auto text-base bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none;
    }

    .combobox-option {
        @apply relative py-2 pl-3 cursor-default select-none pr-9 text-grey-800 ui-active:bg-gray-50
    }
</style>
