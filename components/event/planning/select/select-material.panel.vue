<template>
    <component :is="as">
        <UiFormField label="Material hinzufügen">
            <MaterialPicker @select="select" :full-width-panel="true" :exclude-articles="selectedArticles" />
        </UiFormField>
    </component>
</template>

<script lang="ts" setup>
    defineProps<{
        as: Object | HTMLElement | string
    }>()

    const { signingUpsByResourceType, commit } = inject(EventPlanningKey)
    const signingUps = signingUpsByResourceType('TECHNIC')

    const selectedArticles = computed(() => {
        return signingUps.value?.map((signingUp) => signingUp.resource?.article).filter((article) => !!article)
    })

    const { createSigningUpModel } = useSigningUpStates()

    async function createSigningUp(resource: ApiModel<'Resource'>) {
        await commit('createSigningUp', createSigningUpModel(resource))
    }

    async function select(article: ApiModel<'TechnicArticle'>) {
        const resource = useModelFactory('Resource').create({
            external: false,
            type: 'TECHNIC',
            article
        })

        await createSigningUp(resource)
    }
</script>

<style scoped lang="pcss">
    .combobox {
        @apply relative
    }
    .combobox-input {
        @apply w-full form-input;
    }
    .combobox-options {
        @apply absolute z-30 w-full mt-1 overflow-auto text-base bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none;
    }

    .combobox-option {
        @apply relative py-2 pl-3 cursor-default select-none pr-9 text-grey-800 ui-active:bg-gray-50
    }
</style>
