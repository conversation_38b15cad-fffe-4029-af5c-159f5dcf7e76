<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel" class="relative">
        <template #title>
            <div class="flex items-center gap-2 text-sm">
                <h2 class="text-2xl text-red-500">Lieblingsmaterialien</h2>
            </div>
        </template>

        <template v-if="isRevealed">
            <h3>Eine Ressource auswählen</h3>
            <p class="mb-3 text-sm font-light">Die ausgewählte Ressource wird in die Materialliste verschoben.</p>

            <hr class="h-0 my-3 border-t-0 border-b" />

            <div v-if="allFavoriteMaterials.length > 0">
                <div v-for="favoriteMaterial in allFavoriteMaterials" class="flex cursor-pointer items-center my-2 group" :value="favoriteMaterial" :key="favoriteMaterial._uuid"  @click="toggleSelection(favoriteMaterial)">
                    <div class="check-icon" :class="{'icon-check-blue-500 ': isSelected(favoriteMaterial) }"></div>
                    <div class="signing-up-item flex-1 cursor-pointer shadow-md border" :class="{ 'border-blue-500': isSelected(favoriteMaterial) }">
                        <div class="mr-2 flex-auto text-sm leading-tight">
                            <div class="flex items-center gap-4 block text-sm font-semibold">
                                <UiIcon class="h-7 w-7 min-w-[25px] min-h-[25px]" :name="getIcon(favoriteMaterial.technicArticle.type.name)" />
                                <div class="flex flex-col gap-1">
                                    <span>{{ favoriteMaterial.technicArticle.identification }}</span>
                                    <span class="text-grey-800 text-sm font-light italic"> {{ favoriteMaterial.technicArticle.warehouse.name }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
             </div>
             <div v-else>
                 <p class="mb-6 text-sm font-light">Keine Lieblingsmaterialien.</p>
             </div>
        </template>

        <template #footer>
            <button @click="cancel" class="form-button button-contained-secondary">Abbrechen</button>
            <button class="form-button button-contained" @click="confirm(selectedElements)" :disabled="selected">Favoriten hinzufügen</button>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import { DialogController } from '~~/composables/dialog-controller'
    import { ref } from 'vue'

    const props = defineProps<{
        controller: DialogController<'managaFavoriteMaterials'>
    }>()

    const { isRevealed, confirm, cancel, onReveal } = props.controller

    const {data: allFavoriteMaterials }= useFavoriteTechnicArticles()

    const { getIcon } = useSigningUpsIcons()

    function toggleSelection(element: ApiModel<'FavoriteTechnicArticle'>) {
      const index = selectedElements.value.indexOf(element);
      if (index === -1) {
        selectedElements.value.push(element)
      } else {
        selectedElements.value.splice(index, 1);
      }
    }

    function isSelected(element: ApiModel<'FavoriteTechnicArticle'>) {
      return selectedElements.value.includes(element);
    }

    const selectedElements = ref(<ApiModel<'FavoriteTechnicArticle'>[]>([]));

    const selected = computed(()=> {
       return selectedElements.value.length === 0
    })

    onReveal(() => {
        selectedElements.value = []
    })


</script>

<style lang="pcss" scoped>
    .signing-up-item {
        @apply flex items-center justify-between rounded-md border p-3 shadow-sm transition-colors bg-white;
        @apply cursor-pointer;
    }

    .check-icon {
            @apply mr-2 h-8 w-8 cursor-pointer items-center rounded-full border ;
            @apply bg-[length:1.5rem_1.5rem] bg-center hover:icon-check-gray-300;
        }
</style>
