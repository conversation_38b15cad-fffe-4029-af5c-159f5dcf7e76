<template>
    <component :is="as">
        <UiFormField :label="formLabel">
            <MemberPicker
                v-if="type === 'PERSONAL'"
                @select="select"
                :exclude-members="selectedMembers"
                :full-width-panel="true"
                :allowed-scopes="['MY_MEMBERS', 'GLOBAL_MEMBERS']" />

            <MaterialPicker v-if="type === 'TECHNIC'" @select="select" :exclude-articles="selectedArticles" :full-width-panel="true" />
        </UiFormField>
    </component>
</template>

<script lang="ts" setup>
    const props = defineProps<{
        as: Object | HTMLElement | string
        type: string
        formLabel: string
    }>()

    const { signingUpsByResourceType, commit } = inject(EventPlanningKey)
    const signingUps = signingUpsByResourceType(props.type === 'PERSONAL' ? 'PERSONAL' : 'TECHNIC')

    const { createSigningUpModel } = useSigningUpStates()

    async function createSigningUp(resource: ApiModel<'Resource'>) {
        await commit('createSigningUp', createSigningUpModel(resource))
    }

    const selectedMembers = computed(() => {
        return signingUps.value?.map((signingUp) => signingUp.resource?.member).filter((member) => !!member)
    })

    const selectedArticles = computed(() => {
        return signingUps.value?.map((signingUp) => signingUp.resource?.article).filter((article) => !!article)
    })

    async function select(item: ApiModel<'MemberData' | 'TechnicArticle'>) {
        const resourceType = props.type === 'PERSONAL' ? 'PERSONAL' : 'TECHNIC'
        const resource = useModelFactory('Resource').create({
            external: false,
            type: resourceType,
            [resourceType === 'PERSONAL' ? 'member' : 'article']: item
        })

        await createSigningUp(resource)
    }
</script>

<style scoped lang="pcss">
    .combobox {
      @apply relative;
    }
    .combobox-input {
      @apply w-full form-input;
    }
    .combobox-options {
      @apply absolute z-30 w-full mt-1 overflow-auto text-base bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none;
    }

    .combobox-option {
      @apply relative py-2 pl-3 cursor-default select-none pr-9 text-grey-800 ui-active:bg-gray-50;
    }
</style>
