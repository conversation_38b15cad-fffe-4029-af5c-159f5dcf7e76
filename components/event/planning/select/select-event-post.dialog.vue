<template>
    <UiComposer class="h-full" :is-revealed="isRevealed" @cancel="cancel">
        <template #title>
            <EventPlanningSigningUpProvider :signing-up="signingUp" v-slot="{ avatar, organisation, displayName }">
                <div class="flex">
                    <UiAvatar :name="displayName" :image="avatar" class="mr-1" />
                    <div class="text-sm leading-tight">
                        <div class="text-sm font-semibold">
                            {{ displayName }} <br />
                            <span class="text-grey-800 text-sm font-light italic">{{ organisation }}</span>
                        </div>
                    </div>
                </div>
            </EventPlanningSigningUpProvider>
        </template>

        <h3>Eine Planstelle auswählen</h3>
        <p class="mb-4 text-sm font-light">Die Planstelle wird mit der ausgewählten Ressource besetzt.</p>

        <UiFormField label="" :disabled="eventPosts.length === 0" class="mb-1">
            <input type="text" placeholder="Planung durchsuchen" class="search-box" v-model="eventPostFilter.descriptionContains" />
        </UiFormField>

        <div class="mb-0 flex items-center gap-x-2">
            <UiSwitch v-model="eventPostFilter.isIncomplete" label="Nur unvollständig besetzte" class="text-xs" />

            <UiMenu button-size="sm" class="ml-auto">
                <template #button>
                    <UiIcon :name="eventPostSorter.icon" class="h-auto w-3" />
                    {{ eventPostSorter.label }}
                </template>
                <UiMenuItem v-for="{ key, label, set } in eventPostSorter.alternatives" :key="key" @click="set">
                    {{ label }}
                </UiMenuItem>
            </UiMenu>
        </div>

        <RadioGroup v-model="selected" as="div" class="mb-6 space-y-2">
            <template v-for="eventPost in sortedEventPosts" :key="eventPost.id">
                <RadioGroupOption as="div" class="group/option flex cursor-pointer items-center outline-none" :value="eventPost">
                    <div class="check-icon"></div>
                    <div class="event-post-item flex-1">
                        <div
                            class="bg-grey-100 flex-0 group-data-[headlessui-state~=checked]/option:bg-gold-200 group-data-[headlessui-state~=checked]/option:text-gold-500 mr-2 rounded-full p-2 transition-colors">
                            <UiIcon v-if="eventPost.eventPostResourceType === 'PERSONAL'" class="h-7 w-auto" name="user-circle" />
                            <UiIcon v-if="eventPost.eventPostResourceType === 'TECHNIC'" class="h-7 w-auto" name="material" />
                        </div>
                        <RadioGroupLabel as="div" class="block flex-1 text-sm font-normal text-gray-900">
                            {{ eventPost.description }}<br />
                            <span class="font-light text-gray-500">
                                {{ $formatDateRange(eventPost.dateFrom, eventPost.dateUpTo) }}
                            </span>
                        </RadioGroupLabel>
                    </div>
                </RadioGroupOption>
            </template>
        </RadioGroup>

        <template #footer>
            <button @click="cancel" class="form-button button-contained-secondary">Abbrechen</button>
            <button :disabled="!selected" class="form-button button-contained" @click="submit">Speichern</button>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import { RadioGroup, RadioGroupLabel, RadioGroupOption } from '@headlessui/vue'
    import { type DialogController } from '~~/composables/dialog-controller'

    const { controller } = defineProps<{
        controller: DialogController<'selectEventPost'>
    }>()

    const { isRevealed, onReveal, cancel, confirm } = controller

    const { eventPostsForResourceType } = inject(EventPlanningKey)

    /**
     * This is the selected event post
     */
    const selected = ref<ApiModel<'EventPost'>>(null)

    /**
     * The signing up model is expexted to be in reveal data
     */
    const signingUp = ref<ApiModel<'SigningUp'>>(null)

    onReveal((value) => {
        signingUp.value = value
    })

    /**
     * We'll retrieve all available event posts from the provided planning composable
     */
    const eventPosts = computed(() => {
        if (!!signingUp.value?.resource?.type) {
            return eventPostsForResourceType(signingUp.value?.resource.type)?.value
        }

        return []
    })
    /**
     * Apply filters
     */
    const { visibleEventPosts, eventPostFilter } = useEventPostFilter(eventPosts)

    /**
     * Apply sorting
     */
    const { sortedEventPosts, eventPostSorter } = useEventPostSorter(visibleEventPosts)

    function submit() {
        confirm(selected.value)
    }
</script>

<style lang="pcss" scoped>
    .search-box {
        @apply icon-search-sky-500 form-input box-border w-full bg-no-repeat pl-11 disabled:pointer-events-none disabled:opacity-60;
        @apply bg-[length:1.5rem_1.5rem] bg-[position:left_.5rem_center]
    }

    /**
    hover:bg-grey-50 group cursor-pointer rounded border border-slate-100 p-3 shadow-sm transition-colors
     */
    .event-post-item {
        @apply flex items-center justify-between rounded-md border border-slate-100 p-3 shadow-sm transition-colors bg-white;
        @apply cursor-pointer;
        @apply group-data-[headlessui-state~=checked]/option:bg-gold-100;
        @apply group-data-[headlessui-state~=checked]/option:shadow-md;
        @apply group-data-[headlessui-state~=checked]/option:border-gold-300;

    }

    .check-icon {
        @apply mr-2 h-8 w-8 cursor-pointer items-center rounded-full border;
        @apply bg-[length:1.5rem_1.5rem] bg-center hover:icon-check-gray-300;

        @apply group-data-[headlessui-state~=checked]/option:icon-check-gold-500
    }
</style>
