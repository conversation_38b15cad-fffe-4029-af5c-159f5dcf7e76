<template>
    <component :is="as">
        <!-- <Combobox as="div" :model-value="null" @update:model-value="select" class="combobox">
            <ComboboxLabel class="mb-1 text-xs leading-4 text-grey-900 empty:hidden">Material hinzufügen</ComboboxLabel>

            <div class="flex items-center combobox-input gap-x-2">
                <ComboboxInput @change="userInput = $event.target.value" placeholder="Für Vorschläge tippen" class="w-full outline-none" />
                <UiIcon v-if="pending" name="refresh" class="w-4 h-4 text-gray-500 animate-spin" />
            </div>

            <ComboboxOptions v-if="!!userInput" class="combobox-options">
                <ComboboxOption
                    v-for="option in options"
                    :key="option.id"
                    :value="option"
                    :disabled="isDisabled(option)"
                    class="relative combobox-option ui-disabled:italic ui-disabled:opacity-40">
                    {{ option.identification }}
                    <span
                        aria-hidden
                        class="absolute items-center justify-center hidden w-6 h-6 text-xs text-white bg-gray-200 rounded-full ui-active:flex right-2 top-2">
                        &#9166;
                    </span>
                </ComboboxOption>

                <div v-if="!options.length && !pending" class="px-3 py-2 text-grey-400">
                    Keine Vorschläge für <span class="italic text-grey-600">{{ userInput }}</span>
                </div>
            </ComboboxOptions>
        </Combobox> -->

        <UiFormField label="Material hinzufügen">
            <MaterialPicker @select="select" :full-width-panel="true" :exclude-articles="selectedArticles" />
        </UiFormField>
        <button  @click="$emit('externeMaterialeEmit')" class="text-sm text-sky-500 no-underline hover:underline cursor-pointer pt-2">Externes Material hinzufügen</button>

        <div class="flex justify-end">
        <button class="form-button button-contained my-2" @click="$emit('openFavoriteMaterialDialog', null)">Favoriten auswählen</button>
        </div>

        <hr class="h-0 my-6 border-t-0 border-b" />

        <EventPlanningSigningUpList resource-type="TECHNIC" />
    </component>
</template>

<script lang="ts" setup>
    defineProps<{
        as: Object | HTMLElement | string
    }>()

    const { signingUpsByResourceType } = inject(EventPlanningKey)
    const signingUps = signingUpsByResourceType('TECHNIC')

    const selectedArticles = computed(() => {
        return signingUps.value?.map((signingUp) => signingUp.resource?.article).filter((article) => !!article)
    })

    const { createSigningUp } = inject(EventManageResourcesActions)

    async function select(article: ApiModel<'TechnicArticle'>) {
        const resource = useModelFactory('Resource').create({
            external: false,
            type: 'TECHNIC',
            article
        })

        await createSigningUp(resource)
    }
</script>

<style scoped lang="pcss">
    .combobox {
        @apply relative
    }
    .combobox-input {
        @apply w-full form-input;
    }
    .combobox-options {
        @apply absolute z-30 w-full mt-1 overflow-auto text-base bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none;
    }

    .combobox-option {
        @apply relative py-2 pl-3 cursor-default select-none pr-9 text-grey-800 ui-active:bg-gray-50
    }
</style>
x
