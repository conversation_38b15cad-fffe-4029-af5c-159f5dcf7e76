<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel" class="relative">
        <template #title>Externes Material hinzufügen</template>

        <form class="flex flex-col gap-2 mb-6">
            <UiFormField label="Bezeichnung *">
                <input type="text" v-model="technicArticle.name" class="form-input box-border w-full" />
            </UiFormField>

            <UiFormField label="Modul">
                <UiListbox v-model="module" :disabled="!modules.length">
                    <template #button>{{ module?.name || 'Modul auswählen' }} </template>
                    <UiListboxOption v-for="articleType in modules" :key="articleType.id" :value="articleType">
                        {{ articleType.name }}
                    </UiListboxOption>
                </UiListbox>
            </UiFormField>

            <UiFormField label="Art">
                <UiListbox v-model="type" :disabled="!types.length">
                    <template #button>{{ type?.name || 'Art auswählen' }} </template>
                    <UiListboxOption v-for="unitType in types" :key="unitType.id" :value="unitType">
                        {{ unitType.name }}
                    </UiListboxOption>
                </UiListbox>
            </UiFormField>

            <UiFormField label="Typ">
                <UiListbox v-model="category" :disabled="!categories.length">
                    <template #button>{{ category?.name || 'Typ auswählen' }} </template>
                    <UiListboxOption v-for="categoryType in categories" :key="categoryType.id" :value="categoryType">
                        {{ categoryType.name }}
                    </UiListboxOption>
                </UiListbox>
            </UiFormField>

            <UiFormField label="Anmerkung" class="col-span-2">
                <textarea type="text" class="form-textarea box-border w-full" rows="4" v-model="technicArticle.remark" />
            </UiFormField>

            <UiFormField label="Funktioniert das externe Material?">
                <UiCheckInput label="Ja" v-model="technicArticle.functional" />
            </UiFormField>
        </form>

        <template #footer>
            <button @click="cancel" class="form-button button-contained-secondary text-xs md:text-base">Abbrechen</button>
            <button :disabled="v$.$invalid" class="form-button button-contained text-xs md:text-base" @click="submit">Externes Material hinzufügen</button>
        </template>
    </UiComposer >
</template>

<script lang="ts" setup>
    import { type DialogController } from '~~/composables/dialog-controller'
    import useVuelidate from '@vuelidate/core'
    import { required } from '@vuelidate/validators'

    const props = defineProps<{
        controller: DialogController<'createExternalMaterial'>
    }>()

    const { isRevealed, onReveal, cancel, confirm } = props.controller

    const technicArticle = ref<ApiModel<'ExternalTechnicArticle'>>()

    onReveal(() => {
        technicArticle.value = useModelFactory('ExternalTechnicArticle').create({ functional: true, type: null, unitType: null, unitCategory: null })
    })

    /**
     * Modul / Art / Typ
     *
     * `module`, `type` and `category` are writeable computed properties.
     * Their getters/setters have to be defined upfront a the hooks argument.
     *
     * @see /composables/technic-dependent-types.ts
     */
    const { module, modules, type, types, category, categories } = useTechnicDependentTypes({
        module: {
            get(_collection) {
                return technicArticle.value?.type || null
            },
            set(value) {
                technicArticle.value.type = value
            }
        },
        type: {
            get(_collection) {
                return technicArticle.value?.unitType || null
            },
            set(value) {
                technicArticle.value.unitType = value
            }
        },
        category: {
            get(_collection) {
                return technicArticle.value?.unitCategory || null
            },
            set(value) {
                technicArticle.value.unitCategory = value
            }
        }
    })

    const validation = {
        name: { required },
        functional: { required }
    }

    const v$ = useVuelidate(validation, technicArticle)

    function submit() {
        confirm(unref(technicArticle))
    }
</script>
