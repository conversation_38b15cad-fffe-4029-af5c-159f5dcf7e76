<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel" class="relative">
        <template #title>Planstellen hinzufügen</template>

        <template v-if="isRevealed">
            <TabGroup :selected-index="mode" @change="changeMode" class="tab-group" as="div" manual>
                <TabList class="flex mb-8">
                    <Tab class="tab">Personal planen</Tab>
                    <Tab class="tab">Material planen</Tab>
                </TabList>
                <TabPanels class="mb-8 sm:mb-0">
                    <TabPanel tabindex="-1">
                        <UiIntegerInput top-label="Anzahl *" v-model="eventPostCounter" :min="1" :max="99" @click.prevent />
                        <EventPlanningCreateEventPostPersonalForm v-model="eventPost" />
                    </TabPanel>

                    <TabPanel tabindex="-1">
                        <UiIntegerInput top-label="Anzahl *" v-model="eventPostCounter" :min="1" :max="99" @click.prevent />
                        <EventPlanningCreateEventPostTechnicForm v-model="eventPost" />
                    </TabPanel>
                </TabPanels>
            </TabGroup>
        </template>

        <template #footer>
            <div class="flex justify-between gap-2">
                <button @click="applyMaurerScheme" class="mr-auto form-button text-sm sm:text-base">Maurer-Schema</button>
                <button @click="cancel" class="form-button button-contained-secondary text-sm sm:text-base">Abbrechen</button>
                <button class="form-button button-contained text-sm sm:text-base" @click="createEventPosts" :disabled="v$.$invalid">Planstelle(n) hinzufügen</button>
            </div>
        </template>
    </UiComposer>
    <!-- Because we use modals.vue composable to register composers with id we can not nested composers and we need to keep them in DOM -->
    <EventPlanningCreateEventPostMaurerScheme :controller="dialog.applyMaurerScheme" as="composer" />
</template>

<script lang="ts" setup>
    import useVuelidate from '@vuelidate/core'
    import { UiComposer } from '#components'
    import { required, helpers } from '@vuelidate/validators'
    import { omit, range } from 'lodash-es'
    import { DialogController } from '~~/composables/dialog-controller'
    import { TabGroup, TabList, Tab, TabPanels, TabPanel } from '@headlessui/vue'

    const props = defineProps<{
        controller: DialogController<'createMultipleEventPosts'>
    }>()

    const dialog = {
        applyMaurerScheme: useDialogController('applyMaurerScheme')
    }

    const eventPostCounter = ref(1)
    const { event } = inject(EventPlanningKey)
    const { isRevealed, onReveal, cancel, confirm } = props.controller

    /**
     * Initialize local props that will be updated in `onReveal` hook
     */
    const eventPost = ref<ApiModel<'EventPost'>>()

    /**
     * Build a new event post model for the given resource type
     *
     * - Will be used in onReveal hook and...
     * - When we switch tabs
     *
     * @param resourceType
     */
    function buildEventPost(resourceType: ApiModel<'EventPost'>['eventPostResourceType'] = 'PERSONAL') {
        const data: Partial<ApiModel<'EventPost'>> = {
            eventPostResourceType: resourceType,
            description: null,

            // We need on resource for this post, right?
            desiredValue: 1,

            // Initialize date values from the event we want to create this event post for
            dateFrom: event.value.dateFrom,
            dateUpTo: event.value.dateUpTo,

            // Initialize required attributes
            age: null
        }

        return useModelFactory('EventPost').create(data)
    }

    /**
     * Validate if all data is that is required is filled out.
     */

    const dateInRange = helpers.withMessage(
        'Start and end date needs to be in an event range.',
        (value: Date) => {
            return value <= event.value.dateUpTo && value >= event.value.dateFrom
        }
    )

    const validation = {
        description: { required },
        dateFrom: { required, dateInRange },
        dateUpTo: { required, dateInRange }
    }
    const v$ = useVuelidate(validation, eventPost)

    /**
     * This is a dialog, so we'll set up all variables in this hook
     *
     * When this dialog is revealed it could potentially contain a signing-up model.
     * Based on this model it'll be possible to decide which kind of event post has to be created.
     */
    onReveal((input) => {
        eventPostCounter.value = 1
        eventPost.value = buildEventPost()
    })

    /**
     * This component can be run in differnt modes
     *
     * That means we can let the use decide if she wants to create an event post for material or staff.
     * Or we can make that decision for him.
     *
     * The available modes are:
     */
    enum Mode {
        Personal = 0,
        Technic = 1
    }

    /**
     * When the tab component emits a `tab-changed` event
     *
     * ...then we have to rebuild the event post that's being used as the model of child components
     *
     * @param mode
     */
    function changeMode(mode: Mode) {
        if (mode === Mode.Personal) {
            eventPost.value = buildEventPost('PERSONAL')
        }

        if (mode === Mode.Technic) {
            eventPost.value = buildEventPost('TECHNIC')
        }
    }

    /**
     * Based on the signing up that's revealed in
     */
    const mode = computed(() => {
        switch (eventPost.value?.eventPostResourceType) {
            case 'TECHNIC':
                return Mode.Technic

            default:
            case 'PERSONAL':
                return Mode.Personal
        }
    })

    function createEventPosts() {
        const eventPosts = range(eventPostCounter.value).map(() => {
            // Create as many individual models as needed
            return useModelFactory('EventPost').create(omit(eventPost.value, '_uuid'))
        })
        confirm(eventPosts)
    }

    async function applyMaurerScheme() {
        const { data: eventPosts, isCanceled } = await dialog.applyMaurerScheme.reveal()

        if (!isCanceled) {
            confirm(eventPosts)
        }
    }
</script>

<style scoped lang="pcss">
    .tab {
        @apply px-4 w-1/2 py-2 text-center !mr-1 text-sm font-normal border-b border-b-sky-200 text-sky-500 whitespace-nowrap hover:border-b-sky-500;
        @apply ui-selected:font-medium ui-selected:border-b-2 ui-selected:border-b-sky-500
    }

    .tab-group {
        @apply w-full flex flex-col flex-1 overflow-y-scroll
    }
</style>
