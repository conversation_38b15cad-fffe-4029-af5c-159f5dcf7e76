<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel">
        <template #title>
            <EventPlanningSigningUpProvider :signing-up="signingUp" v-slot="{ avatar, organisation, displayName }">
                <div class="flex">
                    <UiAvatar :name="displayName" :image="avatar" class="mr-1" />
                    <div class="text-sm leading-tight">
                        <div class="text-sm font-semibold">
                            {{ displayName }} <br />
                            <span class="text-sm italic font-light text-grey-800">{{ organisation }}</span>
                        </div>
                    </div>
                </div>
            </EventPlanningSigningUpProvider>
        </template>

        <template v-if="isRevealed">
            <div class="mb-8 md:mb-0">
                <h3 class="">Eine neue Planstelle erzeugen</h3>
                <p class="mb-4 text-sm font-light">Die neue Planstelle wird automatisch mit der ausgewählten Ressource besetzt.</p>

                <EventPlanningCreateEventPostPersonalForm v-if="mode === Mode.Personal" v-model="eventPost" />
                <EventPlanningCreateEventPostTechnicForm v-else-if="mode === Mode.Technic" v-model="eventPost" />
                <div v-else>
                    <!-- No signing up given -->
                </div>
            </div>
        </template>

        <template #footer>
            <button @click="cancel" class="form-button button-contained-secondary">Abbrechen</button>
            <button class="form-button button-contained" @click="createEventPost" :disabled="v$.$invalid">Speichern</button>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import useVuelidate from '@vuelidate/core'
    import { required } from '@vuelidate/validators'
    import { DialogController } from '~~/composables/dialog-controller'

    const props = defineProps<{
        controller: DialogController<'createEventPost'>
    }>()

    const { isRevealed, onReveal, cancel, confirm } = props.controller

    const { event } = inject(EventPlanningKey)
    const signingUp = ref<ApiModel<'SigningUp'>>(null)
    const eventPost = ref<ApiModel<'EventPost'>>(null)

    onReveal((input) => {
        signingUp.value = input
        eventPost.value = useModelFactory('EventPost').create({
            eventPostResourceType: input.resource.type,
            description: null,
            dateFrom: event.value.dateFrom,
            dateUpTo: event.value.dateUpTo
        })
    })

    enum Mode {
        Personal,
        Technic,
        Indifferent
    }

    const mode = computed(() => {
        switch (signingUp.value?.resource.type) {
            case 'PERSONAL':
                return Mode.Personal

            case 'TECHNIC':
                return Mode.Technic

            default:
                return Mode.Indifferent
        }
    })

    const validation = {
        description: { required }
    }
    const v$ = useVuelidate(validation, eventPost)

    function createEventPost() {
        confirm(eventPost.value)
    }
</script>
