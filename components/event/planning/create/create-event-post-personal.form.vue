<template>
    <div class="grid grid-cols-1 gap-4">
        <legend class="sr-only">Personal planen</legend>

        <UiFormField label="Einsatzqualifikationen">
            <OperationQualificationSelect v-model="qualificationsFacade" />
        </UiFormField>

        <UiFormField label="Bezeichnung *" class="w-full select-none">
            <input type="text" placeholder="Bezeichnung eingeben" v-model="eventPost.description" class="box-border w-full form-input" />
        </UiFormField>

        <UiIntervalInput
            label="Zeitraum"
            class="select-none"
            v-model:start="eventPost.dateFrom"
            v-model:end="eventPost.dateUpTo"
            :boundaries="restrictedRange" />

        <div class="pt-4 text-base">
            <p class='mb-4'>Erweiterte Einstellungen</p>
            <div class="flex gap-1 mb-3">
                <UiCheckInput label='Besetzung ist optional' v-model="optionalOccupation"/><UiTooltip>In der Ereignisliste auf der Startseite in der Spalte 'Ist/Soll' zählen diese Planstelle und die Besetzung wie alle anderen auch.</UiTooltip>
            </div>
            <div class="flex gap-1">
                <UiCheckInput label='Besetzung besitzt erweiterte Rechte' v-model="extendedPermissions"/><UiTooltip>Besetzung kann unter anderem Ereignis drucken, Dokumente hochladen und Helfende registrieren.</UiTooltip>
            </div>
        </div>

        <div class="mt-2 text-base">
            <h4>Anforderungen</h4>

            <UiFormField label="Führerscheine" class="mt-2">
                <UiUniversalSelect
                    v-model="drivingLicenses"
                    :options="availableDrivingLicenses"
                    label-prop="value2"
                    :showSearchInput="false"
                    placeholder="Führerschein(e) auswählen"
                />
            </UiFormField>

            <UiFormField label="Mindestalter" class="mt-4">
                <RadioGroup v-model="eventPost.age" class="flex gap-1 sm:gap-2">
                    <RadioGroupOption :value="null" class="age-option">ohne</RadioGroupOption>
                    <RadioGroupOption v-for="option in ageOptions" :value="option" :key="option" class="age-option">
                        {{ option }} Jahre
                    </RadioGroupOption>
                </RadioGroup>
            </UiFormField>
        </div>
    </div>
</template>

<script lang="ts" setup>
    import { useVModel } from '@vueuse/core'
    import { sortedUniq } from 'lodash-es'
    import { RadioGroup, RadioGroupLabel, RadioGroupOption } from '@headlessui/vue'
    import ListType from '~~/enums/listType'
    import { useCodeEntries } from '~/queries/code-entries';

    const props = defineProps<{
        modelValue: ApiModel<'EventPost'>
    }>()

    const emit = defineEmits<{
        (e: 'update:modelValue', value: ApiModel<'EventPost'>): void
    }>()

    const { event } = inject(EventPlanningKey)
    const restrictedRange = { start: event.value.dateFrom, end: event.value.dateUpTo }

    const eventPost = useVModel(props, 'modelValue', emit)

    const { $codeEntries } = useNuxtApp()

    /**
     * We have to set up eventPost.required as true when checkbox is unchecked. That is default value.
     */
    const optionalOccupation = computed({
        get(){
            if(eventPost.value.required === undefined || eventPost.value.required)   {
                eventPost.value.required = true
                return false
            } else {
                return true
            }
        },
        set(input){
            eventPost.value.required = !input
        }
    })

    /**
     * We have to set up eventPost.extendedPermission as false when checkbox is unchecked. That is default value.
     */
    const extendedPermissions = computed({
        get(){
            return eventPost.value.extendedPermission === undefined ? false : eventPost.value.extendedPermission
        },
        set(input){
            eventPost.value.extendedPermission = input
        }
    })

    const drivingLicenses = computed<ApiModel<'CodeEntry'>[]>({
        get() {
            return (
                eventPost.value?.driverLicenseCriteriaList
                    .map((id) => {
                        return $codeEntries.value?.find(({ id: codeEntryId }) => id === codeEntryId)
                    })
                    .filter((codeEntry) => !!codeEntry) || []
            )
        },
        set(value) {
            eventPost.value.driverLicenseCriteriaList = value.map((entry) => entry.id)
        }
    })

    const availableDrivingLicenses = computed(() => {
        return (
            $codeEntries.value?.filter(({ listId, value1 }) => {
                return listId === ListType.DriversLicense && value1 === 'Strasse'
            }) || []
        )
    })

    const { operationQualifications } = useListType()

    const { data: codeEntries } = useCodeEntries([...operationQualifications, ListType.QualificationType])

    const qualificationsFacade = useOperationQualificationsFacade(eventPost, 'qualifications', codeEntries)

    watchEffect(() => {
        // We'll set the description automatically if it's exactly NULL. That means after the user has done anything
        // with the respective form field this rule will not apply anymore. Because then we'll have an empty string.
        if (!!qualificationsFacade.value.length && eventPost.value.description === null) {
            eventPost.value.description = qualificationsFacade.value.at(0).qualifications.at(0)?.value2
        }
    })

    const ageOptions = computed(() => {
        return sortedUniq(
            // Add the value from event post if it's not 14, 16 or 18
            [14, 16, 18, eventPost.value.age].filter((age) => !!age).sort()
        )
    })
</script>

<style lang="pcss" scoped>
    .age-option {
        @apply text-grey-400 border rounded-md px-2 py-1 cursor-pointer;
        @apply ui-checked:text-gray-700  ui-checked:border-blue-500 ui-checked:not-italic
    }
</style>
