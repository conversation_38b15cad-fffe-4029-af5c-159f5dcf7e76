<template>
    <div class="grid grid-cols-1 gap-4">
        <legend class="sr-only">Material planen</legend>

        <UiFormField label="Modul">
            <UiListbox v-model="module" :disabled="!modules.length">
                <template #button>{{ module?.name || 'Modul auswählen' }} </template>
                <UiListboxOption v-for="option in modules" :key="option.id" :value="option">
                    {{ option.name }}
                </UiListboxOption>
            </UiListbox>
        </UiFormField>

        <UiFormField label="Art">
            <UiListbox v-model="type" :disabled="!types.length">
                <template #button>{{ type?.name || 'Art auswählen' }} </template>
                <UiListboxOption v-for="option in types" :key="option.id" :value="option">
                    {{ option.name }}
                </UiListboxOption>
            </UiListbox>
        </UiFormField>

        <UiFormField label="Typ">
            <UiListbox v-model="category" :disabled="!categories.length">
                <template #button>{{ category?.name || 'Typ auswählen' }} </template>
                <UiListboxOption v-for="option in categories" :key="option.id" :value="option">
                    {{ option.name }}
                </UiListboxOption>
            </UiListbox>
        </UiFormField>

        <UiFormField label="Bezeichnung *" class="w-full select-none">
            <input type="text" placeholder="Bezeichnung eingeben" v-model="eventPost.description" class="box-border w-full form-input" />
        </UiFormField>

        <div class="pt-4 text-base">
            <p class='mb-4'>Erweiterte Einstellungen</p>
            <div class="flex gap-1 mb-3">
                <UiCheckInput label='Besetzung ist optional' v-model="optionalOccupation"/><UiTooltip>In der Ereignisliste auf der Startseite in der Spalte 'Ist/Soll' zählen diese Planstelle und die Besetzung wie alle anderen auch.</UiTooltip>
            </div>
        </div>

        <UiIntervalInput
            label="Zeitraum *"
            class="w-full select-none"
            v-model:start="eventPost.dateFrom"
            v-model:end="eventPost.dateUpTo"
            :boundaries="restrictedRange" />
    </div>
</template>
<script lang="ts" setup>
    import { useVModel } from '@vueuse/core'

    const props = defineProps<{
        modelValue: ApiModel<'EventPost'>
    }>()

    const emit = defineEmits<{
        (e: 'update:modelValue', value: ApiModel<'EventPost'>): void
    }>()

    const eventPost = useVModel(props, 'modelValue', emit)

    /**
     * Modul / Art / Typ
     *
     * `module`, `type` and `category` are writeable computed properties.
     * Their getters/setters have to be defined upfront a the hooks argument.
     */
    const { module, modules, type, types, category, categories } = useTechnicDependentTypes({
        module: {
            get(collection) {
                return (
                    // Find anr return in collection of modules:
                    // -- The entry with an ID that equals the first entry of the array at `eventPost.technicArticleTypes`
                    collection.find(({ id }) => {
                        return id === eventPost.value?.technicArticleTypes?.at(0)
                    }) || null
                )
            },
            set(value) {
                // Set `eventPost.technicArticleTypes` to an array containg the id of the given moduleType
                eventPost.value.technicArticleTypes = value ? [value.id] : []
            }
        },
        type: {
            get(collection) {
                return (
                    collection.find(({ id }) => {
                        return id === eventPost.value?.technicArticleUnitTypes?.at(0)
                    }) || null
                )
            },
            set(value) {
                eventPost.value.technicArticleUnitTypes = value ? [value.id] : []
            }
        },
        category: {
            get(collection) {
                return (
                    collection.find(({ id }) => {
                        return id === eventPost.value?.technicArticleUnitCategories?.at(0)
                    }) || null
                )
            },
            set(value) {
                eventPost.value.technicArticleUnitCategories = value ? [value.id] : []
            }
        }
    })

    const interval = computed({
        get() {
            return { start: eventPost.value.dateFrom, end: eventPost.value.dateUpTo }
        },
        set({ start, end }: ApiModel<'Period'>) {
            eventPost.value.dateFrom = start
            eventPost.value.dateUpTo = end
        }
    })

    const { event } = inject(EventPlanningKey)
    const restrictedRange = { start: event.value.dateFrom, end: event.value.dateUpTo }


    /**
     * We have to set up eventPost.required as true when checkbox is unchecked. That is default value.
     */
    const optionalOccupation = computed({
        get(){
            if(eventPost.value.required === undefined || eventPost.value.required)   {
                eventPost.value.required = true
                return false
            } else {
                return true
            }
        },
        set(input){
            eventPost.value.required = !input
        }
    })
</script>
