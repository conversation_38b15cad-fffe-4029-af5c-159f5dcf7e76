<template>
    <EventPlanningEventPostProvider :event-post="eventPost" v-slot="{ icon, criteria }">
        <div class="flex flex-col gap-8">
            <div class="pr-4 text-sm">
                <div class="flex items-center gap-4">
                    <span class="flex p-4 items-center justify-center rounded-full bg-gray-400">
                        <UiIcon class="w-6 min-w-[1.5rem] text-white" :name="icon" />
                    </span>
                    <div>
                        <h3 class="mb-2 font-bold">{{ eventPost.description }}</h3>
                        <span class="inline-block">{{ $formatDateRangeWithAbbreviatedDayAndTime(eventPost.dateFrom, eventPost.dateUpTo) }}</span>
                    </div>
                </div>
                <div class='flex gap-2 mt-4'>
                    <p
                        v-if="!eventPost.required"
                        class="bg-blue-100 px-2 w-fit rounded-full text-xs py-1"
                        >
                            optional
                    </p>
                    <p
                        v-if="eventPost.eventPostResourceType === 'PERSONAL' && eventPost.extendedPermission"
                        class="bg-blue-100 px-2 w-fit rounded-full text-xs py-1"
                        >
                            erweiterte Rechte
                    </p>
                </div>
                <table class="mt-8">
                    <tr v-for="criterion in criteria" :key="criterion.label">
                        <td class="pb-2 pr-4 text-left align-top font-semibold" v-text="criterion.label" />
                        <td class="pb-2 italic" v-if="!criterion.value" v-text="'Keine Angabe'" />
                        <td class="pb-2" v-else v-text="criterion.value" />
                    </tr>
                </table>
            </div>

            <div class="flex flex-col items-end mb-8 md:mb-0 border-grey-100 space-y-2 border-t pt-4 text-sm">
                <div class='text-left'>
                    <button v-if="canManageResources" @click="selectSigningUp(eventPost)" class="action-button">
                        <UiIcon name="user-unknown" class="col-span-1 action-button-icon" />
                        Ressource finden
                    </button>

                    <button v-if="canManageEventPosts" @click="editEventPost(eventPost)" class="action-button">
                        <UiIcon name="pencil" class="col-span-1 action-button-icon" />
                        Planstelle bearbeiten
                    </button>

                    <button v-if="canManageEventPosts" @click="duplicateEventPost(eventPost)" class="action-button">
                        <UiIcon name="document-duplicate" class="col-span-1 action-button-icon" />
                        Planstelle verdoppeln
                    </button>

                    <button v-if="canManageEventPosts" @click="removeEventPost(eventPost)" class="action-button">
                        <UiIcon name="trash" class="col-span-1 action-button-icon" />
                        Planstelle löschen
                    </button>

                    <div v-if="!canManageEventPosts && !canManageResources" class="flex items-center justify-center font-light">
                        Keine Aktionen verfügbar
                    </div>
                </div>
            </div>
        </div>
    </EventPlanningEventPostProvider>
</template>

<script lang="ts" setup>

    const props = defineProps<{
        eventPost: ApiModel<'EventPost'>
    }>()

    const { canManageEventPosts, canManageResources } = inject(EventPermissionsKey)
    const { editEventPost, removeEventPost, duplicateEventPost, selectSigningUp } = inject(EventPlanningActions)
</script>
