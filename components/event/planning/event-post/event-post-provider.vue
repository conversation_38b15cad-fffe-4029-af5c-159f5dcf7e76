<template>
    <slot v-bind="{ icon, conflicts, hasConflicts, status, criteria, resourceSettings }" />
</template>

<script lang="ts" setup>
    import { UiIconName } from '~~/modules/ui'
    import { useCodeEntries } from '~/queries/code-entries';

    const props = defineProps<{
        eventPost: ApiModel<'EventPost'>
    }>()

    const { operationQualifications } = useListType()
    const { data: operationQualificationsData, isFetching } = useCodeEntries(operationQualifications)
    const { $codeEntries } = useNuxtApp()

    const { conflictsByEventPostId, resourceSettingsByEventPostId } = inject(EventPlanningKey)
    const { getIcon } = useSigningUpsIcons()

    /**
     * Retrieve icon by expected resource type
     */
    const icon = computed<UiIconName>(() => {
        if (props.eventPost.eventPostResourceType === 'PERSONAL') {
            return 'user-circle'
        }

        if (props.eventPost.eventPostResourceType === 'TECHNIC') {
            return getIcon(module.value?.name)
        }

        return null
    })

    /**
     * Retrieve conflicts
     */
    const conflicts = conflictsByEventPostId(props.eventPost.id)

    /**
     * Yay or Nay: conflicts?
     */
    const hasConflicts = computed(() => {
        return conflicts.value?.length > 0
    })

    /**
     * Retrieve all assigned resource settings
     */
    const resourceSettings = resourceSettingsByEventPostId(props.eventPost.id)

    /**
     * The status tells if the event post is staffed
     *
     * Optionale Planstelle: EventPost.required == false
     * a=EventPost.currentValue, b=EventPost.desiredValue, b ist immer >= 1
     * a<b: grau
     * a=b: grün
     * a>b: dunkelgrün
     *
     * Nicht optionale Planstelle: EventPost.required == true
     * a=EventPost.currentValue, b=EventPost.desiredValue, b ist immer >= 1
     * a=0: dunkelrot
     * a<b && a>0: hellrot
     * a=b: grün
     * a>b: dunkelgrün
     */
    const status = computed<'empty' | 'occupied' | 'partially-occupied' | 'over-occupied' | 'unoccupied'>(() => {

        const a = props.eventPost.currentValue;
        const b = props.eventPost.desiredValue;

        if(props.eventPost.required) {

            // The event post is partially assigned when `desiredValue` > `currentValue` but there is at least one related resource setting
            if (b > a  && !!resourceSettings.value?.length) {
                return 'partially-occupied'
            }

            if (a === 0) {
                return 'empty'
            }

            if (a > b) {
                return 'over-occupied'
            }

            // The event post is fully assigned when `desiredValue` equals `currentValue`
            if (b === a) {
                return 'occupied'
            }
        }

        // The event post is unassigned when `desiredValue` > `currentValue` and there is no related resource setting
        if (a < b) {
            return 'unoccupied'
        }

        if (a > b) {
            return 'over-occupied'
        }

        if (b === a) {
            return 'occupied'
        }
    })


    /**
     * Translate ids of driving licenses with the help of code entries
     */
    const drivingLicenses = computed(() => {
        return (props.eventPost.driverLicenseCriteriaList || [])
            .map((entryId) => {
                return $codeEntries.value?.find(({ id }) => id === entryId)
            })
            .filter((codeEntry) => !!codeEntry)
    })

    /**
     * Translate qualification ids with the help of code entries
     */
    const qualifications = computed(() => {
        return (props.eventPost.qualifications || [])
            .map((entryId) => {
                return operationQualificationsData.value?.find(({ id }) => id === entryId)
            })
            .filter((codeEntry) => !!codeEntry)
    })

    /**
     * Retrieve Modul/Art/Typ for the given event post. Therefore we provide "getters"
     * for module, type and category.
     */
    const { module, type, category } = useTechnicDependentTypes({
        module: {
            get(collection) {
                return (
                    // Find and return in collection of modules:
                    // -- The entry with an ID that equals the first entry of the array at `eventPost.technicArticleTypes`
                    collection.find(({ id }) => {
                        return id === props.eventPost.technicArticleTypes?.at(0)
                    }) || null
                )
            }
        },
        type: {
            get(collection) {
                return (
                    collection.find(({ id }) => {
                        return id === props.eventPost.technicArticleUnitTypes?.at(0)
                    }) || null
                )
            }
        },
        category: {
            get(collection) {
                return (
                    collection.find(({ id }) => {
                        return id === props.eventPost.technicArticleUnitCategories?.at(0)
                    }) || null
                )
            }
        }
    })

    /**
     * Gather event post criteria that have to be displayed
     */
    const criteria = computed(() => {
        const collected = []

        if (props.eventPost.eventPostResourceType === 'TECHNIC') {
            collected.push({
                label: 'Modul',
                value: module.value?.name
            })

            collected.push({
                label: 'Art',
                value: type.value?.name
            })

            collected.push({
                label: 'Typ',
                value: category.value?.name
            })
        }

        if (props.eventPost.eventPostResourceType === 'PERSONAL') {
            collected.push({
                label: 'Mindestalter',
                value: !!props.eventPost.age ? `${props.eventPost.age} Jahre` : null
            })

            collected.push({
                label: 'Qualifikation',
                value: qualifications.value.map(({ value2 }) => value2).join(', ')
            })

            collected.push({
                label: 'Führerschein',
                value: drivingLicenses.value.map(({ value2 }) => value2).join(', ')
            })
        }

        return collected
    })

</script>
