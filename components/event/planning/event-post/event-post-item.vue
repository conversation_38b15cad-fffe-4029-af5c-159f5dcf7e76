<template>
        <div as="div" ref="dropzoneRef" class="event-post-item" v-bind="$attrs" >
            <EventPlanningEventPostProvider :event-post="eventPost" v-slot="{ icon, hasConflicts, status, resourceSettings }">
                <div
                    ref="popoverReference"
                    @click="viewEventPostItem(0)"
                    class="relative flex flex-col md:flex-row flex-wrap min-h-[80px] gap-y-2 md:gap-y-0 pt-4 md:pt-0 md:items-center justify-between pl-7 hover:cursor-pointer">
                    <div
                        class="absolute bottom-0 left-0 top-0 w-3 rounded-bl rounded-tl"
                        :class="{
                            'bg-grey-500': status === 'unoccupied',
                            'bg-green-800': status === 'over-occupied',
                            'bg-red-200': status === 'partially-occupied',
                            'bg-green-500': status === 'occupied',
                            'bg-red-600': status === 'empty'
                        }" />

                    <div class="flex md:w-1/4 items-center gap-2">
                        <UiIcon class="h-7 w-7 min-w-[25px] min-h-[25px]" :name="icon" />
                        <h4 class="overflow-hidden text-ellipsis pr-4 sm:pr-0 text-xs sm:text-sm font-bold">
                            {{ eventPost.description }}
                        </h4>
                    </div>

                    <div class="text-xs md:w-1/6 flex flex-col">
                       <span>
                            {{ $formatDateRangeWithAbbreviatedDayAndTime(eventPost.dateFrom, eventPost.dateUpTo)?.split('–')[0] }} -
                        </span>
                        <span>
                            {{ $formatDateRangeWithAbbreviatedDayAndTime(eventPost.dateFrom, eventPost.dateUpTo)?.split('–')[1].trim() }}
                        </span>
                    </div>

                    <div class='flex md:w-1/6 flex-col gap-1'>
                        <p
                            v-show="!eventPost.required"
                            style="display: block"
                            class="bg-blue-100 px-2 w-fit rounded-full text-xs"
                            >
                                optional
                        </p>
                        <p
                            v-show="eventPost.eventPostResourceType === 'PERSONAL' && eventPost.extendedPermission"
                            style="display: block"
                            class="bg-blue-100 px-2 rounded-full text-xs w-max"
                            >
                                erweiterte Rechte
                        </p>
                    </div>

                    <div class="flex pr-1 md:pr-0 md:w-2/5 items-center justify-end">
                        <button
                            v-show="hasConflicts"
                            @click.stop="viewEventPostItem(1)"
                            class="flex rounded-md p-2 outline-none transition-colors hover:bg-gray-100">
                            <UiIcon class="text-softred-500 h-5 w-5 min-w-[1.5rem]" name="exclamation" />
                        </button>


                        <button
                            v-show="!!resourceSettings.length"
                            @click.stop="viewEventPostItem(1)"
                            class="flex rounded-md py-2 pl-3 outline-none transition-colors hover:bg-gray-100">
                            <EventPlanningResourceProvider
                                v-for="{ _uuid, eventSigningUp } in resourceSettings.slice(0, 4)"
                                :resource="eventSigningUp.resource"
                                :key="_uuid">
                                <template #default="{ name, avatar }">
                                    <UiAvatar :name="name" :image="avatar" class="-ml-2 md:-ml-4" />
                                </template>
                            </EventPlanningResourceProvider>

                            <UiAvatar :disableInitials="true" v-show="resourceSettings.length >= 5" :name="resourceSettings.length > 99 ? '...' : `+${resourceSettings.length - 4}`" />
                        </button>

                        <UiMenu v-slot="{ close }">
                            <!--
                            We have to manually trigger menu-close() because the default behaviour of @click it prevented.
                            This is needed because otherwise closing the menu will close the popover immeditly.
                            I assume this is a misbehaviour of @headlessui
                        -->
                            <UiMenuItem v-if="canReadEventPosts" @click.prevent.stop="viewEventPostItem(0) && close()" icon="eye">
                                Details ansehen
                            </UiMenuItem>

                            <UiMenuItem v-if="canManageResources" @click="selectSigningUp(eventPost)" icon="user-unknown">
                                Ressource finden
                            </UiMenuItem>

                            <UiMenuItem v-if="canManageEventPosts" @click="editEventPost(eventPost)" icon="pencil">Bearbeiten</UiMenuItem>

                            <UiMenuItem v-if="canManageEventPosts" @click="duplicateEventPost(eventPost)" icon="document-duplicate">
                                Verdoppeln
                            </UiMenuItem>

                            <UiMenuItem v-if="canManageEventPosts" @click="removeEventPost(eventPost)" icon="trash">Löschen</UiMenuItem>
                        </UiMenu>
                    </div>
                </div>
            </EventPlanningEventPostProvider>
        </div>

    <EventPlanningEventPostDialog :controller="dialog.viewEventPostItem" :eventPost="eventPost"/>
</template>

<script lang="ts" setup>
    import { unrefElement } from '@vueuse/core'

    const props = defineProps<{
        eventPost: ApiModel<'EventPost'>
    }>()

    /**
     * Inject needed permissions
     */
    const { canManageEventPosts, canReadEventPosts, canManageResources } = inject(EventPermissionsKey)

    /**
     * Use actions that are defined in `events/[id]/planning.vue`
     */
    const { editEventPost, removeEventPost, duplicateEventPost, createResourceSetting, selectSigningUp } = inject(EventPlanningActions)

    /**
     * Template references
     */
    const popoverTrigger = ref()
    const popoverReference = ref()
    const popoverPanel = ref()

    /**
     * Configure popper in order to place the popover accordingly to screen size ans scroll position
     */
    const popper = usePopper(popoverReference, popoverPanel, {
        placement: 'auto',
        modifiers: [
            {
                name: 'offset',
                options: {
                    offset: [0, 0]
                }
            },
            {
                name: 'preventOverflow',
                options: {
                    padding: { top: 64, bottom: 128 },
                    tether: true
                }
            }
        ]
    })

    const activePopoverTab = ref<'event-post' | 'resource-settings'>(null)
    function showPopover(withTab: 'event-post' | 'resource-settings') {
        activePopoverTab.value = withTab
        unrefElement(popoverTrigger).click()
        nextTick(() => popper.value.update())
        return true
    }

    /**
     * Use the dropzone hook in order to make this item react on
     */
    const dropzoneRef = useDropzone({
        onValidate(signingUp) {
            return props.eventPost.eventPostResourceType === signingUp.resource?.type
        },

        async onDrop(signingUp) {
            createResourceSetting(props.eventPost, signingUp)
        }
    })

    /**
     * Setup of controllers
     */
    const dialog = {
        viewEventPostItem: useDialogController('viewEventPostItem'),
    }

    async function viewEventPostItem(tabIndex: number) {
        await dialog.viewEventPostItem.reveal(tabIndex)
    }

</script>

<style lang="pcss" scoped>


    .event-post-item {
        @apply rounded border border-slate-100 shadow-sm transition-colors hover:bg-gray-50;
        /* @apply relative flex min-h-[80px] items-center justify-between gap-4 py-3 pl-7 pr-4; */

        &[data-ui-dropzone-state~=disabled] {
            @apply opacity-40
        }

        &[data-ui-dropzone-state~=active] {
            @apply bg-gold-200
        }
    }
    /** This is important because otherwise "ondragenter" does not work sufficient */
    :where([data-ui-dropzone-state~=enabled])  * {
        @apply !pointer-events-none
    }
</style>
