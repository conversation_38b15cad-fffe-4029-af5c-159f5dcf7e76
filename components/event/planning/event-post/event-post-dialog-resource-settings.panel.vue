<template>
    <div>
        <EventPlanningEventPostDialogResourceSetting v-if="selected" :key="selected._uuid" :resource-setting="selected">
            <template #chips>
                <div class="mb-6 flex flex-wrap gap-1">
                    <EventPlanningResourceProvider v-for="{ resource, start, end, _uuid, isSelected } in periodChips" :key="_uuid" :resource="resource">
                        <template #default="{ name, avatar }">
                            <button
                                @click="select(_uuid)"
                                class="period-chip min-h-[calc(2rem+4px)]"
                                :data-is-selected="isSelected"
                                :disabled="isSelected">
                                <UiAvatar v-if="!isSelected" :name="name" :image="avatar" size="sm" />
                                <span class="text-xs flex flex-col p-1">
                                    {{ $formatDateRangeWithAbbreviatedDayAndTime(start, end) }}
                                </span>
                            </button>
                        </template>
                    </EventPlanningResourceProvider>
                </div>
            </template>
        </EventPlanningEventPostDialogResourceSetting>
    </div>
</template>

<script setup lang="ts">
    import { orderBy } from 'lodash-es'

    const props = defineProps<{
        resourceSettings: ApiModel<'ResourceSetting'>[]
    }>()

    /**
     * Let users select a specific resource setting
     */
    const _selectedId = ref<string>(null)
    function select(value: string) {
        _selectedId.value = value
    }

    /**
     * We show details of one (the selected) resource setting
     *
     * On startup that's the first entry of the list
     */
    const selected = computed(() => {
        const selected = props.resourceSettings.find(({ _uuid }) => _uuid === _selectedId.value)
        return selected ? selected : props.resourceSettings.at(0)
    })

    /**
     *  Definition if what's available within a period chip
     */
    type ChipData = {
        _uuid: string
        start: Date
        end: Date
        resource: ApiModel<'Resource'>
        isSelected: boolean
    }

    /**
     * Calculate period chips
     *
     * We want to show all periods of all resource settings so that users can click on one period
     * and thus switch to the details of the resource setting the period belongs to.
     */
    const periodChips = computed(() => {
        return orderBy(
            // Walk through resource settings to collect all needed  data
            props.resourceSettings.reduce<ChipData[]>(
                // The reducer:
                (collector, resourceSetting) => {
                    const {
                        eventSigningUp: { resource },
                        eventPost,
                        periods,
                        _uuid
                    } = resourceSetting

                    const isSelected = selected.value === resourceSetting

                    // Resource settings can be created without periods (@see DEL-356)
                    // If this is the case, we assume that the ressource should be assigned for the whole period of the event post
                    if (!periods.length) {
                        collector.push({
                            _uuid,
                            start: eventPost.dateFrom,
                            end: eventPost.dateUpTo,
                            resource,
                            isSelected
                        })
                    }

                    // But if we have periods, then...
                    // Push each period of the resource setting to the collection
                    periods.forEach(({ start, end }) => {
                        collector.push({
                            _uuid,
                            start,
                            end,
                            resource,
                            isSelected
                        })
                    })
                    return collector
                },
                // The collection is empty at first
                []
            ),
            'start' // Sort by start date
        )
    })
</script>

<style lang="pcss" scoped>
    .period-chip {
        @apply flex cursor-pointer disabled:cursor-default disabled:pointer-events-none hover:border-sky-500 items-center justify-between gap-1 rounded-full border p-0 transition-colors pr-2;

        &[data-is-selected=true] {
            @apply border-sky-500 shadow pl-1
        }
    }
</style>
