<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel" class="relative">
        <template #title>
            <EventPlanningEventPostProvider :event-post="eventPost" v-slot="{ icon }">
                <div class="flex items-center gap-4">
                    <span class="flex p-4 items-center justify-center rounded-full bg-gray-400">
                        <UiIcon class="w-6 min-w-[1.5rem] text-white" :name="icon" />
                    </span>
                    <div class="flex flex-col">
                        <span class="text-lg font-semibold">{{ eventPost.description }}</span>
                        <span class="text-base inline-block">{{ $formatDateRangeWithAbbreviatedDayAndTime(eventPost.dateFrom, eventPost.dateUpTo) }}</span>
                    </div>
                </div>
            </EventPlanningEventPostProvider>
        </template>
        <template v-if="isRevealed">
            <div class="flex h-full xl:max-h-screen cursor-default flex-col">
                <TabGroup class="flex flex-1 flex-col p-4" as="div" :selectedIndex="tabIndex" @change="changeTab">
                    <TabList class="mb-8 items-center flex">
                        <Tab class="tab basis-1/2">Details zur Planstelle</Tab>
                        <Tab class="tab basis-1/2" :disabled="!resourceSettings.length">Besetzung</Tab>
                    </TabList>
                    <TabPanels>
                        <TabPanel>
                            <EventPlanningEventPostDialogDetailsPanel :event-post="eventPost" />
                        </TabPanel>
                        <TabPanel>
                            <EventPlanningEventPostDialogResourceSettingsPanel :resource-settings="resourceSettings" />
                        </TabPanel>
                    </TabPanels>
                </TabGroup>
            </div>
        </template>

        <template #footer>
            <button @click="cancel" class="form-button button-contained">Schließen</button>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import { TabGroup, TabList, Tab, TabPanels, TabPanel, PopoverButton } from '@headlessui/vue'
    import { DialogController } from '~~/composables/dialog-controller'

    const props = defineProps<{
        eventPost: ApiModel<'EventPost'>
        controller: DialogController<'viewEventPostItem'>
    }>()

    const { isRevealed, cancel, onReveal } = props.controller

    onReveal((data) => {
        tabIndex.value = data
    })

    /**
     * Find all resource settings for the given event post
     */
    const { resourceSettingsByEventPostId, conflictsByEventPostId } = inject(EventPlanningKey)
    const resourceSettings = resourceSettingsByEventPostId(props.eventPost?.id)

    /**
     * Get all conflicts of this event post
     */
    const conflicts = conflictsByEventPostId(props.eventPost?.id)
    const tabIndex = ref<number>(0)

    function changeTab(index: number){
        tabIndex.value = index
    }


    // ...and provide them to subcomponents.
    provide('event-post-conflicts', conflicts)

    /**
     * If user remove last person from from occupied tab we want to switch to planning tab
     */
    watch(resourceSettings, ()=>{
        if(resourceSettings?.value.length === 0) {
            changeTab(0)
        }
    })
</script>

<style lang="pcss" scoped>
    .tab {
        @apply px-4 py-1 md:py-2 text-center !mr-1 text-sm font-normal border-b border-b-sky-200 text-sky-500 whitespace-nowrap hover:border-b-sky-500;
        @apply ui-selected:font-medium ui-selected:border-b-2 ui-selected:border-b-sky-500;
        @apply disabled:opacity-50 disabled:pointer-events-none;
    }

    .tab-group {
        @apply w-full flex flex-col flex-1 overflow-y-scroll;
    }
</style>
