<template>
    <div class="flex flex-col gap-4">
        <div class="mb-4 md:mb-0 pr-4 text-sm">
            <!-- The periods selector can be placed here-->
            <slot name="chips" />

            <Transition name="fade" appear>
                <div>
                    <EventPlanningSigningUpProvider :signing-up="signingUp" v-slot="{ displayName, avatar, organisation, status }">
                        <div>
                            <div
                                class="relative group flex items-center gap-4 hover:cursor-pointer"
                                :class="{'hover:bg-blue-50 hover:ring-blue-50 hover:ring-4' : signingUp.resource.type === 'PERSONAL'}"
                                @click="signingUp.resource.type === 'TECHNIC' ?
                                    null :
                                    signingUp.resource.external ?
                                        openMemberDataDialog(signingUp.resource.externalPerson) :
                                        openMemberDataDialog(signingUp.resource.member)"
                                >
                                    <UiAvatar :name="displayName" :image="avatar" size="xl" />
                                    <header>
                                        <span class="flex flex-row gap-1">
                                            <h3 class="mb-2 font-bold" v-text="displayName" />
                                            <UiIcon name="user" class="w-4 h-4 opacity-0 transition-opacity group-hover:opacity-100" :class="{'hidden' : signingUp.resource.type === 'TECHNIC'}"/>
                                        </span>
                                        <span class="mb-1 block" v-text="organisation" />
                                        <span class="block">
                                            gemeldet am {{ $formatDate(signingUp.dateOfResponse) }} um {{ $formatTime(signingUp.dateOfResponse) }} Uhr
                                        </span>
                                    </header>
                            </div>

                            <table class="mt-6">
                                <tr>
                                    <th class="mb-2 pr-4 text-left align-top font-semibold">Verfügbarkeit</th>
                                    <td class="mb-2">
                                        <EventSigningUpStatus :status="status" />
                                    </td>
                                </tr>
                                <tr v-for="specification in specifications" :key="specification.label">
                                    <th class="mb-2 pr-4 text-left align-top font-semibold" v-text="specification.label" />
                                    <td v-if="specification.isLoading" v-text="'Laden...'" class="animate-pulse text-slate-400" />
                                    <td v-else-if="!specification.value" v-text="'Keine Angabe'" class="mb-2 italic" />
                                    <td v-else v-text="specification.value" class="mb-2 xl:line-clamp-2" :title="specification.value" />
                                </tr>
                            </table>
                        </div>
                    </EventPlanningSigningUpProvider>
                </div>
            </Transition>
        </div>

        <div v-if="!!conflictValues.length" class="bg-softred-500 flex items-center gap-6 mt-6 px-4 py-2 text-sm text-white">
            <UiIcon class="mr-3 h-8 w-8 min-w-[1.5rem] text-white" name="exclamation" />
            <div class="flex flex-col">
                <ul class="list-disc" v-for="conflict in conflictValues" :key="conflict.id">
                    <li><b>{{conflict.field}}</b>: {{ conflict.value }}</li>
                </ul>
            </div>
        </div>
        <div class="flex flex-col items-end mb-8 md:mb-0 border-grey-100 -mr-2 space-y-2 border-t pt-4 pr-4 text-sm">
           <div class="text-right">
               <button v-if="canManageResources" @click="editResourceSetting(resourceSetting)" class="action-button">
                    <UiIcon name="pencil" class="action-button-icon" />
                    Verfügbaren Zeitraum bearbeiten
                </button>

                <button v-if="canManageResources" @click="removeResourceSetting(resourceSetting)" class="action-button">
                    <UiIcon name="trash" class="action-button-icon" />
                    Von dieser Planstelle entfernen
                </button>

                <button v-if="canManageResources" @click="removeFromResourceSettings(signingUp)" class="action-button">
                    <UiIcon name="x-circle" class="action-button-icon" />
                    Von allen Planstellen entfernen
                </button>

                <div v-if="!canManageResources" class="flex items-center justify-center font-light">Keine Aktionen verfügbar</div>
           </div>
        </div>
    </div>
    <!-- <EventPlanningMemberMasterdataDialog :controller="dialog.viewMemberData" :event="event" :isBackgroundVisible="isBackgroundVisible "/> -->
</template>

<script setup lang="ts">
    import { type ApiSchema } from '~~/schema/factory'
    import { v4 as uuidv4 } from 'uuid'
    import { ViewMemberData } from 'composables/members';

    const props = defineProps<{
        resourceSetting: ApiModel<'ResourceSetting'>
    }>()

    /**
     * Inject the needed permissions
     */
    const { canManageResources } = inject(EventPermissionsKey)

    /**
     * Use actions that are defined in `events/[id]/planning.vue`
     */
    const { editResourceSetting, removeResourceSetting, removeFromResourceSettings } = inject(EventPlanningActions)

    /**
     * Take the signing up from state because `resourceSetting.eventSigningUp` misses some information
     */
    const { signingUps } = inject(EventPlanningKey)
    const signingUp = computed(() => {
        return signingUps.value.find(({ id }) => id === props.resourceSetting.eventSigningUp.id)
    })

    /**
     * We need member data for driving licenses and qualifications. We can make calls savely because
     * fetchers are disabled as long as `member.id` is not numeric
     */
    const memberQueries = useMemberQueries(signingUp.value.resource.member?.id)

    /**
     * The driving licenses of the attached member or external resource
     */
    const { data: membersDrivingLicenses, isFetching: drivingLicensesAreLoading } = memberQueries.drivingLicensesByType('street')
    const drivingLicenses = computed(() => {
        if (signingUp.value.resource.type !== 'PERSONAL') {
            return null
        }

        if (!!signingUp.value.resource.external) {
            // External persons have a dedicated field for driving licenses. It's a simple string.
            return signingUp.value.resource.externalPerson.driverLicenses || null
        }

        if (!signingUp.value.resource.external) {
            // For members we have to make a network request in order to reach for driving licenses.
            return membersDrivingLicenses.value?.map(({ value2 }) => value2).join(', ') || null
        }
    })

    /**
     *  Pperation qualifications of the attached member or external person
     */
    const { data: membersOperationQualifications, isFetching: qualificationsAreLoading } = memberQueries.operationQualifications()
    const qualifications = computed(() => {
        if (signingUp.value.resource.type !== 'PERSONAL') {
            return null
        }

        const operationQualifications = !!signingUp.value.resource.external
            ? // External persons have a dedicated field for operationQualifications
              signingUp.value.resource.externalPerson.operationQualifications
            : // For members we have to wait for a network request in order to reach for operation qualifications
              membersOperationQualifications.value

        return (
            operationQualifications
                ?.map(({ qualifications }) => qualifications)
                .flat()
                .map(({ value2 }) => value2)
                .join(', ') || null
        )
    })

    /**
     * The categorization (a.k.a Modul/Art/Typ) of a technical resource
     */
    function getCommonTechnicArticleProperty(
        resource: ApiModel<'Resource'>,
        value: keyof (ApiModel<'TechnicArticle'> | ApiModel<'ExternalTechnicArticle'>)
    ) {
        return resource.article?.[value] || resource.externalArticle?.[value]
    }

    const categorization = computed(() => {
        if (signingUp.value.resource.type !== 'TECHNIC') {
            return null
        }

        return {
            module: getCommonTechnicArticleProperty(signingUp.value.resource, 'type')?.name,
            type: getCommonTechnicArticleProperty(signingUp.value.resource, 'unitType')?.name,
            category: getCommonTechnicArticleProperty(signingUp.value.resource, 'unitCategory')?.name
        }
    })

    /**
     * The specifications that have to be displayed for the assigned resource
     */
    const specifications = computed(() => {
        const collected = []

        if (signingUp.value.resource.type === 'TECHNIC') {
            collected.push({
                label: 'Modul',
                value: categorization.value.module,
                isLoading: false
            })

            collected.push({
                label: 'Art',
                value: categorization.value.type,
                isLoading: false
            })

            collected.push({
                label: 'Typ',
                value: categorization.value.category,
                isLoading: false
            })
        }

        if (signingUp.value.resource.type === 'PERSONAL') {
            collected.push({
                label: 'Qualifikationen',
                value: qualifications.value,
                isLoading: qualificationsAreLoading.value
            })

            collected.push({
                label: 'Führerschein',
                value: drivingLicenses.value,
                isLoading: drivingLicensesAreLoading.value
            })
        }

        return collected
    })

    /**
     * Collect conflicts for this resource setting
     */
    const eventPostConflicts = inject<ComputedRef<ApiSchema<'EventPostConflict'>[]>>('event-post-conflicts')
    const conflictValues = computed(() => {
        return (
            eventPostConflicts.value
                .find(({ resourceSettingId }) => {
                    return resourceSettingId === props.resourceSetting.id
                })
                ?.conflictValues?.map(({ field, value }) => {
                    return {field, value, id: uuidv4()}
                }) || []
        )
    })

    const  { openMemberDataDialog }  = inject<ViewMemberData>('viewMemberData')
</script>

<style lang="pcss" scoped>
    .fade-enter-active,
    .fade-leave-active {
      transition: opacity 0.5s ease;
    }

    .fade-enter-from,
    .fade-leave-to {
      opacity: 0;
    }
</style>
