<template>
    <slot v-bind="{ name, avatar, isCurrentUser }" />
</template>

<script lang="ts" setup>
    const props = defineProps<{
        resource: ApiModel<'Resource'>
    }>()

    const { $resourceName, $user } = useNuxtApp()

    const avatar = computed(() => {
        if (!!props.resource.avatar) {
            return `data:image/png;base64, ${props.resource.avatar}`
        }

        return null
    })

    const name = computed(() => {
        return $resourceName(props.resource)
    })

    const isCurrentUser = computed(() => {
        return props.resource.member?.id === $user.basicData.id
    })
</script>
