<template>
    <UiBottomSheet class="h-full" :is-revealed="isRevealed" @cancel="cancel">
        <template #title>Externes Material bearbeiten</template>

        <form class="grid grid-cols-1 gap-4">
            <UiFormField label="Identifikation *">
                <input type="text" v-model="technicArticle.name" class="form-input box-border w-full" />
            </UiFormField>

            <UiFormField label="Modul">
                <UiListbox v-model="module" :disabled="!modules.length">
                    <template #button>{{ module?.name || 'Modul auswählen' }} </template>
                    <UiListboxOption v-for="articleType in modules" :key="articleType.id" :value="articleType">
                        {{ articleType.name }}
                    </UiListboxOption>
                </UiListbox>
            </UiFormField>

            <UiFormField label="Art">
                <UiListbox v-model="type" :disabled="!types.length">
                    <template #button>{{ type?.name || 'Art auswählen' }} </template>
                    <UiListboxOption v-for="unitType in types" :key="unitType.id" :value="unitType">
                        {{ unitType.name }}
                    </UiListboxOption>
                </UiListbox>
            </UiFormField>

            <UiFormField label="Typ">
                <UiListbox v-model="category" :disabled="!categories.length">
                    <template #button>{{ category?.name || 'Typ auswählen' }} </template>
                    <UiListboxOption v-for="categoryType in categories" :key="categoryType.id" :value="categoryType">
                        {{ categoryType.name }}
                    </UiListboxOption>
                </UiListbox>
            </UiFormField>

            <UiFormField label="Anmerkungen">
                <input type="text" class="form-input box-border h-20 w-full" v-model="technicArticle.remark" />
            </UiFormField>

            <UiFormField label="Funktioniert?">
                <UiCheckInput label="Ja, funktioniert" v-model="technicArticle.functional" />
            </UiFormField>
        </form>

        <template #footer>
            <button @click="cancel" class="form-button button-contained-secondary">Abbrechen</button>
            <button :disabled="v$.$invalid" class="form-button button-contained" @click="updateExternalMaterial">Übernehmen</button>
        </template>
    </UiBottomSheet>
</template>

<script lang="ts" setup>
    import { type DialogController } from '~~/composables/dialog-controller'
    import useVuelidate from '@vuelidate/core'
    import { required } from '@vuelidate/validators'

    const props = defineProps<{
        controller: DialogController<'editExternalMaterial'>
    }>()

    const { isRevealed, onReveal, cancel, confirm } = props.controller

    const technicArticle = ref<ApiModel<'ExternalTechnicArticle'>>()

    onReveal((signingUp: ApiModel<'SigningUp'>) => {
        technicArticle.value = useModelFactory('ExternalTechnicArticle').create(signingUp.resource.externalArticle)
    })

    /**
     * Modul / Art / Typ
     *
     * `module`, `type` and `category` are writeable computed properties.
     * Their getters/setters have to be defined upfront a the hooks argument.
     *
     * @see /composables/technic-dependent-types.ts
     */
    const { module, modules, type, types, category, categories } = useTechnicDependentTypes({
        module: {
            get(_collection) {
                return technicArticle.value?.type || null
            },
            set(value) {
                technicArticle.value.type = value
            }
        },
        type: {
            get(_collection) {
                return technicArticle.value?.unitType || null
            },
            set(value) {
                technicArticle.value.unitType = value
            }
        },
        category: {
            get(_collection) {
                return technicArticle.value?.unitCategory || null
            },
            set(value) {
                technicArticle.value.unitCategory = value
            }
        }
    })

    const validation = {
        name: { required },
        functional: { required }
    }

    const v$ = useVuelidate(validation, technicArticle)

    function updateExternalMaterial() {
        confirm(unref(technicArticle))
    }
</script>
