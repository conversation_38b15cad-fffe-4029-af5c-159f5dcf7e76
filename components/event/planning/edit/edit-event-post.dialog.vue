<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel">
        <template #title>Planstelle bearbeiten</template>

        <template v-if="isRevealed">
            <EventPlanningCreateEventPostPersonalForm v-if="mode === Mode.Personal" v-model="eventPost" />
            <EventPlanningCreateEventPostTechnicForm v-else-if="mode === Mode.Technic" v-model="eventPost" />
        </template>

        <template #footer>
            <button @click="cancel" class="form-button button-contained-secondary">Abbrechen</button>
            <button class="form-button button-contained" @click="updateEventPost" :disabled="v$.$invalid || !dateInRange">Speichern</button>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import useVuelidate from '@vuelidate/core'
    import { required, helpers } from '@vuelidate/validators'
    import { DialogController } from '~~/composables/dialog-controller'

    const props = defineProps<{
        controller: DialogController<'editEventPost'>
    }>()

    const { isRevealed, onReveal, cancel, confirm } = props.controller

    const eventPost = ref<ApiModel<'EventPost'>>(null)
    const start = ref<Date>(null)
    const end = ref<Date>(null)

    onReveal((input) => {
        eventPost.value = useModelFactory('EventPost').create(input)
        start.value = input.dateFrom
        end.value = input.dateUpTo
    })

    enum Mode {
        Personal,
        Technic,
        Indifferent
    }

    const mode = computed(() => {
        switch (eventPost.value?.eventPostResourceType) {
            case 'PERSONAL':
                return Mode.Personal

            case 'TECHNIC':
                return Mode.Technic

            default:
                return Mode.Indifferent
        }
    })

    const dateInRange = computed(() => {
        return start.value <= eventPost.value?.dateFrom && end.value >= eventPost.value?.dateUpTo
    })

    const validation = {
        description: { required },
    }

    const v$ = useVuelidate(validation, eventPost)

    function updateEventPost() {
        confirm(eventPost.value)
    }
</script>
