<template>
    <component :is="asComponent" :is-revealed="isRevealed" @cancel="cancel" class="relative">
        <template #title>Verfügbaren Zeitraum bearbeiten</template>

        <template v-if="isRevealed">
            <EventPlanningResourceProvider :resource="resourceSetting.eventSigningUp.resource" v-slot="{ name, avatar, isCurrentUser }">
                <div class="mb-6 rounded-sm bg-gray-100 p-4">
                    <div class="mb-2 flex items-center gap-x-2 text-sm font-semibold text-gray-600">
                        <UiAvatar :name="name" :image="avatar" />
                        {{ name }}
                        <span v-if="isCurrentUser" class="bg-softred-100 rounded-md px-2 py-0.5 text-xs font-semibold text-red-600">Du</span>
                    </div>
                    <p class="text-sm">
                        ist auf der Planstelle <span class="font-semibold text-gray-700">{{ resourceSetting.eventPost.description }}</span> ({{
                            $formatDateRange(resourceSetting.eventPost.dateFrom, resourceSetting.eventPost.dateUpTo)
                        }}) zu dieser Zeit eingeplant:
                    </p>
                </div>
            </EventPlanningResourceProvider>

            <h3 class="mb-2">Eingeplanter Zeitraum</h3>

            <UiIntervalPicker2 @select="addPeriod" :boundaries="boundaries" class="mb-6" />

            <TransitionGroup tag="div" name="list" class="mb-6 space-y-2">
                <h3 v-if="periods.length" key="___intro___" class="mt-4 block text-xs">Die Ressource ist also in diesem Zeitraum eingeplant:</h3>

                <span v-for="period in periods" :key="period.start.toISOString()" class="form-input flex items-center">
                    {{ $formatDateRange(period.start, period.end) }}
                    <button @click.prevent="removePeriod(period)" class="form-button button-sm ml-auto">
                        <UiIcon name="trash" class="h-4 w-4" />
                    </button>
                </span>

                <div v-if="!periods.length" key="___empty___" class="mt-4 block text-sm">
                    <h3 class="mb-2 font-semibold">Kein abweichender Zeitraum für Ressource angegeben</h3>
                    <p class="mb-4">
                        Das bedeutet, dass du diese Ressource so lange verplanst, wie die Planstelle angegeben ist:
                    </p>
                    <span disabled="disabled" class="form-input pointer-events-none flex items-center">
                        {{ $formatDateRange(resourceSetting.eventPost.dateFrom, resourceSetting.eventPost.dateUpTo) }}
                    </span>
                </div>
            </TransitionGroup>
        </template>

        <template #footer>
            <button @click="cancel" class="form-button button-contained-secondary">Abbrechen</button>
            <button class="form-button button-contained" @click="submit" :disabled="false">Zeit übernehmen</button>
        </template>
    </component>
</template>

<script lang="ts" setup>
    import { UiBottomSheet, UiComposer } from '#components'
    import { DialogController } from '~~/composables/dialog-controller'

    const props = defineProps<{
        as?: 'bottom-sheet' | 'composer'
        controller: DialogController<'editResourceSetting'>
    }>()

    /**
     * This component can be run as 'bottom sheet' or 'composer'
     */
    const asComponent = computed(() => {
        const { as = 'composer' } = props
        if (as === 'bottom-sheet') {
            return UiBottomSheet
        } else {
            return UiComposer
        }
    })

    /**
     * That's what we need from the dialog controller
     */
    const { isRevealed, onReveal, cancel, confirm } = props.controller

    const resourceSetting = ref<ApiModel<'ResourceSetting'>>(null)

    const boundaries = computed(() => {
        return { start: resourceSetting.value?.eventPost.dateFrom, end: resourceSetting.value?.eventPost.dateUpTo }
    })

    const periods = computed({
        get() {
            return resourceSetting.value?.periods || []
        },

        set(periods) {
            resourceSetting.value = useModelFactory('ResourceSetting').create({
                ...resourceSetting.value,
                periods
            })
        }
    })

    function addPeriod(interval: { start: Date; end: Date }) {
        const merged = mergeIntervals(...periods.value, interval)
        periods.value = merged.map(({ start, end }) => {
            return useModelFactory('Period').create({
                start,
                end
            })
        })
    }

    function removePeriod(period: ApiModel<'Period'>) {
        periods.value = [...periods.value.filter(({ _uuid }) => _uuid !== period._uuid)]
    }

    onReveal((input) => {
        resourceSetting.value = input
    })

    function submit() {
        const value = !periods.value.length
            ? [
                  useModelFactory('Period').create({
                      start: resourceSetting.value.eventPost.dateFrom,
                      end: resourceSetting.value.eventPost.dateUpTo
                  })
              ]
            : periods.value

        confirm(value)
    }
</script>
