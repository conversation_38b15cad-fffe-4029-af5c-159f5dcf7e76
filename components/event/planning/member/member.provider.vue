<template>
    <slot v-bind="{ displayName, age, organisation, listOfTelephones, listOfEmails, qualifications, driverLicenses }" />
</template>

<script lang="ts" setup>
    import { Contact } from '~/composables/members'

    const props = defineProps<{
       member: ApiModel<'Masterdata'>
    }>()

    const { $fullName } = useNuxtApp()
    const displayName = computed(() => $fullName(props.member.basicData))

    const age = computed<number>(()=> props.member.basicData.age)

    const organisation = computed<string>(()=> props.member.basicData.leadingOrganisation.name)

    const listOfTelephones = computed<Contact[]>(() => {
        const validPhoneTypes: string[] = ['FnA', 'FnB', 'FnP', 'MfA', 'MfB', 'MfP'];
        return useFilterAndSortMemberContacts(validPhoneTypes, props.member.communications);
    });

    const listOfEmails = computed<Contact[]>(() => {
        const validEmailTypes: string[] = ['eMA', 'eMB', 'eMP'];
        return useFilterAndSortMemberContacts(validEmailTypes, props.member.communications);
    });

    const qualifications = computed<string[]>(() => {
        const { gender } = props.member.basicData;

        // Check if operationQualifications is null or undefined
        const allValues: string[] = (props.member.operationQualifications ?? []).flatMap(operation =>
            operation.qualifications.flatMap((qualification: ApiModel<'CodeEntry'>) => {
                if (gender === 'M' && qualification.value4 && qualification.value4.trim()) {
                    return [qualification.value4];
                } else if (gender === 'W' && qualification.value3 && qualification.value3.trim()) {
                    return [qualification.value3];
                } else if (qualification.value2 && qualification.value2.trim()) {
                    return [qualification.value2];
                }
                return [];
            })
        );

        // Remove duplicates and sort in ascending order
        const uniqueSortedValues = Array.from(new Set(allValues)).sort((a, b) => a.localeCompare(b));

        return uniqueSortedValues;
    });

    const driverLicenses = computed<string[]>(() => {
    // Collect all licenseClass.value2 values
    const allValues: string[] = props.member.driverLicenses.flatMap(driverLicense =>
        driverLicense.acquiredLicenseClasses.flatMap((acquiredLicense) =>
            acquiredLicense.licenseClass.value2 && acquiredLicense.licenseClass.value2.trim() ? [acquiredLicense.licenseClass.value2] : []
        )
    );

    // Remove duplicates and sort in ascending order
    const uniqueSortedValues = Array.from(new Set(allValues)).sort((a, b) => a.localeCompare(b));

    return uniqueSortedValues;
    });


</script>
