<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel" class="relative">
        <template #title>
            <ProvideMember :member="member" v-slot="{ name, organisation }">
                <div class="flex items-center">
                    <UiAvatar :name="name" :image="avatar" size="lg" class="mr-[0.5em]" />
                    <span>
                       <span class="font-semibold text-lg"> {{ name }} </span> <br />
                        <span v-text="organisation" class="text-base text-red-500" />
                    </span>
                </div>
            </ProvideMember>
        </template>

        <template v-if="isRevealed">
            <TabGroup class="" as="div" :selectedIndex="tabIndex" @change="changeTab">
                <TabList class="mb-8 items-center justify-start gap-4 flex">
                    <Tab class="tab basis-1/2">Details zur Person</Tab>
                    <Tab class="tab basis-1/2" :disabled="!isSignedUp || (!hasReadingRights && isResponsible)">Meldung</Tab>
                </TabList>
                <TabPanels>
                    <TabPanel>
                        <EventPlanningMemberMasterdataDetails :member="member" :event="event"/>
                    </TabPanel>
                    <TabPanel>
                        <EventPlanningMemberMasterdataRaport :member="member" :event="event" />
                    </TabPanel>
                </TabPanels>
            </TabGroup>
        </template>

        <template #footer>
            <button @click="cancel" class="form-button button-contained">Schließen</button>
        </template>
    </UiComposer>
</template>


<script lang="ts" setup>
    import { TabGroup, TabList, Tab, TabPanels, TabPanel } from '@headlessui/vue'
    import { DialogController } from '~~/composables/dialog-controller'

    const props = defineProps<{
        controller: DialogController<'viewMemberData'>
        event?: ApiModel<'Event'>
        isResponsible?: boolean
    }>()

    const { isRevealed, cancel, onReveal } = props.controller

    const member = ref<ApiModel<'MemberData'> | ApiModel<'ExternalPerson'>>(null)

    const {data: allSigningUps } = useEventQuery('signingUps')

    const isSignedUp = ref<boolean>(false)

    const avatar = ref<string | null>(null)

    const { data: memberAvatar, getMemberProfileImage, isLoading }  = useGetMemberProfileImage()

    onReveal((memberData: ApiModel<'MemberData'> | ApiModel<'ExternalPerson'>) => {
        member.value = memberData
        tabIndex.value = 0

        isSignedUp.value = !!allSigningUps.value
            .filter(item => item.resource.type === "PERSONAL")
            .find(item => {
                if(item.resource.external && item.resource.externalPerson) {
                    return item?.resource.externalPerson.id === member.value.id
                } else if(item?.resource.member) {
                    return item?.resource.member.id === member.value.id
                } return false
            })

        getMemberProfileImage(member.value.id)

    })

   watch(
        () => memberAvatar.value,
        (newAvatar) => {
            avatar.value = newAvatar;
        }
    );

    const tabIndex = ref<number>(0)

    function changeTab(index: number){
        tabIndex.value = index
    }

    const hasReadingRights = ref<boolean>(true)

    provide('isResponsible', props.isResponsible)
    provide('hasReadingRights', hasReadingRights)

</script>

<style lang="pcss" scoped>
    .tab {
        @apply px-4 py-1 md:py-2 text-center !mr-1 text-sm font-normal border-b border-b-sky-200 text-sky-500 whitespace-nowrap hover:border-b-sky-500;
        @apply ui-selected:font-medium ui-selected:border-b-2 ui-selected:border-b-sky-500;
        @apply disabled:opacity-50 disabled:pointer-events-none;
    }

    .tab-group {
        @apply w-full flex flex-col flex-1 overflow-y-scroll;
    }
</style>
