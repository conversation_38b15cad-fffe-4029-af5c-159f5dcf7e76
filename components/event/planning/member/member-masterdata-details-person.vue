<template>
    <div v-if="isLoading">
        <UiLoader v-if="isLoading" :is-loading="isLoading" />
    </div>
    <div v-else class="mb-8 md:mb-0">
        <EventPlanningMemberProvider v-if="!!data.basicData" :member="data" v-slot="{ age, listOfTelephones, listOfEmails, qualifications, driverLicenses }">
            <div class="flex flex-col gap-4">
                <EventPlanningMemberBasicData
                    :age="age"
                    :emails="listOfEmails"
                    :telephones="listOfTelephones"
                    :qualifications="qualifications"
                    :driverLicenses="driverLicenses"
                />
                <div class="border-grey-100 border-t pt-4 pr-5 text-sm flex justify-end">
                    <a :href="mitgliedkarteHref" target="_blank" class="action-button">
                        <UiIcon name="user" class="col-span-1 action-button-icon" />
                        <PERSON>ur Mitgliederkarte
                    </a>
                </div>
            </div>
        </EventPlanningMemberProvider>
        <div v-else>
            <div v-if="isResponsible">
                <div class="flex flex-col gap-4">
                    <EventPlanningMemberBasicData
                        :emails="responsibleListOfEmails"
                        :telephones="responsibleListOfTelephones"
                    />
                </div>
            </div>
            <span v-else>Sie haben leider keine Berechtigung, weitere Informationen zu dieser Person zu sehen.</span>
        </div>
    </div>
</template>

<script lang="ts" setup>
    import { Contact } from '~/composables/members'

    const props = defineProps<{
        member: ApiModel<'MemberData'> | ApiModel<'ExternalPerson'>
        event: ApiModel<'Event'>
    }>()


    const {
        public: { baseUrl }
    } = useRuntimeConfig()

    const mitgliedkarteHref = ref<string>("/mv/drkadminmember--mitglied/" + props.member.id + "/drkadminmember--tab/stammdaten/")

    const { data, isLoading } = useMemberQueries(props.member.id).details()

    // Get cached data from event
    const allResponsibles = props.event.responsibles

    const responsible = ref<ApiModel<'EventResponsible'>>(
        allResponsibles.filter(item => item.masterdata.id === props.member.id)[0]
    )

    // If we open this dialog via responsible people list
    // we want to check if person has reading rights to disable raport tab
    // but we do not want to disable this tab via other places
    const isResponsible = inject<Ref<boolean>>('isResponsible')
    const hasReadingRights = inject<Ref<boolean>>('hasReadingRights')
    hasReadingRights.value = data.value?.basicData == undefined ? false : true

    watch(isLoading, ()=>{
        hasReadingRights.value = data.value?.basicData == null ? false : true
    })

    const responsibleListOfTelephones = computed<Contact[]>(() => {
        const validPhoneTypes: string[] = ['FnA', 'FnB', 'FnP', 'MfA', 'MfB', 'MfP'];
        return useFilterAndSortMemberContacts(validPhoneTypes, responsible.value.communications);
    });

    const responsibleListOfEmails = computed<Contact[]>(() => {
        const validEmailTypes: string[] = ['eMA', 'eMB', 'eMP'];
        return useFilterAndSortMemberContacts(validEmailTypes, responsible.value.communications);
    });

</script>
