<template>
    <div class="mb-8 md:mb-0">
        <EventPlanningSigningUpProvider
            v-if="signingUp"
            :signing-up="signingUp"
            v-slot="{ status, reportedAt, remark, accomodation, periods, isAssigned }">
                <div class="flex flex-col gap-4">
                    <div class="flex flex-col gap-2 pr-4 text-sm">
                        <div class="grid grid-cols-12 mb-4">
                            <span class="col-span-12 md:col-span-4 font-semibold">
                                Verfügbarkeit
                            </span>
                            <div class="col-span-12 md:col-span-8">
                                <EventSigningUpStatus :status="status" />
                            </div>
                        </div>
                        <div class="grid grid-cols-12 mb-4">
                            <span class="col-span-12 md:col-span-4 font-semibold">
                                Eingeplant
                            </span>
                            <div class="col-span-12 md:col-span-8">
                                <span v-if="isAssigned">ja</span>
                                <span v-else>nein</span>
                            </div>
                        </div>
                        <div class="grid grid-cols-12 mb-4">
                            <span class="col-span-12 md:col-span-4 mb-2 md:mb-0 font-semibold">
                                Zeitraum
                            </span>
                            <div v-if="status === 100 || status === 80" class="col-span-12 md:col-span-8">
                                <span v-if="periods.length == 0 && status === 100"> verfügbar für gesamten Zeitraum des Ereignisses</span>
                                <span v-for="period in periods">
                                    {{ $formatDateRangeWithAbbreviatedDayAndTime(period.start, period.end) }} <br/>
                                </span>
                            </div>
                        </div>
                        <div v-if="!!reportedAt" class="grid grid-cols-12 mb-4">
                            <span class="col-span-12 md:col-span-4 mb-2 md:mb-0 font-semibold">
                                gemeldet am
                            </span>
                            <div class="col-span-12 md:col-span-8">
                                {{ $formatDateWithAbbreviatedDayAndTime(reportedAt) }}
                            </div>
                        </div>
                        <div v-if="!!remark" class="grid grid-cols-12 mb-4">
                            <span class="col-span-12 md:col-span-4 mb-2 md:mb-0 font-semibold">
                                Anmerkung
                            </span>
                            <div class="col-span-12 md:col-span-8">
                                {{ remark }}
                            </div>
                        </div>
                        <div v-if="accomodation !== null && (status === 100 || status === 80)" class="grid grid-cols-12 mb-4">
                            <span class="col-span-12 md:col-span-4 mb-2 md:mb-0 font-semibold">
                                Übernachtung
                            </span>
                            <div class="col-span-12 md:col-span-8">
                                {{ accomodation ? "ja" : "nein" }}
                            </div>
                        </div>
                    </div>

                    <div v-if="!signingUp.resource.external" class="border-grey-100 border-t pt-4 pr-5 text-sm flex flex-col items-end">
                        <div class="text-left">
                            <div>
                                <a :href="mitgliedkarteHref" target="_blank" class="action-button">
                                    <UiIcon name="user" class="col-span-1 action-button-icon" />
                                    Zur Mitgliederkarte
                                </a>
                            </div>
                            <!-- TO DO: implement later -->
                            <!-- <div>
                                <a href="#" target="_blank" class="action-button">
                                    <UiIcon name="pencil" class="col-span-1 action-button-icon" />
                                    Meldung bearbeiten
                                </a>
                            </div> -->
                        </div>
                    </div>
                </div>
        </EventPlanningSigningUpProvider>
    </div>
</template>

<script lang="ts" setup>
    import { useQueryClient } from '@tanstack/vue-query';

    const props = defineProps<{
        member: ApiModel<'MemberData'> | ApiModel<'ExternalPerson'>
        event?: ApiModel<'Event'>
    }>()

    const {
        public: { baseUrl }
    } = useRuntimeConfig()

    const mitgliedkarteHref = ref<string>("/mv/drkadminmember--mitglied/" + props.member.id + "/drkadminmember--tab/stammdaten/")

    const queryClient = useQueryClient();

    const queryKey = ["events","detail",props.event.id,"signingUps","all"];

    // Get cached data from the query client
    const allSigningUps = queryClient.getQueryData<ApiModel<'SigningUp'>[]>(queryKey);

    const signingUp = ref<ApiModel<'SigningUp'>>(
        allSigningUps
        .filter(item=>item.resource.type === "PERSONAL")
        .filter(item => {
            if(item.resource.external && item.resource.externalPerson) {
                return item?.resource.externalPerson.id === props.member.id
            } if(item.resource.member) {
                return item?.resource.member.id === props.member.id
            } return false
        })[0]
    )

</script>
