<template>
    <div class="flex flex-col gap-2 pr-4 text-sm">
        <div v-if="age" class="grid grid-cols-12 mb-4">
            <span class="col-span-12 md:col-span-4 font-semibold">
                Alter
            </span>
            <div class="col-span-12 md:col-span-8">
                <span class="inline-block"> {{ age ? age : 'Keine <PERSON>abe' }}</span>
            </div>
        </div>
        <div class="grid grid-cols-12 mb-4">
            <span class="col-span-12 md:col-span-4 mb-2 md:mb-0 font-semibold">
                Telefon
            </span>
            <div class="col-span-12 md:col-span-8">
                <div v-if="Array.isArray(telephones) && telephones.length > 0" class="flex flex-wrap gap-3">
                    <template  v-for="telephone in telephones" :key="telephone">
                        <div class="flex items-center gap-x-1 hover:bg-blue-50 hover:ring-blue-50 hover:ring-4">
                            <UiPopper
                                class="flex"
                                :text="useGetPriorityText(telephone.priority)">
                                <span
                                    class="rounded-full p-1.5"
                                    :class="{
                                        'bg-green-400': telephone.priority === 1,
                                        'bg-yellow-400': telephone.priority === 2,
                                        'bg-red-400': telephone.priority === 3
                                    }">
                                </span>
                            </UiPopper>
                            <span class="inline-block">
                                <a :href="`tel:${telephone.contact}`">{{ telephone.contact }}</a>
                            </span>
                        </div>
                    </template>
                </div>
                <div v-else-if="telephones && telephones.length > 0" class="flex flex-wrap gap-3">
                    <span class="inline-block hover:bg-blue-50 hover:ring-blue-50 hover:ring-4">
                        <a :href="`tel:${telephones}`">{{ telephones }}</a>
                    </span>
                </div>
                <span v-else class="inline-block text-grey-500">Keine Angabe</span>
            </div>
        </div>
        <div class="grid grid-cols-12 mb-4">
            <span class="col-span-12 md:col-span-4 mb-2 md:mb-0 font-semibold">
                E-Mail
            </span>
            <div class="col-span-12 md:col-span-8">
                <div v-if="Array.isArray(emails) && emails.length > 0" class="flex flex-wrap gap-3">
                    <template v-for="email in emails" :key="email">
                        <div class="flex items-center gap-x-1 hover:bg-blue-50 hover:ring-blue-50 hover:ring-4">
                            <UiPopper
                                class="flex"
                                :text="useGetPriorityText(email.priority)">
                                <span
                                    class="rounded-full p-1.5"
                                    :class="{
                                        'bg-green-400': email.priority === 1,
                                        'bg-yellow-400': email.priority === 2,
                                        'bg-red-400': email.priority === 3
                                    }">
                                </span>
                            </UiPopper>
                            <span class="inline-block ">
                                <a :href="`mailto:${email.contact}`">{{ email.contact }}</a>
                            </span>
                        </div>
                    </template>
                </div>
                <div v-else-if="emails && emails.length > 0" class="flex flex-wrap gap-3">
                     <span class="inline-block hover:bg-blue-50 hover:ring-blue-50 hover:ring-4">
                        <a :href="`mailto:${emails}`">{{ emails }}</a>
                    </span>
                </div>
                <span v-else class="inline-block text-grey-500">Keine Angabe</span>
            </div>
        </div>
        <div class="grid grid-cols-12 mb-4">
            <span class="col-span-12 md:col-span-4 mb-2 md:mb-0 font-semibold">
                Einsatzqualifikation
            </span>
            <div class="col-span-12 md:col-span-8">
                <div v-if="qualifications?.length > 0" class="flex flex-wrap gap-1">
                    <template v-for="(qualification, index) in qualifications" :key="qualification">
                        <div class="flex items-center">
                            <span class="inline-block"> {{ qualification }}</span>
                            <span v-if="index !== qualifications.length-1">,</span>
                        </div>
                    </template>
                </div>
                <span v-else class="inline-block text-grey-500">Keine Angabe</span>
            </div>
        </div>
        <div class="grid grid-cols-12 mb-4">
            <span class="col-span-12 md:col-span-4 mb-2 md:mb-0 font-semibold">
                Führerscheine
            </span>
            <div class="col-span-12 md:col-span-8">
                <div v-if="Array.isArray(driverLicenses) && driverLicenses.length > 0" class="flex flex-wrap gap-1">
                    <template v-for="(driverLicense, index) in driverLicenses" :key="driverLicense">
                        <div class="flex items-center">
                            <span class="inline-block"> {{ driverLicense }}</span>
                            <span v-if="index !== driverLicenses.length-1">,</span>
                        </div>
                    </template>
                </div>
                <div v-else-if="driverLicenses && driverLicenses.length > 0" class="flex flex-wrap gap-1">
                    <div class="flex items-center">
                        <span class="inline-block"> {{ driverLicenses }}</span>
                    </div>
                </div>
                <span v-else class="inline-block text-grey-500">Keine Angabe</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { Contact } from '~/composables/members'

    const props = defineProps<{
        age?: number,
        telephones?: Contact[] | string,
        emails?: Contact[] | string,
        qualifications?: string[],
        driverLicenses?: string[] | string
    }>()

</script>
