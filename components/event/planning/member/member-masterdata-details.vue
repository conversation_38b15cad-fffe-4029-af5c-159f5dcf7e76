<template>
    <EventPlanningMemberMasterdataDetailsPerson v-if="!isExternalPerson" :member="member" :event="event" />
    <div v-else>
        <ProvideExternalPerson :externalPerson="externalPersonData" v-slot="{ telephone, email, driverLicenses, qualifications }">
            <div class="flex flex-col gap-4">
                <EventPlanningMemberBasicData
                    :emails="email"
                    :telephones="telephone"
                    :qualifications="qualifications"
                    :driverLicenses="driverLicenses"
                />
            </div>
        </ProvideExternalPerson>
    </div>

</template>

<script lang="ts" setup>

    const props = defineProps<{
        member: ApiModel<'MemberData'> | ApiModel<'ExternalPerson'>
        event: ApiModel<'Event'>
    }>()

    const memberData = ref<ApiModel<'MemberData'>>(null)
    const externalPersonData = ref<ApiModel<'ExternalPerson'>>(null)
    const isExternalPerson = ref<boolean>(false)

    if(props.member instanceof useModelFactory('ExternalPerson')) {
        externalPersonData.value = props.member
        isExternalPerson.value = true
    } else if(props.member instanceof useModelFactory('MemberData')){
        memberData.value = props.member
    }


</script>
