<template>
    <UiComposer :is-revealed="isRevealed" @cancel="closeAndClear" class="relative">
        <template #title
            >Gliederungen finden <span v-if="targetOrganisation.length > 0"> ({{ targetOrganisation.length }})</span></template
        >

        <UiTab :defaultTabIndex="tab" tab-width="w-1/2" :tabs="tabs">
            <TabPanel>
                <EventSharingCommunityPickerPanel v-model="eventShareRequests" />
            </TabPanel>
            <TabPanel>
                <EventSharingCommunitySearchPanel
                    v-model="eventShareRequests"
                    :selectedOrganisations="selectedOrganisations"
                    @update:selectedOrganisations="updateSelectedOrganisations"
                    @update:deleteOrganisation="deleteOrganisation"
                />
            </TabPanel>
        </UiTab>

        <template #footer>
            <button @click="closeAndClear" class="form-button button-contained-secondary ml-auto">Abbrechen</button>
            <button class="form-button button-contained" @click="submit" :disabled="eventShareRequests.length === 0">Ereignis teilen</button>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import { TabPanel } from '@headlessui/vue'
    import { EventSharingCommunitySearchPanel, UiComposer } from '#components'
    import { EventSharingDialogTypes } from '~~/composables/event-sharing'
    import { ExtractedOrganization } from './community-search/types'

    const props = defineProps<{
        controller: EventSharingDialogTypes<'shareEventDialog'>
    }>()

    const tabs = computed(() => [
        { name: 'Gliederungen auswählen', disabled: false },
        { name: 'Gliederungen suchen', disabled: false }
    ])

    enum Tab {
        PICKER = 0,
        SEARCH = 1
    }

    const tab = ref<Tab>(Tab.PICKER)

    const { isRevealed, onReveal, cancel, confirm } = props.controller

    const eventShareRequests = ref([])
    const selectedOrganisations: Ref<ExtractedOrganization[]> = ref([])

    watch(isRevealed, ()=>{
        eventShareRequests.value = [],
        selectedOrganisations.value = []
    })

    const selectItem = ref<boolean>(false)

    function updateSelectedOrganisations(selected: Ref<ExtractedOrganization>) {
        selectItem.value = true
        selectedOrganisations.value = [...selectedOrganisations.value, selected.value]
    }

    provide('selectItemWhenSelectedFromSearchInput', selectItem)

    function deleteOrganisation(id: number) {
        selectedOrganisations.value = selectedOrganisations.value.filter(item =>{
            return item.id !== id
        })
        eventShareRequests.value = eventShareRequests.value.filter(request=>request.targetOrganisation.id !== id)
    }

    function closeAndClear() {
        eventShareRequests.value = []
        cancel()
    }

    function submit() {
        confirm(eventShareRequests.value)
        eventShareRequests.value = []
    }

    const targetOrganisation = computed(
      () =>
      [...new Set(
        eventShareRequests.value.map(
          (item) => item.targetOrganisation
        )
      )]
    )
</script>
