<template>
  <li
    class="text-grey-800 relative cursor-default select-none py-2 pr-9"
    :class="{
      'bg-grey-100 ': active,
      'italic': selected,
      'opacity-50 text-[10px] uppercase leading-[1.2]': option.disabled,
      'text-sm leading-[1.43]': !option.disabled
    }"
    :style="{
      'padding-left': getTextIndentFromOption(option)
    }"
    >
      <span class="inline-flex gap-1 truncate justify-start">
        <span v-if="option.disabled && option.item.depth !== 0">›</span>
        {{ name }}
      </span>
      <IconCheck
        v-if="selected"
        class="w-5 h-5 absolute top-1/2 right-4 -translate-y-1/2 text-blue-700"
        aria-hidden="true" />
  </li>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { ExtractedOrganizationOption} from './types'

  type Props = {
    active: boolean
    option: ExtractedOrganizationOption,
    selected: boolean
  }

  const props = defineProps<Props>()

  const name = computed(() => props.option.item.value.name)

  function getTextIndentFromOption(option: ExtractedOrganizationOption): string
    {
      const base = 0.5;
      const depth = option.item.depth === 0
       ? 0
       : option.item.depth - 1
      return `${base * depth + 1}rem`
    }

</script>
