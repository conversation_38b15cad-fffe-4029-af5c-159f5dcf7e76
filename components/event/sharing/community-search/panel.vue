<template>
    <div>
        <UiDisclosure v-model:open="open" class="mb-8">
            <template #button>
                <span class="flex items-center gap-4 text-base"> <UiIcon name="information-circle" class="h-5 w-5" /> So geht das </span>
                <UiIcon
                    name="chevron-down"
                    class="text-gray-900transform h-6 w-6 transition-all"
                    :class="{ 'rotate-180': open, 'rotate-0': !open }" />
            </template>
            <div class="text-xs">
                Teile das Ereignis mit Helferinnen und Helfer anderer Gliederungen.
                Den Reiter „Gliederungen auswählen“ über diesem Hilfetext nutzt du, wenn du weißt, wo im Verbandsstufenbaum sich die Gliederungen befinden. Nutze ansonsten den Reiter „Gliederungen suchen“.
                Grundsätzlich gilt:<span class="font-bold">
                    Du teilst das Ereignis immer nur mit Gliederungen, die du links mit einem roten Punkt markiert hast.
                </span>
                Mehr Infos dazu siehst du im Video auf <NuxtLink :to="`https://drkserver.org/ereignis-teilen`" target="_blank" :external="true" class="underline text-blue-500">dieser Seite.</NuxtLink>
            </div>
        </UiDisclosure>
        <UiComboboxBatch
            label="Gliederung hinzufügen"
            placeholder="Für Vorschläge tippen"
            v-model="selected"
            :mutation="organizationsMutation"
            :limit="25"
            :showSummary="false"
            :get-option-label="getLabel"
            :get-option-disabled="(item) => getDisabled(item, event.organisation.id)"
            :initial-loading="false"
            :clearAfterSelecting="true"
            @update:onSelectedItem="updateSelectedOrganisations">
            <template #item="itemProps">
                <EventSharingCommunitySearchItem
                    :selected="itemProps.selected"
                    :active="itemProps.active"
                    :option="itemProps.option"
                />
            </template>
        </UiComboboxBatch>
        <EventSharingCommunityPickerList
            show-parents
            class="mt-8"
            :organisations="selectedOrganisations.map((item) => item.value)"
            @update:organisation="(id)=>emit('update:deleteOrganisation', id)"
            :isNavigationPossible="false"
            v-model="requests" />
    </div>
</template>
<script lang="ts" setup>
    import { Component } from '@nuxt/schema'
    import { Ref } from 'vue'
    import { useVModel } from '@vueuse/core'
    import { ExtractedOrganization } from './types'

    const props = defineProps<{
        as?: Component | HTMLElement
        modelValue: ApiModel<'EventShareRequest'>[]
        selectedOrganisations: ExtractedOrganization[]
    }>()

    type Emit = {
        (e: 'update:modelValue', value: ApiModel<'EventShareRequest'>[]): void
        (e: 'update:selectedOrganisations', value: Ref<ExtractedOrganization>): void
        (e: 'update:deleteOrganisation', value: number): void
    }

    const open = ref<boolean>(false)

    const emit = defineEmits<Emit>()

    const { event } = inject(EventSharingKey)

    const requests = useVModel(props, 'modelValue', emit)

    const selected: Ref<ExtractedOrganization> | null = ref(null)

    function updateSelectedOrganisations() {
        let isSelected= false
        if(selected.value){
            props.selectedOrganisations.forEach(item=>{
                if(item.id === selected.value.id) {
                    isSelected = true
                }
            })
            if (!isSelected) {
                emit('update:selectedOrganisations', selected)
            }
        }
    }

    function getLabel(item: any) {
        return Boolean(item.value) ? item.value.name : ''
    }

    function getDisabled(item: any, eventId: number) {
        if (item.value) {
            const isActive = item.value.active ?? false
            // we can not share event with ourselves
            const isSameOrganisation = item.value.id === eventId
            return isSameOrganisation ? isSameOrganisation : !isActive
        }
        return false
    }
    const { organizationsMutation } = useEventSharingExtractedOrganisation()
</script>
