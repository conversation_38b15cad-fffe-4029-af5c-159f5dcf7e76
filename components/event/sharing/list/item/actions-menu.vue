<script setup lang="ts">
    import { UseConfirmDialogReturn } from '@vueuse/core'
    import EventShareRequestStatus from '~/enums/event-share-request-status'

    export type Controllers = {
        cancel: UseConfirmDialogReturn<never, never, never>
        resend: UseConfirmDialogReturn<never, never, never>
    }

    const props = defineProps<{
        shareRequest: ApiModel<'EventShareRequest'>
        event: ApiModel<'Event'>
        controllers: Controllers
    }>()

    const shouldBeResendable = computed(() => {
        return props.shareRequest.status !== EventShareRequestStatus.ACCEPTED
    })
</script>

<template>
    <UiMenu>
        <EventSharingListItemResendAction
            v-if="shouldBeResendable"
            :share-request="props.shareRequest"
            :event="props.event"
            :controller="props.controllers.resend" />
        <EventSharingListItemCancelAction :share-request="props.shareRequest" :event="props.event" :controller="props.controllers.cancel" />
    </UiMenu>
</template>
