<script setup lang="ts">
    import { useConfirmDialog, useVModel } from '@vueuse/core'
    import { withoutAbbreviation } from '~/enums/community'
    import { Controllers } from '~/components/event/sharing/list/item/actions-menu.vue'

    const props = defineProps<{
        selected: boolean
        shareRequest: ApiModel<'EventShareRequest'>
        event: ApiModel<'Event'>
    }>()

    const emit = defineEmits<(event: 'update:selected', value: boolean) => any>()

    const isSelected = useVModel(props, 'selected', emit)

    const { organisationName, communityName} = unref(
        computed(() => {
            return {
                organisationName: props.shareRequest.targetOrganisation.name,
                communityName: withoutAbbreviation(props.shareRequest.targetGemeinschaft),
            }
        })
    )

    const resendController = useConfirmDialog()
    const cancelController = useConfirmDialog()

    const controllers = computed(() => {
        return {
            resend: resendController,
            cancel: cancelController
        } as Controllers
    })
</script>

<template>
    <div :class="{ 'bg-gold-50': isSelected }" class="grid grid-rows-4 md:grid-rows-1 grid-cols-12 mb-2 items-center w-full border-1 shadow rounded-sm border-gray-200">
        <div class="col-start-1 row-start-1 col-span-1 py-2 flex gap-2">
            <input
                type="checkbox"
                v-model="isSelected"
                class="form-checkbox indeterminate:icon-minus-green-600 h-5 w-5 ml-4 rounded-full"
            />
            <p class="text-sm md:hidden">auswählen</p>
        </div>
        <div class="row-start-2 col-start-1 md:row-start-1 md:hidden col-span-2 px-4">
            <p class="text-sm">Gliederung:</p>
        </div>
        <div class="row-start-2 col-start-6 md:col-start-2 col-span-8 md:row-start-1 md:col-span-4 px-4 py-4">
            <p class="text-sm">{{ organisationName }}</p>
            <p class="font-light">{{ communityName }}</p>
        </div>
        <div class="row-start-3 col-start-1 md:row-start-1 md:hidden col-span-3 md:col-span-2 px-4">
            <p class="text-sm">Geteilt am:</p>
        </div>
        <div class="row-start-3 col-start-6 md:col-start-6 md:row-start-1 md:col-span-2 px-2 py-4">{{ $formatDateWithAbbreviatedDay(new Date(props.shareRequest.lastShared)) }}</div>
        <div class="row-start-4 col-start-1 md:row-start-1 md:hidden col-span-2 px-4">
            <p class="text-sm">Rückmeldestatus:</p>
        </div>
        <div class="row-start-4 md:row-start-1 col-start-6 md:col-start-8 col-span-3 px-4 py-4">
            <EventSharingStatus :status="shareRequest.status" />
        </div>
        <div class="row-start-4 md:row-start-1 col-start-12 col-span-1 xl:px-8 py-4">
            <EventSharingListItemActionsMenu :share-request="shareRequest" :event="event" :controllers="controllers" />
        </div>
        <EventSharingListItemCancelDialog :controller="cancelController" />
        <EventSharingListItemResendDialog :controller="resendController" />
    </div>
</template>
