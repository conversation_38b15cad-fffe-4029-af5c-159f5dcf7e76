<script lang="ts" setup>
    import { UseConfirmDialogReturn } from '@vueuse/core'

    const props = defineProps<{
        controller: UseConfirmDialogReturn<boolean, any, any>
    }>()
</script>

<template>
    <UiConfirmDialog
        class="w-72"
        title="Nicht mehr teilen"
        :controller="controller"
        confirm-with="Nicht mehr teilen"
        cancel-with="Abbrechen"
        max-width-class="max-w-lg">
        Du bist dabei, das Teilen mit dieser Gliederung zurückzunehmen. Ereignismanager*innen, die deine Anfrage schon angenommen haben, bekommen eine Benachrichtigung, bereits eingeladene Helfer*innen dort aber nicht.
    </UiConfirmDialog>
</template>
