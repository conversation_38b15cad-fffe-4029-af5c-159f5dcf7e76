<script lang="ts" setup>
    import { useConfirmDialog, UseConfirmDialogReturn } from '@vueuse/core'

    const props = defineProps<{
        shareRequest: ApiModel<'EventShareRequest'>
        event: ApiModel<'Event'>
        controller: UseConfirmDialogReturn<never, never, never>
    }>()

    const { canCancel = false } = inject(EventPermissionsKey) || {}

    const { resendEventShareRequest } = useShareEventMutations(toRef(props, 'event'))

    const eventId = props.event.id
    const eventQueries = useEventQueries(eventId)
    provideEventQueries(eventId, eventQueries)
    const { refetch } = eventQueries.details()

    const runWithNotification = useRunWithNotification()

    async function triggerAction() {
        if (!(await props.controller.reveal()).isCanceled) {
            runWithNotification(async () => {
                await resendEventShareRequest([props.shareRequest])
                await refetch()
            }, {
                pending: `Deine Anfrage an ${props.shareRequest.targetOrganisation.name} wird noch einmal gesendet.`,
                success: `Das hat geklappt: Du hast deine Anfrage an ${props.shareRequest.targetOrganisation.name} noch einmal gesendet.`,
                error: `Du wolltest deine Anfrage an ${props.shareRequest.targetOrganisation.name} noch einmal senden. Das hat nicht geklappt.`
            })
        }
    }
</script>

<template>
    <UiMenuItem v-if="canCancel" @click="() => triggerAction()"> <UiIcon name="refresh" class="mr-2 inline h-5 w-5 flex-none text-sky-500" /> Noch einmal teilen </UiMenuItem>
</template>
