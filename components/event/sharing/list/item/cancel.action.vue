<script lang="ts" setup>
    import { UseConfirmDialogReturn } from '@vueuse/core'

    const props = defineProps<{
        shareRequest: ApiModel<'EventShareRequest'>
        event: ApiModel<'Event'>
        controller: UseConfirmDialogReturn<never, never, never>
    }>()

    const { deleteEventShareRequest } = useShareEventMutations(toRef(props, 'event'))

    const eventId = props.event.id
    const eventQueries = useEventQueries(eventId)
    provideEventQueries(eventId, eventQueries)

    const { refetch } = eventQueries.details()

    const runWithNotification = useRunWithNotification()

    async function triggerAction() {
        if (!(await props.controller.reveal()).isCanceled) {
            runWithNotification(async () => {
                await deleteEventShareRequest(props.shareRequest)
                await refetch()
            }, {
                pending: `Deine Anfrage an ${props.shareRequest.targetOrganisation.name} wird aufgehoben.`,
                success: `Das hat geklappt: Du hast deine Anfrage an ${props.shareRequest.targetOrganisation.name} aufgehoben.`,
                error: `Du wolltest deine Anfrage an ${props.shareRequest.targetOrganisation.name} aufheben. Das hat nicht geklappt.`
            })
        }
    }
</script>

<template>
    <div>
        <UiMenuItem v-if="event.detailedPermission.canEdit" @click="() => triggerAction()"> <UiIcon name="trash" class="mr-2 inline h-6 w-6 flex-none text-sky-500" /> Nicht mehr teilen </UiMenuItem>
    </div>
</template>
