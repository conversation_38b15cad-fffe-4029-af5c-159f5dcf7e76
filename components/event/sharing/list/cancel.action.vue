<script lang="ts" setup>
    import { useConfirmDialog } from '@vueuse/core'

    const props = defineProps<{
        shareRequests: ApiModel<'EventShareRequest'>[]
        event: ApiModel<'Event'>
    }>()

    const { canCancel = false } = inject(EventPermissionsKey) || {}

    const { deleteEventShareRequest } = useShareEventMutations(toRef(props, 'event'))

    const controller = useConfirmDialog()

    const eventId = props.event.id
    const eventQueries = useEventQueries(eventId)
    provideEventQueries(eventId, eventQueries)

    const { refetch } = eventQueries.details()

    const runWithNotification = useRunWithNotification()

    async function triggerAction() {
        if (!(await controller.reveal()).isCanceled) {
            runWithNotification(async () => {
                await Promise.all(props.shareRequests.map(async (shareRequest) => await deleteEventShareRequest(shareRequest)))
                await refetch()
            }, {
                pending: 'Deine <PERSON>fragen werden aufgehoben.',
                success: 'Das hat geklappt: Du hast deine Anfragen aufgehoben.',
                error: 'Du wolltest deine Anfragen aufheben. Das hat nicht geklappt.'
            })
        }
    }
</script>

<template>
    <button
        @click="() => triggerAction()"
        class="text-grey-500 ui-active:bg-grey-50 my-2 flex items-center rounded-md px-2 text-left text-sm transition-colors hover:bg-gray-200">
        <UiIcon name="trash" class="mr-2 inline h-6 w-6 flex-none text-sky-500" />
        Nicht mehr teilen
    </button>
    <EventSharingListCancelDialog :controller="controller" :amount="props.shareRequests.length" />
</template>
