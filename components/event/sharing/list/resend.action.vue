<script lang="ts" setup>
    import { useConfirmDialog } from '@vueuse/core'

    const props = defineProps<{
        shareRequests: ApiModel<'EventShareRequest'>[]
        event: ApiModel<'Event'>
    }>()

    const { canCancel = false } = inject(EventPermissionsKey) || {}

    const { resendEventShareRequest } = useShareEventMutations(toRef(props, 'event'))

    const controller = useConfirmDialog()

    const eventId = props.event.id
    const eventQueries = useEventQueries(eventId)
    provideEventQueries(eventId, eventQueries)
    const { refetch } = eventQueries.details()

    const runWithNotification = useRunWithNotification()

    async function triggerAction() {
        if (!(await controller.reveal()).isCanceled) {
            runWithNotification(async () => {
                await resendEventShareRequest(props.shareRequests)
                await refetch()
            }, {
                pending: 'De<PERSON> werden noch einmal gesendet.',
                success: 'Das hat geklappt: Du hast deine Anfragen noch einmal gesendet.',
                error: 'Du wolltest deine Anfragen noch einmal senden. Das hat nicht geklappt.'
            })
        }
    }
</script>

<template>
    <button
        @click="() => triggerAction()"
        class="text-grey-500 ui-active:bg-grey-50 my-2 flex items-center rounded-md px-2 text-left text-sm transition-colors hover:bg-gray-200">
        <UiIcon name="refresh" class="mr-2 inline h-5 w-5 flex-none text-sky-500" />
        Noch einmal teilen
    </button>
    <EventSharingListResendDialog :controller="controller" :amount="shareRequests.length" />
</template>
