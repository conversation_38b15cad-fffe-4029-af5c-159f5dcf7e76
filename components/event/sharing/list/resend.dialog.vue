<script lang="ts" setup>
    import { UseConfirmDialogReturn } from '@vueuse/core'

    const props = defineProps<{
        controller: UseConfirmDialogReturn<boolean, any, any>
        amount: number
    }>()
</script>

<template>
    <UiConfirmDialog
        class="w-72"
        title="Noch einmal teilen"
        :controller="controller"
        confirm-with="Noch einmal teilen"
        cancel-with="Abbrechen"
        max-width-class="max-w-lg">
        Möchtest du das Ereignis noch einmal mit {{ props.amount }} Gliederungen teilen? Ereignismanager*innen, die deine Anfrage schon angenommen haben, bekommen dazu keine Benachrichtigung. Für sie ändert sich also nichts.
    </UiConfirmDialog>
</template>
