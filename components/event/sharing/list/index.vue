<script lang="ts" setup>
    import EventShareRequestStatus from '~/enums/event-share-request-status'

    export type ShareRequestGroup = {
        organisation?: { id: number; name: string }
        shareRequests: ApiModel<'EventShareRequest'>[]
    }

    const props = defineProps<{
        shareRequests: ApiModel<'EventShareRequest'>[]
        event: ApiModel<'Event'>
        filterQuery: string
    }>()

    enum SortType {
        Hierarchy,
        Alphabetical,
        ReverseAlphabetical,
        Status
    }

    const sortOptions: Option[] = [
        { label: 'Hierarchisch', id: SortType.Hierarchy },
        { label: 'Gliederung A-Z', id: SortType.Alphabetical },
        { label: 'Gliederung Z-A', id: SortType.ReverseAlphabetical },
        { label: 'Rückmeldestatus', id: SortType.Status }
    ]

    const currentSort = ref<Option>(sortOptions[0])

    const ids = computed(() => props.shareRequests.map((shareRequest) => shareRequest.id.toString(10)))
    const selectableList = useSelectableList(ids)
    const { isAnyItemSelected, selectedItems, selectableItems, toggleAllItems } = selectableList

    const groupedShareRequests = computed((): ShareRequestGroup[] => {
        const shareRequests = [...props.shareRequests]
        switch (currentSort.value.id) {
            case SortType.Hierarchy: {
                const grouped = new Map<number, ShareRequestGroup>()
                shareRequests.forEach((shareRequest) => {
                    const organisation = shareRequest.targetOrganisation.parents?.[0] ?? shareRequest.targetOrganisation
                    const id = organisation.id ?? 0

                    if (grouped.has(id)) grouped.get(id).shareRequests.push(shareRequest)
                    else grouped.set(id, { organisation, shareRequests: [shareRequest] })
                })

                return Array.from(grouped.values())
            }
            case SortType.Alphabetical:
                return [{ shareRequests: shareRequests.sort((a, b) => a.targetOrganisation.name.localeCompare(b.targetOrganisation.name)) }]
            case SortType.ReverseAlphabetical:
                return [{ shareRequests: shareRequests.sort((a, b) => b.targetOrganisation.name.localeCompare(a.targetOrganisation.name)) }]
            case SortType.Status:
                return [
                    { shareRequests: shareRequests.filter((shareRequest) => shareRequest.status === EventShareRequestStatus.OPEN) },
                    { shareRequests: shareRequests.filter((shareRequest) => shareRequest.status === EventShareRequestStatus.ACCEPTED) },
                    { shareRequests: shareRequests.filter((shareRequest) => shareRequest.status === EventShareRequestStatus.REJECTED) }
                ]
        }
    })

   const filteredGroupShareRequests = computed(() =>
        groupedShareRequests.value
            .map(({ organisation, shareRequests }) => {
                const filtered = shareRequests.filter(({ targetOrganisation }) =>
                    targetOrganisation.name.toLocaleLowerCase().includes(props.filterQuery.toLocaleLowerCase())
                )
                return { organisation, shareRequests: filtered }
            })
            .filter(group => group.shareRequests.length > 0)
    )

    const selectedShareRequests = computed(() => {
        return filteredGroupShareRequests.value.map(request=>{
            return request.shareRequests
        })
        .flat()
        .filter((shareRequest) => selectedItems.value.includes(shareRequest.id.toString(10)))
    })

</script>

<template>
    <UiMenu button-size="sm" class="flex flex-row items-center sm:justify-end py-5 text-xs">
        <template #button>
            <UiIcon :name="currentSort.id !== SortType.ReverseAlphabetical ? 'sort-ascending' : 'sort-descending'" class="h-auto w-3" />
        {{ currentSort.label }}
        </template>
        <UiMenuItem v-for="{ label, id } in sortOptions" :key="id" @click="currentSort = sortOptions[id]">
            {{ label }}
        </UiMenuItem>
    </UiMenu>
    <div class="text-grey-900 w-full border-spacing-x-0 border-spacing-y-2 text-xs">
        <div class="relative" :class="isAnyItemSelected ? 'bg-gray-100' : ''">
            <div class="relative h-12 items-center grid grid-cols-12">
                <div class="col-span-1 col-start-1 pl-4">
                    <label class="cursor-pointer">
                        <input
                            @click="toggleAllItems"
                            type="checkbox"
                            :checked="selectedItems.length === props.shareRequests.length ? true : false"
                            class="form-checkbox indeterminate:icon-minus-green-600 h-5 w-5 rounded-full"/>
                    </label>
                </div>
                <div v-if="!isAnyItemSelected" class="md:hidden col-start-2 pt-1 md:pt-0 col-span-6 px-4 font-normal">Alle auswählen</div>
                <div v-if="!isAnyItemSelected" class="hidden md:block col-start-2 col-span-4 px-4 font-normal">Gliederung</div>
                <div v-if="!isAnyItemSelected" class="hidden md:block col-start-6 col-span-2 px-2 font-normal">Geteilt am</div>
                <div v-if="!isAnyItemSelected" class="hidden md:block col-start-8 col-span-3 px-4 font-normal">Rückmeldestatus</div>
                <div v-if="!isAnyItemSelected" class="hidden md:block px-2 font-normal" aria-label=""><span class="sr-only">Aktionen</span></div>
            </div>
            <div v-if="isAnyItemSelected"
                class="absolute top-0 h-12 left-10 flex flex-row justify-start md:gap-5 bg-gray-100"
                :class="isAnyItemSelected ? 'pointer-events-auto visible' : 'pointer-events-none invisible'">
                <EventSharingListResendAction :share-requests="selectedShareRequests" :event="event" />
                <EventSharingListCancelAction :share-requests="selectedShareRequests" :event="event" />
            </div>
        </div>
        <div class="flex flex-col">
            <EventSharingListGroup
                v-for="group in filteredGroupShareRequests"
                :share-requests="group"
                :event="event"
                :key="group.shareRequests[0]?.id"
                :selectable-list="selectableList"
                v-bind="$attrs"/>
        </div>
    </div>
</template>

<!--suppress CssUnusedSymbol -->
<style lang="pcss" scoped>

    .v-enter-active,
    .v-leave-active {
        transition: opacity 0.3s ease;
    }

    .v-enter-from,
    .v-leave-to {
        opacity: 0;
    }
</style>
