<script setup lang="ts">
    import { ShareRequestGroup } from '~/components/event/sharing/list/index.vue'

    const props = defineProps<{
        selectableList: ReturnType<typeof useSelectableList>
        shareRequests: ShareRequestGroup
        event: ApiModel<'Event'>
    }>()

    const { isAnyItemSelected, selectedItems, selectableItems, toggleAllItems } = props.selectableList
</script>

<template>
    <div v-if="Boolean(props.shareRequests.organisation)">
        <div>
            <div class="pt-4">
                {{ props.shareRequests.organisation.name }}
            </div>
        </div>
    </div>
    <EventSharingListItem
        v-for="request in shareRequests.shareRequests"
        :share-request="request"
        :event="event"
        v-model:selected="selectableItems[request.id]"
        :key="request.id"
        v-bind="$attrs" />
</template>
