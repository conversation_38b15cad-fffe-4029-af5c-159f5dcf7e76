<script lang="ts" setup>
    import EventShareRequestStatus from '~~/enums/event-share-request-status'

    defineProps<{
        status: EventShareRequestStatus
        short?: boolean
        monochromatic?: boolean
        checked?: boolean
    }>()
</script>

<template>
    <span v-if="status === EventShareRequestStatus.ACCEPTED" class="relative inline-flex  gap-1 xl:gap-0 flex-row justify-start w-full items-center">
        <IconCheck
            class="inline-flex aspect-square h-6 flex-none items-center justify-center rounded-full bg-green-200 py-1"
            :class="monochromatic && `border border-blue-500 bg-transparent !py-0.5 text-blue-500`" />
        <span class="xl:ml-2 text-center leading-tight">angenommen</span>
        <span v-if="checked" class="absolute inset-y-0 right-0 flex items-center text-blue-700">
            <IconCheck class="h-6 w-6" aria-hidden="true" />
        </span>
    </span>

    <span v-else-if="status === EventShareRequestStatus.REJECTED" class="relative inline-flex  gap-1 xl:gap-0 flex-row justify-start w-full items-center">
        <IconX
            class="bg-softred-300 inline-flex aspect-square h-6 flex-none items-center justify-center rounded-full py-1"
            :class="monochromatic && `border border-blue-500 bg-transparent !py-0.5 text-blue-500`" />
        <span class="xl:ml-2 text-center leading-tight">abgelehnt</span>
        <span v-if="checked" class="absolute inset-y-0 right-0 flex items-center text-blue-700">
            <IconCheck class="h-6 w-6" aria-hidden="true" />
        </span>
    </span>

    <span v-else-if="status === EventShareRequestStatus.OPEN" class="relative inline-flex gap-1 xl:gap-0 flex-row justify-start w-full items-center">
        <IconQuestionMark
            class="inline-flex aspect-square h-6 flex-none items-center justify-center rounded-full bg-blue-200 py-1"
            :class="monochromatic && `border border-blue-500 bg-transparent !py-0.5 text-blue-500`" />
        <span class="xl:ml-2 text-center leading-tight">{{ short ? 'offen' : 'Rückmeldung offen' }}</span>
        <span v-if="checked" class="absolute inset-y-0 right-0 flex items-center text-blue-700">
            <IconCheck class="h-6 w-6" aria-hidden="true" />
        </span>
    </span>
</template>
