<template>
    <div>
        <div class="flex flex-wrap md:justify-between items-center  gap-y-2 sm:gap-y-0 gap-x-2">
            <input type="text" placeholder="Gliederung durchsuchen" class="search-input" v-model="filterQuery" />
            <button class="form-button button-contained button-sm " @click="openShareEventDialog">Gliederungen finden</button>
        </div>
        <EventSharingList v-if="shareRequestsCount > 0" :share-requests="props.event.shareRequests" :event="event" :filterQuery="filterQuery" />
        <div v-else class="text-softred-900 bg-softred-50 mt-8 flex flex-grow flex-col items-center px-4 xl:px-36 py-20 text-center">
            <h3 class="pb-2 text-lg">Ereignis noch nicht geteilt</h3>
            <p>Teile ein Ereignis mit anderen Gliederungen. Helfende dieser Gliederung können sich zu dem Ereignis melden, wenn mindestens ein*e Ereignismanager*in der Zielgliederung das Teilen annimmt. Gibt es vor Ort keine*n Ereignismanager*in, können Helfende sich zu dem Ereignis melden, sobald du es geteilt hast.</p>
        </div>
        <EventSharingDialog :controller="shareEventDialog" />
    </div>
</template>

<script lang="ts" setup>
    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const filterQuery = ref<string>('')

    const eventSharing = useEventSharing(toRef(props, 'event'))
    provide(EventSharingKey, eventSharing)

    const shareRequestsCount = computed(() => props.event.shareRequests?.length ?? 0)

    const shareEventDialog = useEventSharingDialogController('shareEventDialog')

    const eventId = props.event.id
    const eventQueries = useEventQueries(eventId)
    provideEventQueries(eventId, eventQueries)

    const { refetch } = eventQueries.details()

    const runWithNotification = useRunWithNotification()

    async function openShareEventDialog() {
        const { data, isCanceled } = await shareEventDialog.reveal()
        if (!isCanceled) {
            const organisationsNames: string = data.map(organisation=>{
               return organisation.targetOrganisation.name
            }).join(', ')

            runWithNotification(async () => {
                await eventSharing.commit('createEventShareRequest', data)
                await refetch()
            }, {
                pending: `Das Ereignis wird mit ${organisationsNames} geteilt.`,
                success: `Das hat geklappt: Du hast das Ereignis mit ${organisationsNames} geteilt.`,
                error: `Du wolltest das Ereignis mit ${organisationsNames} teilen. Das hat nicht geklappt.`
            })
        }
    }
</script>

<style scoped>
    .search-input {
        @apply form-input py-[.25rem] text-sm leading-6 basis-full sm:basis-[unset];
        @apply pl-[1.5rem] bg-no-repeat icon-search-sky-500 bg-[length:1rem_1rem] bg-[.25rem_center]
    }

     .search-box {
        @apply icon-search-sky-500 form-input box-border w-full bg-no-repeat pl-11 disabled:pointer-events-none disabled:opacity-60;
        @apply bg-[length:1.5rem_1.5rem] bg-[position:left_.5rem_center]
    }

</style>
