<template>
    <div class="mb-2 mt-4 text-xs" v-if="showParent">{{ parentName }}</div>
    <div class="mb-2">
        <div class="flex p-4 shadow hover:bg-gray-50" :class="{ 'bg-gold-50': isActive }">
            <div class="w-8">
                <UiPopper :text="popperText" :showPopper="isItemDisabled">
                    <UiCheckInput v-model="isActive" circle :label="''" :disabled="isItemDisabled" />
                </UiPopper>
            </div>
            <div
                class="hover:cursor-pointer"
                :class="{ 'hover:text-sky-500': isNavigationPossible }"
                @click="isNavigationPossible ? emit('update:organisation', organisation.id) : (isActive = !isActive)">
                {{ organisation.name }}
            </div>
            <div class="ml-auto">
                <div v-if="!isNavigationPossible" class="flex flex-col">
                    <UiIcon
                        name="trash"
                        @click="emit('update:organisation', organisation.id)"
                        class="mr-2 inline h-5 w-5 flex-none text-sky-500 hover:cursor-pointer hover:bg-blue-50 hover:ring-4 hover:ring-blue-50" />
                </div>
            </div>
        </div>
        <div class="mb-2 mt-1 flex" v-if="isActive">
            <UiBadgeSelect
                :options="definedCommunities"
                v-model="selectedCommunities"
                :selection-handler="selectCommunity"
                :label-handler="(item)=> item === 'ALL' ? 'Gesamt' : item"
            />
        </div>
    </div>
</template>

<script lang="ts" setup>
    import Community from '~~/enums/community'

    const props = defineProps<{
        organisation: ApiModel<'OrganizationWithParent'>
        isSelected: boolean
        communities: Community[]
        showParent?: boolean
        isNavigationPossible?: boolean
    }>()

    type Emit = {
        (e: 'update', value: ApiModel<'EventShareRequest'>[]): void
        (e: 'update:organisation', value: number): void
        (e: 'update:isSelected', value: boolean): void
    }

    const emit = defineEmits<Emit>()

    const isActive = ref(props.isSelected)

    const selectItem = inject<Ref<boolean>>('selectItemWhenSelectedFromSearchInput')

    if(selectItem.value && !props.isNavigationPossible) {
        isActive.value = true
        selectItem.value = false
    }

    const { event } = inject(EventSharingKey)

    const isItemDisabled = ref<boolean>(event.value.organisation.id === props.organisation.id)
    const popperText = ref<string>(isItemDisabled.value ? 'Du kannst das Ereignis nicht mit deiner Gliederung teilen' : '')

    const { definedCommunities, selectedCommunities, selectCommunity } = useCommunities(null, props.communities)

    watch(
        () => props.isSelected,
        () => {
            isActive.value = props.isSelected
        }
    )

    const parentName = computed(() => {
        return props.organisation.parents.length ? props.organisation.parents[0].name : ''
    })

    watch(
        [isActive, selectedCommunities],
        () => {
            const requests = isActive.value
                ? selectedCommunities.value.map((community) => {
                      return useModelFactory('EventShareRequest').create({
                          targetOrganisation: props.organisation,
                          targetGemeinschaft: community
                      })
                  })
                : [
                      useModelFactory('EventShareRequest').create({
                          targetOrganisation: props.organisation,
                          targetGemeinschaft: null
                      })
                  ]
            emit('update', requests)
        },
        { immediate: true }
    )
</script>

<style lang="pcss" scoped>
    .shadow {
        box-shadow: 0 1px 3px 0 rgba(54, 54, 52, 0.1), 0 0 3px -1px rgba(0, 0, 0, 0.15);
    }
</style>
