<template>
    <component :is="as || 'div'">
        <UiLoader :is-loading="isLoading">
            <UiDisclosure v-model:open="open" class="mb-8">
                <template #button>
                    <span class="flex items-center gap-4 text-base"> <UiIcon name="information-circle" class="h-5 w-5" /> So geht das </span>
                    <UiIcon
                        name="chevron-down"
                        class="text-gray-900transform h-6 w-6 transition-all"
                        :class="{ 'rotate-180': open, 'rotate-0': !open }" />
                </template>
                <div class="text-xs">
                    Teile das Ereignis mit Helferinnen und Helfer anderer Gliederungen.
                    Den Reiter „Gliederungen auswählen“ über diesem Hilfetext nutzt du, wenn du weißt, wo im Verbandsstufenbaum sich die Gliederungen befinden. Nutze ansonsten den Reiter „Gliederungen suchen“.
                    Grundsätzlich gilt:<span class="font-bold">
                        Du teilst das Ereignis immer nur mit Gliederungen, die du links mit einem roten Punkt markiert hast.
                    </span>
                    Mehr Infos dazu siehst du im Video auf <NuxtLink :to="`https://drkserver.org/ereignis-teilen`" target="_blank" :external="true" class="underline text-blue-500">dieser Seite.</NuxtLink>
                </div>
            </UiDisclosure>
            <div class="relative">
                <Breadcrumbs class="mr-16" @change="loadOrganisation" :organisation="parentOrganisation" />
                <IconHomeOutlined
                    class="absolute right-4 top-0 h-8 w-8 text-sky-500 opacity-50 hover:cursor-pointer hover:opacity-75"
                    @click="loadDefaultOrganisation" />
            </div>
            <div class="flex justify-end">
                <button type="button" class="form-button mb-4" @click="selectAll">Alle auswählen</button>
            </div>
            <UiNotification
                v-if="isLastChildOrganisation && !isLoading"
                :content="`${data?.items[0].name} hat keine Untergliederungen. Gemeinschaften wählst du aus, indem du den Button links neben dem Namen anklickst.`"
                type="soft-error"
                :isFinal="true"
                @dismissed="() => isLastChildOrganisation = !isLastChildOrganisation"
                class="pointer-events-auto mr-6 fixed bottom-[6rem]"
            />
            <EventSharingCommunityPickerList :organisations="childOrganisations" v-model="requests" @update:organisation="loadOrganisation" />
        </UiLoader>
    </component>
</template>
<script lang="ts" setup>
    import { useVModel } from '@vueuse/core'
    import Community from '~~/enums/community'
    import Breadcrumbs from '~/components/event/sharing/community-picker/breadcrumbs.vue'

    type DefaultSearchListResponse = {
        items: ApiModel<'OrganizationWithHierarchy'>[]
        totalItems: number
    }
    const mocked = false

    const props = defineProps<{
        as?: Component | HTMLElement
        modelValue: ApiModel<'EventShareRequest'>[]
    }>()

    type Emit = {
        (e: 'update:modelValue', value: ApiModel<'EventShareRequest'>[]): void
    }

    const emit = defineEmits<Emit>()

    const open = ref<boolean>(false)

    const requests = useVModel(props, 'modelValue', emit)

    const { event } = inject(EventSharingKey)

    const { data, organisationSearch, isLoading } = useOrganisationSearch()

    const isLastChildOrganisation = ref<boolean>(false)

    const initialOrganisationId = ref(!mocked ? event.value.organisation.id : 5)
    const currentOrganisationId = ref(!mocked ? event.value.organisation.id : 5)

    const childOrganisations = ref<ApiModel<'OrganizationWithHierarchy'>[]>([])
    const parentOrganisation = ref<ApiModel<'OrganizationWithHierarchy'>>(null)

    await loadDefaultOrganisation()

    watch(
        data,
        (updatedData) => {
            if (updatedData && updatedData.items.length !==1) {
                childOrganisations.value = updatedData.items.filter((organisation) => {
                    return organisation.id !== currentOrganisationId.value
                })
                parentOrganisation.value = updatedData.items.find((organisation) => {
                    return organisation.id === currentOrganisationId.value
                })
                isLastChildOrganisation.value = false
            }
            if (updatedData && updatedData.items.length ===1) {
                isLastChildOrganisation.value = true
            }
        },
        { deep: true, immediate: true }
    )

   async function loadDefaultOrganisation() {
         const data = await $fetch<DefaultSearchListResponse>(`/api/organisations`, {
            body: {
                offset: 0,
                limit: 25,
                sortBy: [
                    {
                        field: 'CHAIN_OF_COMMAND_ENABLED',
                        directionAsc: true
                    }
                ],
                types: ["F", "M", "LD", "B"],
                organisationIdsWithChilds: "DIRECT",
                searchTerm: "",
                organisationIds: [event.value.organisation.id]
            },
            method: 'post'
        })

        // Check if this is basic organisation and then search for parent for default list of organisations
        if(data.totalItems === 1) {
            initialOrganisationId.value = data.items[0].parentInChainOfCommand
        }
        currentOrganisationId.value = initialOrganisationId.value
        organisationSearch({ organisationIds: [initialOrganisationId.value], limit: 1000, organisationIdsWithChilds: "DIRECT" })
    }

    function loadOrganisation(id: number) {
        currentOrganisationId.value = id
        organisationSearch({ organisationIds: [id], limit: 10000, organisationIdsWithChilds: "DIRECT" })
    }

    function selectAll() {
        const alreadySelectedRequests = requests.value

        const newRequests = childOrganisations.value
            .filter(org => org.id !== event.value.organisation.id)
            .filter(org =>
            !alreadySelectedRequests.some(req => req.targetOrganisation.id === org.id)
            )
            .map(org =>
            useModelFactory('EventShareRequest').create({
                targetOrganisation: org,
                targetGemeinschaft: Community.ALL
            })
            )

        requests.value = [...alreadySelectedRequests, ...newRequests]
    }

</script>
