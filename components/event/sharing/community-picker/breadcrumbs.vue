<template>
    <div class="mb-4 w-2/3">
        <span
            v-for="parentOrganisation in parents"
            class="text-sm hover:cursor-pointer hover:text-gray-600"
            @click="emit('change', parentOrganisation.id)">
            {{ parentOrganisation.name }} >
        </span>
        <span class="font-medium"> {{ name }} </span>
    </div>
</template>
<script lang="ts" setup>
    const props = defineProps<{
        organisation: ApiModel<'OrganizationWithHierarchy'>
    }>()

    const name = computed(() => props.organisation?.name ?? '')
    const parents = computed(() => {
        if (!props?.organisation?.parents) {
            return []
        }
        const reversed = props.organisation.parents ? [...props.organisation.parents] : []
        reversed.reverse()
        return reversed
    })

    type Emit = {
        (e: 'change', value: number): void
    }

    const emit = defineEmits<Emit>()
</script>
