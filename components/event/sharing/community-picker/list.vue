<template>
    <div>
        <EventSharingCommunityPickerItem
            v-for="organisation in organisations"
            :key="organisation.id"
            :show-parent="showParents"
            :organisation="organisation"
            :is-selected="Boolean(eventShareRequests.find((r) => r.targetOrganisation.id === organisation.id))"
            :communities="communitiesPerOrganizationWithDefaults[organisations.indexOf(organisation)]"
            :isNavigationPossible="isNavigationPossible"
            @update:organisation="updateOrganisation"
            @update="updateEventShareRequests" />
    </div>
</template>
<script lang="ts" setup>
    import { useVModel } from '@vueuse/core'
    import Community from '~~/enums/community'

    type Props = {
        organisations: ApiModel<'Organization'>[],
        modelValue: ApiModel<'EventShareRequest'>[]
        showParents?: boolean,
        isNavigationPossible?: boolean,
    }

    const props = withDefaults(defineProps<Props>(),
    {
        isNavigationPossible: true,
    }
)

    type Emit = {
        (e: 'update:modelValue', value: ApiModel<'EventShareRequest'>[]): void
        (e: 'update:organisation', value: number): void
    }

    const emit = defineEmits<Emit>()

    const eventShareRequests = useVModel(props, 'modelValue', emit)

    const isCommunity = (c: Community): c is Community => Object.values(Community).includes(c)

    const communitiesPerOrganization = computed(() => {
        return props.organisations.map((organisation) => {
            return eventShareRequests.value
                .filter((r) => r.targetOrganisation.id === organisation.id)
                .map((r) => r.targetGemeinschaft)
                .filter(isCommunity)
        })
    })

    const communitiesPerOrganizationWithDefaults = computed(() => {
        return communitiesPerOrganization.value.map((communities) => {
            return communities.length ? communities : [Community.ALL]
        })
    })

    function updateEventShareRequests(requests: ApiModel<'EventShareRequest'>[]) {
        const filteredRequests = eventShareRequests.value.filter((r) => r.targetOrganisation.id !== requests[0].targetOrganisation.id)
        eventShareRequests.value = requests.find((r) => r.targetGemeinschaft === null) ? filteredRequests : filteredRequests.concat(requests)
    }

    function updateOrganisation(id: number) {
        emit('update:organisation', id)
    }
</script>
