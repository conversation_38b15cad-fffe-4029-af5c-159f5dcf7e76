<template>
    <div v-if="shareRequestsReceived.length > 0">
        <div class="relative flex">
            <h4 class="mb-3 text-base font-semibold">Ereign<PERSON> geteilt</h4>
            <UiIcon
                v-if="shareRequestsReceived.length > 3"
                :name="displayRequests ? 'chevron-up' : 'chevron-down'"
                @click="toggleDisplayRequests"
                class="absolute right-0 top-0 h-6 w-6 text-blue-500 hover:cursor-pointer" />
        </div>

        <div v-if="shareRequestsReceived.length > 3" class="mb-4 text-sm">{{ shareRequestsReceived.length }} mal mit dir geteilt.</div>

        <div v-if="isAnyRequestOpen" class="bg-softred-50 item mb-2 flex items-center px-2 py-1 font-light text-red-500">
            <IconExclamationCircle class="absolute h-5 w-5" />
            <div class="text-sm ml-8">Jemand hat eure Gliederung bei diesem Ereignis um Hilfe gebeten. Möchtest du dieses Ereignis jetzt mit den Helfenden bei dir vor Ort teilen?</div>
        </div>

        <div v-if="shareRequestsReceived.length > 3">
            <div v-if="displayRequests">
                <div v-for="request in shareRequestsReceived">
                    <EventShareRequest :request="request" :event="event" />
                </div>
            </div>
            <hr v-else class="border-t-grey-100 mb-3 mt-6" />
        </div>

        <div v-else>
            <div v-for="request in shareRequestsReceived">
                <EventShareRequest :request="request" :event="event" />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
    import { EventSharingSectionRequest as EventShareRequest } from '#components'
    import EventShareRequestStatus from '~~/enums/event-share-request-status'

    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const eventSharing = useEventSharing(toRef(props, 'event'))
    provide(EventSharingKey, eventSharing)

    const { shareRequestsReceived } = eventSharing

    const displayRequests = ref(false)

    function toggleDisplayRequests() {
        displayRequests.value = !displayRequests.value
    }

    const isAnyRequestOpen = computed(() => {
        return !!shareRequestsReceived.value.find((request) => request.status === EventShareRequestStatus.OPEN)
    })
</script>
