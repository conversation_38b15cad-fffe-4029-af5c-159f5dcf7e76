<template>
    <UiMenu class="aria-disabled:pointer-events-none">
        <template #button>
            <EventSharingStatus data-size="xl" :status="status" class="text-xs text-yellow-900" />
        </template>

        <UiMenuItem
            @click="updateStatus(EventShareRequestStatus.ACCEPTED)"
            class="ui-disabled:opacity-40 ui-disabled:italic ui-disabled:cursor-not-allowed text-xs text-yellow-900">
            <EventSharingStatus
                data-size="xl"
                :status="EventShareRequestStatus.ACCEPTED"
                :checked="status === EventShareRequestStatus.ACCEPTED"
                class="text-xs text-yellow-900" />
        </UiMenuItem>

        <UiMenuItem
            @click="updateStatus(EventShareRequestStatus.REJECTED)"
            class="ui-disabled:opacity-40 ui-disabled:italic ui-disabled:cursor-not-allowed text-xs text-yellow-900">
            <EventSharingStatus data-size="xl" :status="EventShareRequestStatus.REJECTED" :checked="status === EventShareRequestStatus.REJECTED" />
        </UiMenuItem>
    </UiMenu>
</template>

<script lang="ts" setup>
    import EventShareRequestStatus from '~~/enums/event-share-request-status'

    const props = defineProps<{
        eventShareRequest: ApiModel<'EventShareRequest'>
        event: ApiModel<'Event'>
    }>()

    const { commit } = inject(EventSharingKey)

    const eventId = props.event.id
    const eventQueries = useEventQueries(eventId)
    provideEventQueries(eventId, eventQueries)

    const { refetch } = eventQueries.details()

    const status = computed(() => props.eventShareRequest.status)

    const runWithNotification = useRunWithNotification()

    async function updateStatus(newStatus: EventShareRequestStatus.REJECTED | EventShareRequestStatus.ACCEPTED) {

        let messages = {
                pending: 'Deine Rückmeldung wird gespeichert.',
                success: 'Das hat geklappt: Du hast die Anfrage angenommen.',
                error: 'Du wolltest die Anfrage annehmen. Das hat nicht geklappt.'
            }

        if(newStatus === 'REJECTED' ) {
            messages = {
                pending: 'Deine Rückmeldung wird gespeichert.',
                success: 'Das hat geklappt: Du hast die Anfrage abgelehnt.',
                error: 'Du wolltest die Anfrage ablehnen. Das hat nicht geklappt.'
            }
        }

        runWithNotification(async () => {
            await commit('sendEventShareRequestDecision', [props.eventShareRequest.id, newStatus])
            await refetch()
        }, messages)
    }
</script>
