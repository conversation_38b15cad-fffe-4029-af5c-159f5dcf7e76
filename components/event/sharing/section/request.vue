<template>
    <div class="my-1">
        <div class="my-1 text-sm leading-8">
            <div><span class="font-light"> geteilt mit:</span> {{ organizationName }}, {{ targetGemeinschaft }}</div>
            <div><span class="font-light">geteilt am: </span>{{ $formatDateWithAbbreviatedDayAndTime(new Date(props.request.lastShared)) }}</div>
        </div>
        <EventSharingSectionResponseMenu :event-share-request="request" :event="event" />
        <hr class="border-t-grey-100 my-3" />
    </div>
</template>

<script lang="ts" setup>

    const props = defineProps<{
        request: ApiModel<'EventShareRequest'>
        event: ApiModel<'Event'>
    }>()

    const organizationName = computed(()=>{
        return props.request.targetOrganisation.name
    })
    const targetGemeinschaft = computed(()=>{
        return props.request.targetGemeinschaft === 'ALL' ? 'Gesamte Gliederung' : props.request.targetGemeinschaft

    })
</script>
