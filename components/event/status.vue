<template>
    <div
        class="cursor-default group-hover/interactive:cursor-pointer"
        :class="{ 'flex items-center truncate': withLabel }"
        ref="trigger"
        @mouseenter="showPopperHandler"
        @mouseleave="hidePopperHandler">
        <IconUpcoming class="mr-2 w-6 flex-none" v-if="status.isWaitingForApproval" />
        <IconCheckCircle class="mr-2 w-6 flex-none" v-if="status.isApproved" />
        <IconShieldCheck class="mr-2 w-6 flex-none" v-if="status.isFinished" />
        <IconCanceled class="mr-2 w-6 flex-none" v-if="status.isCanceled" />
        <span v-if="withLabel">{{ status.label }}</span>
    </div>
    <div
        ref="popup"
        v-if="!withLabel"
        class="px-3 py-1 text-xs tracking-tight text-black md:text-sm"
        :class="{
            'bg-gray-200': status.isCanceled || status.isFinished,
            'bg-gold-300': status.isWaitingForApproval,
            'bg-green-400': status.isApproved,
            'hidden': !popperIsShown,
            'inline-block': popperIsShown
        }">
        {{ status.info }}
    </div>
</template>
<script setup lang="ts">
    const props = defineProps<{
        status: ApiModel<'Event'>['status']
        withLabel?: boolean
    }>()

    const status = computed(() => useEventStatus(props.status))

    const trigger = ref()
    const popperIsShown = ref(false)

    const popup = ref()
    const popperInstance = usePopper(trigger, popup, {
        placement: 'top-end',
        modifiers: [
            {
                name: 'offset',
                options: {
                    offset: [0, -5]
                }
            }
        ]
    })

    const showPopperHandler = () => {
        popperIsShown.value = true
        popperInstance.value.update()
    }

    const hidePopperHandler = () => (popperIsShown.value = false)
</script>
