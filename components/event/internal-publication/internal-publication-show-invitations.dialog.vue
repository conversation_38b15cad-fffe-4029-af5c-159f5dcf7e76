<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel">
        <template #title>Alle Einladungen</template>
        <div class="flex flex-col gap-8">
            <template v-for="publication in internalPublications" :key="publication.id">
                <div class="mb-4 flex w-full flex-col gap-4 rounded border p-4 font-light shadow-sm">
                    <div class="flex items-center justify-between">
                        <div class="flex flex-col">
                            <span class="text-xs font-normal">Erstellt</span>
                            {{ $formatDateTime(new Date(publication.recievingDate)) }}
                        </div>
                        <div class="flex flex-col gap-2 sm:flex-row">
                            <button
                                aria-label="Veröffentlichung bearbeiten"
                                type="button"
                                @click="
                                    () => {
                                        action.internalPublicationInviteHelp(publication)
                                        cancel()
                                    }
                                ">
                                <UiIcon name="pencil" class="inline h-6 w-6 cursor-pointer text-blue-500" />
                            </button>

                            <button aria-label="Veröffentlichung löschen" type="button" @click="() => action.removeInternalPublication(publication)">
                                <UiIcon name="trash" class="inline h-6 w-6 cursor-pointer text-blue-500" />
                            </button>
                        </div>
                    </div>
                    <div class="group flex flex-col">
                        <span class="text-xs font-normal">Kriterien/Einsatzqualifikationen</span>
                        <EventInternalPublicationInvitations
                            v-if="!isLoading"
                            :internal-publications="publication"
                            :event="event"
                            :is-clickable="false" />
                    </div>
                    <div class="flex flex-col">
                        <span class="text-xs font-normal">Anmerkung</span>
                        <span :class="{ 'text-grey-500': !publication.remark }">{{ publication.remark || 'Keine Angabe' }}</span>
                    </div>
                </div>
            </template>
        </div>

        <template #footer>
            <div class="flex w-full justify-between">
                <button
                    @click="
                        () => {
                            action.internalPublicationInviteHelp()
                            cancel()
                        }
                    "
                    class="form-button button-contained-secondary text-sky-500">
                    <UiIcon name="plus" />Weitere einladen
                </button>
                <button class="form-button button-contained" @click="cancel">Schließen</button>
            </div>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import { DialogController } from '~~/composables/dialog-controller'
    import { injectEventActions } from '~~/composables/event-actions'

    const props = defineProps<{
        controller: DialogController<'internalPublicationInviteHelp'>
        event: ApiModel<'Event'>
    }>()

    const { isRevealed, cancel } = props.controller
    const { canEdit } = inject(EventPermissionsKey)
    const action = injectEventActions()

    const { data: internalPublications, isLoading } = useEventQueries(props.event.id).internalPublication(canEdit.value)
</script>
