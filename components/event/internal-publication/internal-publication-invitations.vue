<template>
    <div
        v-if="oneOrganisationInvited"
        @[isClickable&&`click`]="action.internalPublicationShowInvitations()"
        class="flex items-center gap-2 pl-2"
        :class="{ 'hover:cursor-pointer hover:rounded-sm hover:bg-blue-50 hover:ring-4 hover:ring-blue-50': isClickable }">
        <UiIcon name="published" class="h-6 w-6 text-blue-500" />
        <span class="line-clamp-2 w-full text-sm group-[.flex]:line-clamp-5">{{ oneOrganisationInvitationDescription }}</span>
    </div>
    <div
        v-else-if="allOrganisationsInvited"
        @[isClickable&&`click`]="action.internalPublicationShowInvitations()"
        class="flex items-center gap-2 pl-2"
        :class="{ 'hover:cursor-pointer hover:rounded-sm hover:bg-blue-50 hover:ring-4 hover:ring-blue-50': isClickable }">
        <UiIcon name="published" class="h-6 w-6 text-blue-500" />
        <span class="line-clamp-2 w-full text-sm">alle aus {{ eventName }} eingeladen</span>
    </div>
    <div
        v-else-if="!arePublicationsAvailable"
        @click="action.internalPublicationInviteHelp()"
        class="flex items-center gap-2 pl-2"
        :class="{ 'hover:cursor-pointer hover:rounded-sm hover:bg-blue-50 hover:ring-4 hover:ring-blue-50': isClickable }">
        <UiIcon name="not-published" class="h-6 w-6 text-blue-500" />
        <span class="line-clamp-2 w-full text-sm"> klicke hier, um Helfende einzuladen </span>
    </div>
    <div
        v-else-if="twoOrganisationInvited"
        @[isClickable&&`click`]="action.internalPublicationShowInvitations()"
        :class="{ 'hover:cursor-pointer hover:rounded-sm hover:bg-blue-50 hover:ring-4 hover:ring-blue-50': isClickable }">
        <div v-for="publication in internalPublications" :key="publication.id" class="flex gap-2 pl-2">
            <UiIcon name="published" class="h-6 w-6 text-blue-500" />
            <span class="line-clamp-2 w-full text-sm">
                {{ getDescription(publication) }}
            </span>
        </div>
    </div>
    <div
        v-else
        class="flex items-center gap-2 pl-2"
        :class="{ 'hover:cursor-pointer hover:rounded-sm hover:bg-blue-50 hover:ring-4 hover:ring-blue-50': isClickable }"
        @[isClickable&&`click`]="action.internalPublicationShowInvitations()">
        <UiIcon name="published" class="h-6 w-6 text-blue-500" />
        <span class="line-clamp-2 w-full text-sm">{{ internalPublications?.length }} Ziele eingeladen</span>
    </div>
</template>

<script lang="ts" setup>
    import { injectEventActions } from '~~/composables/event-actions'

    const props = defineProps<{
        internalPublications: ApiModel<'InternalPublication'>[] | ApiModel<'InternalPublication'>
        event: ApiModel<'Event'>
        isClickable?: boolean
    }>()

    const internalPublications = computed<ApiModel<'InternalPublication'>[]>(() => {
        return Array.isArray(props.internalPublications) ? props.internalPublications : [props.internalPublications]
    })

    const eventName = ref<string>(props.event?.organisation.name)

    const action = injectEventActions()

    const {
        oneOrganisationInvitationDescription,
        oneOrganisationInvited,
        arePublicationsAvailable,
        allOrganisationsInvited,
        twoOrganisationInvited,
        getDescription
    } = useInternalPublications(internalPublications)
</script>
