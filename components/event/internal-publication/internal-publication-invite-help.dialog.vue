<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel">
        <template #title>
            <span v-if="isInvitationEdit">Einladung bearbeiten</span>
            <span v-else>Helfende einladen</span>
        </template>
        <div class="flex flex-col gap-8">
            <UiDisclosure v-model:open="open" class="mb-8">
                <template #button>
                    <span class="flex items-center gap-4 text-base"> <UiIcon name="information-circle" class="h-5 w-5" /> So geht das </span>
                    <UiIcon
                        name="chevron-down"
                        class="text-gray-900transform h-6 w-6 transition-all"
                        :class="{ 'rotate-180': open, 'rotate-0': !open }" />
                </template>
                <div class="flex flex-col gap-4 text-xs">
                    <span>
                        Lade Helferinnen und Helfer aus der Gliederung ein, für die das Ereignis angelegt ist. Die Gliederung siehst du links über dem
                        Datum. Wähle hier eine oder mehrere
                    </span>
                    <ul class="list-disc pl-4">
                        <li><strong>Gemeinschaften</strong></li>
                        <li><strong>Gremien oder Gruppen</strong></li>
                        <li>
                            <strong>Einsatzqualifikationen</strong> (Wähle hier alle Qualifikationen aus, die dir wichtig sind. Wähle nicht nur die
                            höchste Qualifikation, denn Menschen mit einer niedrigeren Qualifikation in der Personalakte lädst du dann nicht ein.)
                        </li>
                    </ul>
                    <span> Noch mehr Auswahlfelder findest du, wenn du runterscrollst und auf <strong>"Alle Optionen"</strong> klickst. </span>
                </div>
            </UiDisclosure>

            <p v-if="isInvitationEdit" class="-mt-12 text-xs">Eingeladen am: {{ $formatDateTime(new Date(internalPublication.recievingDate)) }}</p>

            <div v-if="!showExtraOptions" class="flex flex-col gap-8">
                <UiFormField label="Gemeinschaften">
                    <div class="mb-2 mt-1 flex">
                        <UiBadgeSelect
                            v-model="selectedCommunities"
                            :options="definedCommunities"
                            :label-handler="(item) => (item === 'ALL' ? 'Gesamt' : item)"
                            :selection-handler="selectCommunity" />
                    </div>
                </UiFormField>

                <UiFormField label="Gruppe/Gremium">
                    <UiUniversalSelect
                        v-model="selectedMembershipGroupDescriptions"
                        :options="membershipGroupDescriptions"
                        label-prop="value2"
                        category-prop="category"
                        :is-loading="isFetchingMembershipGroupDescriptions"
                        placeholder="Gruppe/Gremium auswählen"
                        alternativeSearchTipNote="Keine Vorschläge. Probiere “Alle Werte”"
                    >
                    </UiUniversalSelect>
                </UiFormField>

                <UiFormField label="Einsatzqualifikation">
                    <UiUniversalSelect
                        v-model="selectedOperationQualifications"
                        :options="operationQualifications"
                        label-prop="value2"
                        category-prop="category"
                        :is-loading="isFetchingOperationQualifications"
                        placeholder="Einsatzqualifikation auswählen" />
                </UiFormField>
            </div>

            <div v-else class="flex flex-col gap-8">
                <UiFormField label="Status im DRK">
                    <UiUniversalSelect
                        v-model="selectedStatusAtDrk"
                        :options="statusAtDrk"
                        label-prop="value2"
                        :is-loading="isFetchingStatusAtDrk"
                        placeholder="Status auswählen" />
                </UiFormField>

                <hr />

                <UiFormField label="Zugehörigkeit">
                    <UiUniversalSelect
                        v-model="selectedMembershipEntries"
                        :options="membershipEntries"
                        label-prop="value2"
                        :is-loading="isFetchingMembershipEntries"
                        placeholder="Zugehörigkeit auswählen" />
                </UiFormField>

                <UiFormField label="Art der Zugehörigkeit">
                    <UiUniversalSelect
                        v-model="selectedTypesOfMembership"
                        :options="typesOfMembership"
                        label-prop="value2"
                        :is-loading="isFetchingTypesOfMembership"
                        placeholder="Art der Zugehörigkeit auswählen" />
                </UiFormField>

                <hr />

                <UiFormField label="Art der Gruppe/des Gremiums">
                    <UiUniversalSelect
                        v-model="selectedMembershipGroups"
                        :options="membershipGroups"
                        label-prop="value2"
                        :is-loading="isFetchingMembershipGroups"
                        placeholder="Art der Gruppe/ des Gremiums auswählen" />
                </UiFormField>

                <UiFormField label="Gruppe/Gremium">
                    <UiUniversalSelect
                        v-model="selectedMembershipGroupDescriptions"
                        :options="membershipGroupDescriptions"
                        label-prop="value2"
                        category-prop="category"
                        :is-loading="isFetchingMembershipGroupDescriptions"
                        placeholder="Gruppe/Gremium auswählen"
                        alternativeSearchTipNote="Keine Vorschläge. Probiere “Alle Werte”"
                    >
                    </UiUniversalSelect>
                </UiFormField>

                <UiFormField label="Funktion">
                    <UiUniversalSelect
                        v-model="selectedFunctions"
                        :options="functions"
                        label-prop="value2"
                        :is-loading="isFetchingFunctions"
                        placeholder="Funktion auswählen" />
                </UiFormField>

                <hr />

                <UiFormField label="Einsatzqualifikation">
                    <UiUniversalSelect
                        v-model="selectedOperationQualifications"
                        :options="operationQualifications"
                        label-prop="value2"
                        category-prop="category"
                        :is-loading="isFetchingOperationQualifications"
                        placeholder="Einsatzqualifikation auswählen" />
                </UiFormField>

                <hr />

                <UiFormField label="Art der Einsatzformation">
                    <UiUniversalSelect
                        v-model="selectedOperations"
                        :options="operations"
                        label-prop="value2"
                        :is-loading="isFetchingOperations"
                        placeholder="Art der Einsatzformation auswählen" />
                </UiFormField>

                <UiFormField label="Bezeichnung">
                    <UiUniversalSelect
                        v-model="selectedOperationDescriptions"
                        :options="operationDescriptions"
                        label-prop="value2"
                        :is-loading="isFetchingOperationDescriptions"
                        placeholder="Bezeichnung auswählen" />
                </UiFormField>
            </div>

            <hr />

            <UiFormField label="Anmerkung" class="col-span-2 mb-4">
                <textarea type="text" class="form-textarea box-border w-full" rows="4" v-model="internalPublication.remark" />
            </UiFormField>

            <button v-if="!showExtraOptions" @click="showExtraOptions = !showExtraOptions" class="form-button button-outline mb-4 w-fit self-end">
                Alle Optionen
            </button>
        </div>

        <template #footer>
            <button class="form-button button-contained-secondary" @click="cancel">Abbrechen</button>
            <button class="form-button button-contained" @click="confirm(internalPublication)">
                {{ isInvitationEdit ? 'Speichern' : 'Helfende jetzt einladen' }}
            </button>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import ListType from '~/enums/listType'
    import { useCodeEntries, useInternalPublicationCodeEntries } from '~/queries/code-entries'
    import { DialogController } from '~~/composables/dialog-controller'
    import { communitiesMap, communitiesMapInverted } from '~/composables/select-community'
    import Community from '~/enums/community'
    import { CodeEntriesNames } from '~/enums/code-entries-with-category'

    const props = defineProps<{
        controller: DialogController<'internalPublicationInviteHelp'>
        publication?: ApiModel<'InternalPublication'>
        event?: ApiModel<'Event'>
    }>()

    const { isRevealed, cancel, confirm } = props.controller

    const open = ref<boolean>(false)
    const showExtraOptions = ref<boolean>(false)
    const internalPublication = ref<ApiModel<'InternalPublication'>>(useModelFactory('InternalPublication').create({}))
    const internalPublicationEvent = ref<ApiModel<'Event'>>(props.event)
    const isInvitationEdit = ref<boolean>(false)

    if (props.publication) {
        isInvitationEdit.value = true
        internalPublication.value = useModelFactory('InternalPublication').create({
            ...props.publication
        })
        showExtraOptions.value =
            internalPublication.value.statusAtDrk.length > 0 ||
            internalPublication.value.typesOfMembership.length > 0 ||
            internalPublication.value.functions.length > 0 ||
            internalPublication.value.membershipGroups.length > 0 ||
            internalPublication.value.operations.length > 0 ||
            internalPublication.value.operationDescriptions.length > 0 ||
            !internalPublication.value.membershipEntries.every((entry) => {
                return !!communitiesMapInverted[entry.value1]
            })
    }

    // Communities
    const { data: communityCodeEntries } = useCodeEntries(ListType.MembershipentryE)
    const { definedCommunities, selectedCommunities, selectCommunity } = useCommunities(internalPublication.value.membershipEntries)

    watch(selectedCommunities, () => {
        if (selectedCommunities.value.includes(Community.ALL)) {
            internalPublication.value.membershipEntries = []
        } else {
            internalPublication.value.membershipEntries = selectedCommunities.value.map((community) => {
                return communityCodeEntries.value.find((codeEntry) => codeEntry.value1 === communitiesMap[community])
            })
        }
    })

    // Membership groups
    const { data: membershipGroupDescriptionsData, isFetching: isFetchingMembershipGroupDescriptions } = useInternalPublicationCodeEntries('MEMBERSHIP_GROUP_DESCRIPTIONS', props.event)
    const {
        codeEntryWithCategory: membershipGroupDescriptions,
        selectedCodeEntry: selectedMembershipGroupDescriptions
    } = useCodeEntriesWithCategoryFacade(
        internalPublication,
        membershipGroupDescriptionsData,
        CodeEntriesNames.MembershipGroupDescriptions,
        internalPublicationEvent
    )

    // Operation qualifications
    const { operationQualifications: operationQualificationsList } = useListType()
    const { data: operationQualificationsData, isFetching: isFetchingOperationQualifications } = useCodeEntries(operationQualificationsList)
    const { codeEntryWithCategory: operationQualifications, selectedCodeEntry: selectedOperationQualifications } = useCodeEntriesWithCategoryFacade(
        internalPublication,
        operationQualificationsData,
        CodeEntriesNames.OperationQualifications
    )

    // Status at DRK
    const { statusAtDrk: statusAtDrkList } = useListType()
    const { data: statusAtDrkData, isFetching: isFetchingStatusAtDrk } = useCodeEntries(statusAtDrkList)
    const { codeEntryWithCategory: statusAtDrk, selectedCodeEntry: selectedStatusAtDrk } = useCodeEntriesWithCategoryFacade(
        internalPublication,
        statusAtDrkData,
        CodeEntriesNames.StatusAtDrk
    )

    // Types of membership
    const { typesOfMembership: typesOfMembershipList } = useListType()
    const { data: typesOfMembershipData, isFetching: isFetchingTypesOfMembership } = useCodeEntries(typesOfMembershipList)
    const { codeEntryWithCategory: typesOfMembership, selectedCodeEntry: selectedTypesOfMembership } = useCodeEntriesWithCategoryFacade(
        internalPublication,
        typesOfMembershipData,
        CodeEntriesNames.TypesOfMembership
    )

    // membership entries
    const { membershipEntries: membershipEntriesList } = useListType()
    const { data: membershipEntriesData, isFetching: isFetchingMembershipEntries } = useCodeEntries(membershipEntriesList)
    const { codeEntryWithCategory: membershipEntries, selectedCodeEntry: selectedMembershipEntries } = useCodeEntriesWithCategoryFacade(
        internalPublication,
        membershipEntriesData,
        CodeEntriesNames.MembershipEntries
    )

    // membership groups
    const { membershipGroups: membershipGroupsList } = useListType()
    const { data: membershipGroupsData, isFetching: isFetchingMembershipGroups } = useCodeEntries(membershipGroupsList)
    const { codeEntryWithCategory: membershipGroups, selectedCodeEntry: selectedMembershipGroups } = useCodeEntriesWithCategoryFacade(
        internalPublication,
        membershipGroupsData,
        CodeEntriesNames.MembershipGroups
    )

    // functions
    const { functions: functionsList } = useListType()
    const { data: functionsData, isFetching: isFetchingFunctions } = useCodeEntries(functionsList)
    const { codeEntryWithCategory: functions, selectedCodeEntry: selectedFunctions } = useCodeEntriesWithCategoryFacade(
        internalPublication,
        functionsData,
        CodeEntriesNames.Functions
    )

    // Operations
    const { operations: operationsList } = useListType()
    const { data: operationsData, isFetching: isFetchingOperations } = useCodeEntries(operationsList)
    const { codeEntryWithCategory: operations, selectedCodeEntry: selectedOperations } = useCodeEntriesWithCategoryFacade(
        internalPublication,
        operationsData,
        CodeEntriesNames.Operations
    )

    // Operations descriptions
    const { operationDescriptions: operationDescriptionsList } = useListType()
    const { data: operationDescriptionsData, isFetching: isFetchingOperationDescriptions } = useCodeEntries(operationDescriptionsList)
    const { codeEntryWithCategory: operationDescriptions, selectedCodeEntry: selectedOperationDescriptions } = useCodeEntriesWithCategoryFacade(
        internalPublication,
        operationDescriptionsData,
        CodeEntriesNames.OperationDescriptions
    )
</script>
