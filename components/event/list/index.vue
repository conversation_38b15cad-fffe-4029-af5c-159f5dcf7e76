<script lang="ts" setup>
  const props = defineProps<{
    events: ApiModel<'Event'>[]
  }>()

  // Generate a list of events with Event ID as Key and false as value
  const generateSelectedEvents = () => {
    const se = {}
    props.events.forEach((event) => {
      se[event.id] = false
    })
    return ref(se)
  }

  const selection = ref(generateSelectedEvents())

  // watch if events changes (filters, etc.) and update the selectedEvents
  watch(
    () => [...props.events],
    () => {
      selection.value = generateSelectedEvents().value
    }
  )

  // computed keys of selectedEvents where value is true
  const selectedEventIds = computed(() => {
    return Object.keys(selection.value).filter((id) => selection.value[id])
  })

  const selectedEvents = computed(() => {
    return props.events.filter(({ id }) => !!selection.value[id])
  })

  const toggleAllItems = function () {
    const selectValue = selectedEventIds.value.length === 0
    Object.entries(selection.value).forEach(([id]) => {
      selection.value[id] = selectValue
    })
  }

  // If selectedEventsIDs largert than 0, show the action selector
  const areEventsSelected = computed(() => selectedEventIds.value.length > 0)
</script>

<template>
  <div>
    <div class="_event-list-grid text-xs items-center">
      <div class="flex gap-x-4 items-center pl-4 lg:pl-0">
        <!-- <th scope="col" class="w-12 px-2"> -->
        <label class="cursor-pointer">
          <input
            @click="toggleAllItems"
            type="checkbox"
            :class="{
                'icon-check-white': selectedEventIds.length === props.events.length || selectedEventIds.length === 0,
                'icon-minus-white': selectedEventIds.length < props.events.length && selectedEventIds.length > 0
            }"
            class="outline-grey-400 sm:hover:outline-softred-300 sm:hover:bg-softred-300 checked:outline-softred-500 checked:bg-softred-500 h-5 w-5 appearance-none rounded-full outline outline-1" />
        </label>
        <span class="hidden lg:block">Beginn & Dauer</span>
        <span class="lg:hidden font-semibold ">Alle auswählen</span>
        <!-- <span class="ml-auto">Typ</span> -->
      </div>

      <div class="hidden lg:block">
        Bezeichnung & Nähere Bezeichnung
      </div>

      <div class="hidden lg:block">
        Mein Status
      </div>

      <div class="hidden lg:block">
        Zuordnung
      </div>

      <div class="hidden lg:block">
        Ist / Soll
      </div>

      <div class="hidden lg:block">
        Ereignis-Status
      </div>

      <div class="hidden lg:block">
        <span class="sr-only">Aktionen</span>
      </div>
    </div>

    <Transition>
      <div class="w-full pl-6 pr-4 py-3">
        <EventActionSelector v-if="areEventsSelected" :selected-events="selectedEvents" />
      </div>
    </Transition>

    <EventListItem v-for="event in events" :event="event" v-model:selected="selection[event.id]" :key="event.id" />
  </div>
</template>

<!--suppress CssUnusedSymbol -->
<style lang="pcss" scoped>
  .v-enter-active,
  .v-leave-active {
    transition: opacity 0.3s ease;
  }

  .v-enter-from,
  .v-leave-to {
    opacity: 0;
  }
</style>
<style lang="pcss">

  @media screen and (min-width: 1024px) {
    ._event-list-grid {
      @apply grid grid-cols-24 gap-x-4 pl-2 sm:pl-6 pr-4 py-3;
    }
  }

  ._event-list-grid > div:nth-child(1) { @apply col-span-4; }
  ._event-list-grid > div:nth-child(2) { @apply col-span-9; }
  ._event-list-grid > div:nth-child(3) { @apply col-span-3; }
  ._event-list-grid > div:nth-child(4) { @apply col-span-2; }
  ._event-list-grid > div:nth-child(5) { @apply col-span-3; }
  ._event-list-grid > div:nth-child(6) { @apply col-span-3; }

</style>
