
<script lang="ts" setup>
    import { useVModel } from '@vueuse/core'
    import { useWindowSize } from '@vueuse/core'

    const props = defineProps<{
        selected: boolean
        event: ApiModel<'Event'>
    }>()

    const emit = defineEmits<{ (event: 'update:selected', value: boolean): any }>()

    const isSelected = useVModel(props, 'selected', emit)

    /**
     * Provide event queries to all list elements
     *
     * For example in order to retrieve publication status
     */
    provideEventQueries(props.event.id)

    /**
     * It's not enough to pass the simple prop to user permissions
     */
    const reactivateEvent = toRef(props, 'event')

    /**
     * Provide permissions to every child
     */
    provide(EventPermissionsKey, useEventPermissions(reactivateEvent))

    const { assignment } = useMyEventSignup(props.event)

    const signUps = computed(() => useEventStatistics(props.event))
    const status = computed(() => useEventStatus(props.event.status))

    const { width } = useWindowSize()

    const isClickable = computed<boolean>(() => {
        return width.value >= 1024
    })

    const { dialog: memberDataDialog, reveal: openMemberDataDialog } = useMemberDataDialog()

    provide('viewMemberData', {
        openMemberDataDialog
    })

</script>

<template>
    <ProvideEventActions :event="event">
        <ProvideEvent :event="event" v-slot="{ title, icon, description }">
            <div
                class="_event-list-grid border-r-12 relative mb-4 rounded-sm border px-1 py-4 text-xs shadow lg:mb-0 lg:mt-2 lg:cursor-pointer lg:items-center lg:border-b-0 lg:border-l-0 lg:border-t-0 lg:px-0"
                @click="isClickable ? $router.push({ name: 'events-id-details', params: { id: event.id } }) : null"
                @keydown.enter="isClickable ? $router.push({ name: 'events-id-details', params: { id: event.id } }) : null  "
                tabindex="0"
                :class="{
                    'bg-gold-50': isSelected,
                    'border-gray-200': status.isCanceled || status.isFinished,
                    'border-gold-300': status.isWaitingForApproval,
                    'border-green-400': status.isApproved
                }">
                <div
                    class="mb-2 flex flex-col items-start border-b border-gray-100 pl-4 pr-4 lg:mb-0 lg:flex-row lg:items-center lg:gap-x-4 lg:border-b-0 lg:pl-0 lg:pr-0 lg:pt-0">
                    <div class="mb-2 flex items-center gap-2 lg:hidden">
                        <input
                            v-model="isSelected"
                            @click.stop
                            type="checkbox"
                            class="icon-check-white outline-grey-400 checked:outline-softred-500 checked:bg-softred-500 h-5 w-5 appearance-none rounded-full outline outline-1" />
                        <span class="font-semibold">Beginn & Dauer:</span>
                    </div>
                    <div class="mb-4 lg:hidden">
                        <span :class="{ 'line-through': status.isCanceled }">
                            <span class="text-gray-900">
                                {{ $formatDateWithAbbreviatedDay(event.dateFrom) }}
                            </span>
                            {{ $formatTime(event.dateFrom, 'short') }} <br />
                            Dauer: {{ $formatDuration(event.dateFrom, event.dateUpTo, 'lax') }}
                        </span>
                    </div>
                    <input
                        v-model="isSelected"
                        @click.stop
                        type="checkbox"
                        class="icon-check-white outline-grey-400 hover:outline-softred-300 hover:bg-softred-300 checked:outline-softred-500 checked:bg-softred-500 hidden h-5 w-5 appearance-none rounded-full outline outline-1 lg:block" />

                    <span class="hidden lg:block" :class="{ 'line-through': status.isCanceled }">
                        <span class="text-gray-900">
                            {{ $formatDateWithAbbreviatedDay(event.dateFrom) }}
                        </span>
                        {{ $formatTime(event.dateFrom, 'short') }} <br />
                        Dauer: {{ $formatDuration(event.dateFrom, event.dateUpTo, 'lax') }}
                    </span>
                </div>

                <div class="mb-2 flex flex-col items-start gap-1 border-b border-gray-100 pb-2 pl-4 pr-4 lg:mb-0 lg:border-b-0 lg:pb-0 lg:pr-0">
                    <div class="lg:hidden">
                        <span class="font-semibold lg:hidden">Bezeichnung & Nähere Bezeichnung:</span>
                    </div>
                    <div class="flex flex-col gap-y-1">
                        <span class="text-grey-900 bg-grey-100 flex w-fit items-center gap-x-2 rounded-xl px-2 text-xs sm:gap-x-4 sm:py-1">
                            <UiIcon :name="icon" class="h-auto w-6 flex-none" />
                            {{ title }}
                        </span>
                        <span class="text-sm empty:hidden" :class="{ 'line-through': status.isCanceled }" v-text="event.organisation?.name" />
                        <span class="line-clamp-5 break-all text-sm leading-5" :class="{ 'line-through': status.isCanceled }">{{ description }}</span>
                    </div>
                </div>

                <div
                    class="mb-2 flex items-center justify-between gap-1 border-b border-gray-100 px-4 pb-2 lg:mb-0 lg:block lg:gap-9 lg:border-b-0 lg:px-2 lg:py-4 lg:pb-0">
                    <div class="lg:hidden">
                        <span class="font-semibold">Mein Status:</span>
                    </div>
                    <div>
                        <EventMySigningUpMenu :event="event" v-slot="{ status }" class="aria-disabled:opacity-60">
                            <EventSigningUpStatus :status="status" data-size="xl" class="text-xs text-yellow-900" />
                        </EventMySigningUpMenu>
                    </div>
                </div>

                <div class="mb-2 flex items-center justify-between gap-1 border-b border-gray-100 px-4 pb-2 lg:mb-0 lg:border-b-0 lg:pb-0">
                    <div class="lg:hidden">
                        <span class="font-semibold">Zuordnung:</span>
                    </div>
                    <div>
                        <EventAssignmentStatus :value="assignment" data-size="xl" data-no-label />
                    </div>
                </div>

                <div class="mb-2 lg:mb-0 flex items-center justify-between gap-1 border-b border-gray-100 lg:border-none px-4 pb-2 lg:pb-0">
                    <div class="lg:hidden">
                        <span class="font-semibold">Ist / Soll:</span>
                    </div>
                    <EventListItemStatus
                        class="mb-2 flex cursor-pointer items-center justify-between rounded-md px-4 pb-2 transition duration-150 ease-in-out lg:w-full lg:mb-0 lg:block lg:pl-1 lg:pr-1 lg:p-1 lg:transition-all lg:hover:bg-blue-100"
                        :signUps="signUps"
                        :status="status"
                        :event="event"

                    />
                </div>

                <div class="mb-2 flex items-center justify-between gap-1 px-4 lg:mb-0 lg:justify-end">
                    <div class="lg:hidden">
                        <span class="font-semibold">Ereignis-Status:</span>
                    </div>
                    <div class="flex items-center justify-end" @click.stop="">
                        <EventShareStatus :event="event" class="cursor-default" />
                        <EventStatus :status="event.status" />
                        <EventActionsMenu :event="event" />
                    </div>
                </div>

                <div class="mb-2 mt-8 flex items-center justify-end gap-1 px-4 lg:mb-0 lg:hidden">
                    <div class="flex items-center justify-end">
                        <NuxtLink :to="{ name: 'events-id-details', params: { id: event.id } }" class="form-button button-sm inline-flex">
                            mehr Details
                            <IconChevronDoubleRight class="h-5 flex-none align-bottom" />
                        </NuxtLink>
                    </div>
                </div>

                <div v-if="status.isCanceled" class="pointer-events-none absolute left-0 top-0 mb-2 h-full w-full bg-white opacity-60 lg:mb-0"></div>
            </div>
        </ProvideEvent>
    </ProvideEventActions>
    <EventPlanningMemberMasterdataDialog :controller="memberDataDialog.viewMemberData" :event="event" />
</template>
