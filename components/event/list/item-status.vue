<template>
    <div
        @click.stop="handleClick">
        <div>
            <span
                class="inline-block w-20 whitespace-nowrap rounded-sm p-1 text-center text-xs lg:w-full"
                :class="{
                    'bg-gray-200': status.isCanceled || status.isFinished,
                    'bg-red-300': signUps.status === 'bad' && !(status.isCanceled || status.isFinished),
                    'bg-red-100': signUps.status === 'sufficient' && !(status.isCanceled || status.isFinished),
                    'bg-green-100': signUps.status === 'ok' && !(status.isCanceled || status.isFinished),
                    'bg-green-300': signUps.status === 'good' && !(status.isCanceled || status.isFinished)
                }">
                {{ signUps.actual }} / {{ signUps.desired }}
            </span>
        </div>
    </div>
</template>

<script lang="ts" setup>
    import type { SigningUpStatusType, EventStatusValue} from '~/composables/event';
    import { injectEventActions } from '~/composables/event-actions'

    type Props = {
        status: EventStatusValue,
        signUps: SigningUpStatusType,
        event: ApiModel<'Event'>,
        openComposer?: boolean,
        onClick?: () => Promise<void>
    }

    const props = withDefaults(defineProps<Props>(),
    {
        openComposer: true,
    }
    )

    const { viewResources } = injectEventActions()

    const { canManageResources, canManageData } = inject(EventPermissionsKey)

    function handleClick() {
        if (props.openComposer || !canManageData.value) {
            viewResources()
            return
        }

        if (canManageResources.value) {
            props.onClick()
        } else {
            viewResources()
        }
    }

</script>
