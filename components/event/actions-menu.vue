<template>
    <UiMenu class="cursor-pointer">
        <UiMenuItem v-if="canManageData" @click="action.editEvent" icon="pencil">Stammdaten bearbeiten</UiMenuItem>
        <UiMenuItem
            v-if="canManageData && isEventManager"
            @click="action.copyEvent"
            icon="duplicate"
            :disabled="isAnyCopyOperationRunning"
            class="group"
            :class="{ 'opacity-80 cursor-not-allowed': isAnyCopyOperationRunning }"
        >
            <span>
                Ereignis multiplizieren
                <span v-if="isAnyCopyOperationRunning" class="text-xs block">
                    (Ereignis multiplizieren läuft...)
                </span>
            </span>
        </UiMenuItem>

        <UiMenuItem v-if="canPublish && !arePublicationsAvailable && !isLoading" @click="action.internalPublicationInviteHelp()" icon="users"
            >Eingeladen</UiMenuItem
        >
        <UiMenuItem v-if="canPublish && arePublicationsAvailable && !isLoading" @click="action.internalPublicationShowInvitations" icon="users"
            >Eingeladen</UiMenuItem
        >
        <UiMenuItem v-if="canClose" @click="action.closeEvent" icon="lock-closed">Abschließen</UiMenuItem>
        <UiMenuItem v-if="!canClose && canManageData" @click="action.canNotCloseEvent" icon="lock-closed" class="opacity-50">Abschließen</UiMenuItem>
        <UiMenuItem v-if="canReopen" @click="action.openEvent" icon="lock-open"> Erneut öffnen</UiMenuItem>
        <UiMenuItem v-if="canCancel" @click="action.cancelEvent" icon="exclamation"> Absagen</UiMenuItem>
        <UiMenuItem v-if="canReactivate" @click="action.reactivateEvent" icon="fast-forward">Wieder aktivieren</UiMenuItem>
        <UiMenuItem v-if="canDelete" @click="action.removeEvent" icon="trash"> Löschen </UiMenuItem>
        <UiMenuItem v-if="canCreateReport && canPrint" @click="action.downloadReports" icon="document-report"> Listen & Übersichten </UiMenuItem>
        <UiMenuItem v-if="canExportToCalendar" @click="action.downloadCalendarEntry" icon="calendar-outlined"> In Kalender exportieren </UiMenuItem>
        <UiMenuItem as="a" :href="useClassicEventUrl(event)" target="_blank" icon="external-link">Zum EM Klassik </UiMenuItem>
    </UiMenu>
</template>

<script setup lang="ts">
    import { injectEventActions } from '~~/composables/event-actions'
    import { useCopyOperations } from '~~/composables/copy-operations'

    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const { $user } = useNuxtApp()

    const eventOrganisationId = props.event.organisation.id
    const permissions = $user.permissions

    const isEventManager = computed<boolean>(()=>{
        const permission = permissions.value.find(permission => permission.dependendOrganisation.id === eventOrganisationId)
        if(permission){
            return permission.isEventManager
        }
        return false
    })


    const { canPublish, canClose, canCancel, canReactivate, canReopen, canEdit, canDelete, canPrint, canExportToCalendar, canManageData, canCreateReport } =
        inject(EventPermissionsKey)

    const action = injectEventActions()
    const { isAnyCopyOperationRunning } = useCopyOperations()

    const { data: internalPublications, isLoading } = useEventQueries(props.event.id).internalPublication(canEdit.value)
    const { arePublicationsAvailable } = useInternalPublications(internalPublications)
</script>
