<template>
    <div>
        <h3 class="mb-4 text-base">Verantwortliche</h3>
        <div v-if="!isRevealed">
            <template v-if="isEmpty">
                <button v-if="canManageData" @click="reveal" class="create-entry-button">Hinzufügen</button>
                <span v-else>Nichts eingetragen</span>
            </template>

            <div v-else :data-not-editable="!canManageData" class="group/panel-entry panel-entry">
                <div class="space-y-3">
                    <ProvideMember v-for="responsible in responsibles" :member="responsible" :key="responsible._uuid">
                        <template #default="{ name, avatar, organisation }">
                            <div class="group flex items-center text-sm">
                                <div class="flex items-center text-sm cursor-pointer pointer-events-auto hover:bg-blue-50 hover:ring-blue-50 hover:ring-4" @click="responsible.id ? openMemberDataDialog(responsible) : null">
                                    <UiAvatar :name="name" :image="avatar" size="sm" class="mr-[0.5em]" />
                                    <span class="leading-none">
                                         <span class="flex flex-row items-center gap-1">
                                            {{ name }} <UiIcon name="user" class="w-4 h-4 opacity-0 transition-opacity group-hover:opacity-100" /> <br />
                                        </span>
                                        <span v-text="organisation" class="text-xs text-gray-500" />
                                    </span>
                                </div>
                            </div>
                        </template>
                    </ProvideMember>
                </div>
                <button v-if="isEditable" @click="reveal" class="edit-entries-button" title="Bearbeiten" />
            </div>
        </div>
        <div v-else class="panel-form">
            <MemberPicker
                @select="(member) => commit('createResponsible', member)"
                :allowed-scopes="['MY_MEMBERS','GLOBAL_MEMBERS']"
                :exclude-members="responsibles"
                placeholder="Personen hinzufügen"
                popper-position="top-start"
                class="mb-4" />

            <TransitionGroup tag="div" name="list" class="space-y-2">
                <div v-for="responsible in responsibles" :key="responsible._uuid" class="relative rounded panel-entry group/panel-entry">
                    <ProvideMember :member="responsible" :key="responsible._uuid">
                        <template #default="{ name, avatar, organisation }">
                            <div class="flex items-center text-sm">
                                <UiAvatar :name="name" :image="avatar" size="sm" class="mr-[0.5em]" />
                                <span class="leading-none">
                                    {{ name }}<br />
                                    <span v-text="organisation" class="text-xs text-gray-500" />
                                </span>
                            </div>
                        </template>
                    </ProvideMember>
                    <button v-if="canRemove" @click.prevent="commit('removeResponsible', responsible)" class="remove-entry-button" title="Entfernen" />
                </div>
            </TransitionGroup>

            <div class="flex justify-end gap-2 mt-4">
                <button @click="cancel" data-type="confirm">Fertig</button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { useConfirmDialog } from '@vueuse/core'
import { ViewMemberData } from 'composables/members';

    const { canManageData } = inject(EventPermissionsKey)

    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const commit = useCommitEventMutation(props.event)

    const { data: responsibles } = useEventQuery('responsibles')

    const { reveal, cancel, isRevealed } = useConfirmDialog()

    const isEmpty = computed(() => {
        if (!responsibles) {
            return true
        }

        return !responsibles.value.length
    })

    const isEditable = computed(() => {
        if (!canManageData.value) {
            return false
        }

        return true
    })

    const canRemove = computed(() => {
        if (!canManageData.value) {
            return false
        }

        if ((responsibles.value?.length ?? 0) < 2) {
            return false
        }

        return true
    })

    const  { openMemberDataDialog }  = inject<ViewMemberData>('viewMemberData')

</script>

<style lang="pcss" scoped>

    .panel-entry {
        @apply relative -m-2 p-2 hover:bg-blue-50/20 data-[not-editable=true]:pointer-events-none
    }

    .edit-entries-button {
        @apply absolute -right-1 -top-1 xl:opacity-0 focus:opacity-100 group-hover/panel-entry:opacity-100 group-hover/panel-entry:inline;
        @apply form-button button-xs absolute -right-1 -top-1  cursor-pointer rounded-full p-1 group-hover/panel-entry:inline;
        @apply icon-pencil-alt-blue-500 w-7 h-7 bg-center bg-[length:1.25rem_1.25rem] bg-no-repeat
    }

    .remove-entry-button {
        @apply absolute -right-1 -top-1 opacity-0 group-hover/panel-entry:opacity-100 focus:opacity-100;
        /* @apply opacity-50 hover:opacity-100; */
        @apply form-button button-xs hover:!bg-red-50 hover:!border-red-100 cursor-pointer rounded-full p-1 ;
        @apply w-7 h-7 bg-center bg-[length:1.25rem_1.25rem] bg-no-repeat;
        @apply icon-trash-red-500
    }

    .create-entry-button {
        @apply form-button button-sm pl-8;
        @apply icon-plus-circle-sky-500 bg-[left_0.5em_center] bg-[length:1.25rem_1.25rem] bg-no-repeat
    }

    .panel-form {
        button {
            @apply form-button button-sm;

            &[data-type=cancel] {
                @apply button-contained-secondary
            }

            &[data-type=confirm] {
                @apply button-contained
            }
        }
    }


    .list-move,  .list-enter-active, .list-leave-active {
        transition: all 0.3s ease;
    }

    .list-enter-from, .list-leave-to {
        opacity: 0;
        transform: translateX(16px);
    }

    /* ensure leaving items are taken out of layout flow so that moving
        animations can be calculated correctly. */
    .list-leave-active {
        position: absolute;
        display: none;
    }
</style>
