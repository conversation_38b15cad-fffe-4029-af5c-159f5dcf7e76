<template>
    <h3 class="mb-4 text-base">Weitere Informationen</h3>
    <div v-if="!isRevealed">
        <template v-if="isEmpty">
            <button v-if="canManageData" @click="reveal" class="create-entry-button">Hinzufügen</button>
            <span v-else class="text-sm">Nicht<PERSON> eingetragen</span>
        </template>

        <div v-else :data-not-editable="!canManageData" class="group/panel-entry panel-entry">
            <span class="block whitespace-pre-line text-sm" v-html="formattedRemark" />
            <button v-if="isEditable" @click="reveal" class="edit-entry-button" title="Bearbeiten" />
        </div>
    </div>
    <div v-else class="panel-form">
        <textarea rows="3" v-model="eventModel.publicEventRemark" placeholder="Was ist für Helfende bei diesem Ereignis noch gut zu wissen?" />

        <div class="mt-1 flex justify-end gap-2">
            <button @click="cancel" class="form-button px-2" data-type="cancel">Abbrechen</button>
            <button @click="confirm" class="form-button button-contained button-sm" data-type="confirm">Speichern</button>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { useConfirmDialog, syncRef } from '@vueuse/core'
    import {computed} from 'vue'

    const { canManageData } = inject(EventPermissionsKey)

    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const commit = useCommitEventMutation(props.event)

    const eventModel = ref<ApiModel<'Event'>>(null)

    syncRef(toRef(props, 'event'), eventModel, {
        direction: 'ltr',
        transform: {
            ltr: (event) => useModelFactory('Event').fromJson(JSON.parse(JSON.stringify(event)) as object)
        }
    })

    const { reveal, confirm, cancel, isRevealed, onConfirm } = useConfirmDialog()
    const { formatLinksToHtmlAnchor, formatToHtmlEntities, formatNewLineToHtmlBreaks } = useFormatter()

    const formattedRemark = computed(() => {
        const rawText = eventModel.value.publicEventRemark || ''
        return formatNewLineToHtmlBreaks(
            formatLinksToHtmlAnchor(
                formatToHtmlEntities(rawText)
            )
        )
    })

    onConfirm(() => {
       commit('updateBasicData', eventModel.value)
    })

    const isEmpty = computed(() => {
        if (!props.event?.publicEventRemark) {
            return true
        }

        return false
    })

    const isEditable = computed(() => {
        if (!canManageData.value) {
            return false
        }

        return true
    })
</script>

<style lang="pcss" scoped>

    .panel-entry {
        @apply relative -m-2 py-2 pl-2 pr-5 hover:bg-blue-50/20 data-[not-editable=true]:pointer-events-none
    }

    .edit-entry-button {
        @apply absolute -right-1 -top-1 md:hidden group-hover/panel-entry:inline;
        @apply form-button button-xs absolute -right-1 -top-1 md:hidden cursor-pointer rounded-full p-1 group-hover/panel-entry:inline;
        @apply icon-pencil-alt-blue-500 w-7 h-7 bg-center bg-[length:1.25rem_1.25rem] bg-no-repeat
    }

    .create-entry-button {
        @apply form-button button-sm pl-8;
        @apply icon-plus-circle-sky-500 bg-[left_0.5em_center] bg-[length:1.25rem_1.25rem] bg-no-repeat

    }

    .panel-form {
        textarea {
            @apply form-textarea box-border w-full text-sm leading-tight placeholder:text-sm
        }

        button {
            @apply form-button button-sm;

            &[data-type=cancel] {
                @apply button-contained-secondary
            }

            &[data-type=confirm] {
                @apply button-contained
            }
        }
    }
</style>
