<template>
    <div>
        <div v-if="!isRevealed" :data-not-editable="!canManageData" class="group/panel-entry panel-entry">
            <template v-if="!!eventAddress.addressContact">
                <AddressContact :contact="eventAddress.addressContact" class="text-sm" />
            </template>

            <template v-else-if="!!eventAddress.addressValue">
                <AddressLocation :location="eventAddress.addressValue" class="text-sm" />
            </template>

            <template v-else>
                <AddressCustom :address="eventAddress.freeAddressText" class="text-sm" />
            </template>

            <div class="entry-actions flex flex-col">
                <a v-if="hasLegacyLink" :href="classicUrl" target="_blank" title="Im EM-Klassik öffnen" class="legacy-entry-button" />
                <button v-if="isEditable" @click="reveal" class="edit-entry-button" title="Adresse bearbeiten" />
                <button v-if="isRemovable" @click="emit('remove:eventAddress', eventAddress)" class="remove-entry-button" title="Adresse entfernen" />
            </div>
        </div>

        <div v-else class="panel-form my-2">
            <textarea rows="3" v-model="addressModel.freeAddressText" placeholder="Trage hier einen Ereignisort ein." />

            <div class="mt-1 flex justify-end gap-2">
                <button @click="cancel" data-type="cancel">Abbrechen</button>
                <button @click="confirm" data-type="confirm" :disabled="!addressModel.freeAddressText">Speichern</button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { useConfirmDialog, useVModel } from '@vueuse/core'
    import { pick } from 'lodash-es'

    const { canManageData } = inject(EventPermissionsKey)

    const props = defineProps<{
        eventAddress: ApiModel<'EventAddress'>
        classicUrl?: string
    }>()

    const emit = defineEmits<{
        (event: 'update:eventAddress', value: ApiModel<'EventAddress'>): any
        (event: 'remove:eventAddress', value: ApiModel<'EventAddress'>): any
    }>()

    /**
     * Use the VModel to create and maintain a clone of the event property.
     * It does not emit an "update" event.
     */
    const addressModel = useVModel(props, 'eventAddress', emit, {
        clone: true,
        passive: true, // We want to watch deep changes
        shouldEmit: () => false // We don't want to emit on every change but only during confirmation
    })

    const { reveal, confirm, cancel, isRevealed, onConfirm } = useConfirmDialog()

    onConfirm(() => {
        emit('update:eventAddress', addressModel.value)
    })

    /**
     * Address is only editable when the address is of type "custom".
     * Which is the case, when everything is empty
     */
    const isEditable = computed(() => {
        if (!canManageData.value) {
            return false
        }

        const relevantValues = Object.values(
            // Extract relevant fields (that's all but custom)
            pick(props.eventAddress, ['addressContact', 'addressValue'])
        )

        return relevantValues.every((value) => !value)
    })

    const isRemovable = computed(() => {
        if (!canManageData.value) {
            return false
        }

        return true
    })

    const hasLegacyLink = computed(() => {
        if (!canManageData.value) {
            return false
        }

        if (!props.classicUrl) {
            return false
        }

        const relevantValues = Object.values(
            // Extract relevant fields (that's all but custom)
            pick(props.eventAddress, ['addressContact', 'addressValue'])
        )

        return !!relevantValues.some((value) => !!value)
    })
</script>

<style lang="pcss" scoped>

    .panel-entry {
        @apply relative -m-2 py-2 pl-2 pr-5 hover:bg-blue-50/20 data-[not-editable=true]:pointer-events-none
    }

    .entry-actions {
        @apply absolute -right-1 -top-1 md:invisible group-hover/panel-entry:visible;
    }

    .legacy-entry-button {
        /* @apply opacity-50 hover:opacity-100; */
        @apply w-6 h-6 bg-center bg-[length:1rem_1rem] bg-no-repeat;
        /* @apply hover:bg-yellow-50 hover:border-yellow-100; */
        @apply form-button button-xs cursor-pointer rounded-full p-1;
        @apply w-7 h-7 bg-center bg-[length:1.25rem_1.25rem] bg-no-repeat;
        @apply icon-external-link-blue-500;
    }

    .edit-entry-button {
        /* @apply opacity-50 hover:opacity-100; */
        @apply form-button button-xs cursor-pointer rounded-full p-1 group-hover/panel-entry:inline;
        @apply w-7 h-7 bg-center bg-[length:1.25rem_1.25rem] bg-no-repeat;
        @apply icon-pencil-alt-blue-500;
    }

    .remove-entry-button {
        /* @apply opacity-50 hover:opacity-100; */
        @apply form-button button-xs hover:bg-red-50 hover:!border-red-100 cursor-pointer rounded-full p-1;
        @apply w-7 h-7 bg-center bg-[length:1.25rem_1.25rem] bg-no-repeat;
        @apply icon-trash-red-500;
    }


    .panel-form {
        textarea {
            @apply form-textarea box-border w-full text-sm leading-tight placeholder:text-sm
        }

        button {
            @apply form-button button-sm;

            &[data-type=cancel] {
                @apply button-contained-secondary
            }

            &[data-type=confirm] {
                @apply button-contained
            }
        }
    }
</style>
