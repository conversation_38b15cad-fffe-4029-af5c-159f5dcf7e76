<template>
    <h3 class="mb-4 text-base">Ereignisorte</h3>
    <div v-if="!isRevealed">
        <template v-if="isEmpty">
            <button v-if="canManageData" @click="reveal" class="create-entry-button">Hinzufügen</button>
            <span v-else class="text-sm">Nichts eingetragen</span>
        </template>

        <TransitionGroup tag="div" name="list" v-else class="space-y-1">
            <template v-for="location in visibleLocations" :key="location._uuid">
                <EventSidebarLocationsPanelItem
                    :event-address="location"
                    :classic-url="useClassicEventUrl(event)"
                    @update:event-address="updateLocation"
                    @remove:event-address="removeLocation" />
            </template>
        </TransitionGroup>

        <div v-if="locations.length > 1" class="mt-3">
            <button @click.prevent="() => toggleVisibility()" class="form-button button-xs ml-auto">
                {{ allVisible ? 'weniger anzeigen' : 'mehr anzeigen' }}
            </button>
        </div>
    </div>
    <div v-else class="panel-form">
        <textarea rows="3" v-model="freeTextAddress" placeholder="Trage hier einen Ereignisort ein." />

        <div class="mt-2 flex justify-end gap-2">
            <button @click="cancel" data-type="cancel">Abbrechen</button>
            <button @click="confirm" data-type="confirm" :disabled="!freeTextAddress">Speichern</button>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { useConfirmDialog, useToggle, useVModel } from '@vueuse/core'

    const { canManageData } = inject(EventPermissionsKey)

    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const { data: locations } = useEventQuery('locations')
    const commit = useCommitEventMutation(props.event)

    const { reveal, confirm, cancel, isRevealed, onConfirm } = useConfirmDialog()

    onConfirm(createLocation)

    const [allVisible, toggleVisibility] = useToggle()

    const isEmpty = computed(() => {
        return !locations.value?.length
    })

    const visibleLocations = computed(() => {
        return allVisible.value ? locations.value : locations.value.slice(0, 1)
    })

    const freeTextAddress = ref<string>(null)

    function createLocation() {
        if (!freeTextAddress.value) {
            return
        }

        const address = useModelFactory('EventAddress').create({
            freeAddressText: freeTextAddress.value
        })

        commit('createLocation', address)
        freeTextAddress.value = null
    }

    function updateLocation(address: ApiModel<'EventAddress'>) {
        commit('updateLocation', address)
    }

    function removeLocation(address: ApiModel<'EventAddress'>) {
        commit('removeLocation', address)
    }
</script>

<style lang="pcss" scoped>

    .panel-entry {
        @apply relative -m-2 p-2 hover:bg-blue-50/20 data-[not-editable=true]:pointer-events-none
    }

    .legacy-entry-button {
        @apply absolute -right-1 -top-1 hidden group-hover/panel-entry:inline
    }

    .edit-entry-button {
        @apply absolute -right-1 -top-1 md:hidden group-hover/panel-entry:inline;
        @apply form-button button-xs absolute -right-1 -top-1 md:hidden cursor-pointer rounded-full p-1 group-hover/panel-entry:inline;
        @apply icon-pencil-alt-blue-500 w-7 h-7 bg-center bg-[length:1.25rem_1.25rem] bg-no-repeat
    }

    .create-entry-button {
        @apply form-button button-sm pl-8;
        @apply icon-plus-circle-sky-500 bg-[left_0.5em_center] bg-[length:1.25rem_1.25rem] bg-no-repeat

    }

    .panel-form {
        textarea {
            @apply form-textarea box-border w-full text-sm leading-tight placeholder:text-sm
        }

        button {
            @apply form-button button-sm;

            &[data-type=cancel] {
                @apply button-contained-secondary
            }

            &[data-type=confirm] {
                @apply button-contained
            }
        }
    }


    .list-move,  .list-enter-active, .list-leave-active {
        transition: all 0.3s ease;
    }

    .list-enter-from, .list-leave-to {
        opacity: 0;
        transform: translateX(16px);
    }

    /* ensure leaving items are taken out of layout flow so that moving
        animations can be calculated correctly. */
    .list-leave-active {
        position: absolute;
        display: none;
    }
</style>
