<template>
    <h3 class="mb-4 text-base">Veranstalter/Auftraggeber</h3>
    <div v-if="!isRevealed">
        <template v-if="isEmpty">
            <button v-if="canManageData" @click="reveal" class="create-entry-button">Hinzufügen</button>
            <span v-else class="text-sm">Nichts eingetragen</span>
        </template>

        <div v-else :data-not-editable="!canManageData" class="group/panel-entry panel-entry">
            <template v-if="!!organizer.addressContact">
                <AddressContact :contact="organizer.addressContact" class="text-sm" />
            </template>

            <template v-else-if="!!organizer.addressLocation">
                <AddressLocation :location="organizer.addressLocation" class="text-sm" />
            </template>

            <template v-else-if="!!organizer.member">
                <AddressMember :member="organizer.member" class="text-sm" />
            </template>

            <template v-else>
                <AddressCustom :address="organizer.custom" class="text-sm" />
            </template>

            <button v-if="isEditable" @click="reveal" class="edit-entry-button" title="Bearbeiten" />
            <a v-else-if="hasLegacyHint" :href="useClassicEventUrl(event)" target="_blank" title="Im EM-Klassik öffnen" class="legacy-entry-button" />
        </div>
    </div>
    <div v-else class="panel-form">
        <textarea rows="3" v-model="formModel.custom" placeholder="Trage hier den oder die Veranstalter ein." />

        <div class="mt-1 flex justify-end gap-2">
            <button @click="cancel" data-type="cancel">Abbrechen</button>
            <button @click="confirm" data-type="confirm">Speichern</button>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { syncRef, useConfirmDialog, useVModel } from '@vueuse/core'
    import { pick } from 'lodash-es'

    const { canManageData } = inject(EventPermissionsKey)

    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const { data: organizer } = useEventQuery('organizer')

    const commit = useCommitEventMutation(props.event)

    const formModel = ref<ApiModel<'ComplexAddressContact'>>()

    syncRef(organizer, formModel, {
        direction: 'ltr',
        transform: {
            ltr(organizer) {
                return useModelFactory('ComplexAddressContact').fromJson(
                    //...
                    JSON.parse(JSON.stringify(organizer ?? {})) as object
                )
            }
        }
    })

    const { reveal, confirm, cancel, isRevealed, onConfirm } = useConfirmDialog()

    onConfirm(() => {
        commit('updateOrganizer', formModel.value)
    })

    const isEmpty = computed(() => {
        if (!organizer.value) {
            return true
        }

        const relevantValues = Object.values(
            // Extract relevant fields
            pick(organizer.value, ['addressContact', 'addressLocation', 'member', 'custom'])
        )

        return relevantValues.every((value) => !value)
    })

    /**
     * Address is only editable when the address is of type "custom".
     * Which is the case, when everything is empty
     */
    const isEditable = computed(() => {
        if (!canManageData.value) {
            return false
        }

        if (!organizer.value) {
            return false
        }

        const relevantValues = Object.values(
            // Extract relevant fields (that's all but custom)
            pick(organizer.value, ['addressContact', 'addressLocation', 'member'])
        )

        return relevantValues.every((value) => !value)
    })

    const hasLegacyHint = computed(() => {
        if (!canManageData.value) {
            return false
        }

        if (!organizer.value) {
            return false
        }

        const relevantValues = Object.values(
            // Extract relevant fields (that's all but custom)
            pick(organizer.value, ['addressContact', 'addressLocation', 'member'])
        )

        return !!relevantValues.some((value) => !!value)
    })
</script>

<style lang="pcss" scoped>

    .panel-entry {
        @apply relative -m-2 py-2 pl-2 pr-5 hover:bg-blue-50/20 data-[not-editable=true]:pointer-events-none
    }


    .legacy-entry-button {
        @apply absolute -right-1 -top-1 invisible group-hover/panel-entry:visible;
        /* @apply opacity-50 hover:opacity-100; */
        @apply w-6 h-6 bg-center bg-[length:1rem_1rem] bg-no-repeat;
        /* @apply hover:bg-yellow-50 hover:border-yellow-100; */
        @apply form-button button-xs cursor-pointer rounded-full p-1;
        @apply w-7 h-7 bg-center bg-[length:1.25rem_1.25rem] bg-no-repeat;
        @apply icon-external-link-blue-500;
    }

    .edit-entry-button {
        @apply absolute -right-1 -top-1 md:hidden group-hover/panel-entry:inline;
        @apply form-button button-xs absolute -right-1 -top-1 md:hidden cursor-pointer rounded-full p-1 group-hover/panel-entry:inline;
        @apply icon-pencil-alt-blue-500 w-7 h-7 bg-center bg-[length:1.25rem_1.25rem] bg-no-repeat
    }

    .create-entry-button {
        @apply form-button button-sm pl-8;
        @apply icon-plus-circle-sky-500 bg-[left_0.5em_center] bg-[length:1.25rem_1.25rem] bg-no-repeat

    }

    .panel-form {
        textarea {
            @apply form-textarea box-border w-full text-sm leading-tight placeholder:text-sm
        }

        button {
            @apply form-button button-sm;

            &[data-type=cancel] {
                @apply button-contained-secondary
            }

            &[data-type=confirm] {
                @apply button-contained
            }
        }
    }
</style>
