<template>
    <div v-if="value === 'rejected'" title="Abgelehnt" class="group">
        <IconAssignmentRejected class="icon text-blue-600" />
        <span class="label">Abgelehnt</span>
    </div>
    <div v-else-if="value === 'assigned'" title="Zugeordnet" class="group">
        <IconUser class="icon text-blue-600" />
        <span class="label">Zugeordnet</span>
    </div>
    <div v-else class="group">
        <span class="icon inline-block"></span>
    </div>
</template>

<script lang="ts" setup>
    defineProps<{
        value: 'none' | 'assigned' | 'rejected'
    }>()
</script>

<style lang="pcss" scoped>

    .group {
        @apply inline-flex items-center
    }
    .icon {
        @apply h-4 aspect-square items-center justify-center rounded-full flex-none;
        @apply group-data-[size=sm]:h-3;
        @apply group-data-[size=lg]:h-5;
        @apply group-data-[size=xl]:h-6;
    }

    .label {
        @apply group-data-[no-label]:sr-only ml-[0.5em] text-start leading-tight
    }
</style>
