<script lang="ts" setup>
    import { UseConfirmDialogReturn } from '@vueuse/core'
    import { PrintType } from '~/composables/print'

    const props = defineProps<{
        controller: UseConfirmDialogReturn<boolean, any, any>
        event: ApiModel<'Event'>
    }>()

    const { isRevealed, cancel, confirm: _confirm } = props.controller

    const { canPrint = false } = inject(EventPermissionsKey) || {}
    const { getDocument, printTypeOptions } = usePrint()
    const { download } = useDownloadBase64()

    const isLoading = ref(false)

    const printType = ref(printTypeOptions[0])

    async function downloadDocument() {
        const document = await getDocument(props.event.id, printType.value.id as PrintType)
        if (typeof document === 'string') download(document, `ereignis-${props.event.id}-${printType.value.label}`)
    }

    async function confirm() {
        isLoading.value = true
        await downloadDocument()
        isLoading.value = false

        _confirm()
    }
</script>

<template>
    <UiDialog :title="'Ereignis drucken'" :is-revealed="isRevealed" allow-overflow>
        <UiLoader :is-loading="isLoading">
            <p class="mb-4">Welche Informationen über das Ereignis möchtest du drucken?</p>
            <UiListboxInput class="mx-auto h-12 w-4/5" v-model="printType" :options="printTypeOptions" />
        </UiLoader>
        <template #buttons>
            <button type="button" :disabled="isLoading" @click="cancel" class="form-button button-sm">Abbrechen</button>
            <button
                type="button"
                :disabled="isLoading || !canPrint"
                @click="confirm"
                class="form-button button-sm button-contained disabled:pointer-events-none disabled:opacity-50">
                Drucken
            </button>
        </template>
    </UiDialog>
</template>
