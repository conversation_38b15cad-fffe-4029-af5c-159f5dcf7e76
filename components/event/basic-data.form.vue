<template>
    <fieldset>
        <UiLoader :is-loading="isLoading" class="mb-4 grid grid-cols-1 gap-6">
            <UiFormField label="Typ/Art *" class="mt-2 w-full select-none">
                <div v-if="loadingOperationTypes" class="blur-xs flex animate-pulse gap-x-2 text-sm">
                    <span class="inline-block h-4 w-4 rounded-full bg-gray-200" />
                    <span class="inline-block h-4 w-24 rounded-md bg-gray-200" />
                    <span class="inline-block h-4 w-4 rounded-full bg-gray-200" />
                    <span class="inline-block h-4 w-16 rounded-md bg-gray-200" />
                </div>
                <div v-else class="flex text-xs">
                    <UiRadioInput
                        v-for="option in operationTypes ?? []"
                        :key="option.id"
                        v-model="category"
                        :label="option.value2"
                        :value="option"
                        class="mr-3" />
                </div>
            </UiFormField>

            <UiFormField v-if="!isApprenticeship" class="mt-1" label="Bezeichnung *">
                <CodeEntrySelect v-model="event.description" list="ServiceLogDescription" />
            </UiFormField>

            <UiFormField v-if="isApprenticeship" class="mt-1" label="Bezeichnung *">
                <UiUniversalSelect
                    v-model="event.apprenticeshipType"
                    :options="apprenticeshipTypes"
                    :is-loading="isLoadingApprenticeshipTypes"
                    label-prop="name" />
            </UiFormField>

            <CodeEntryInputCombobox
                label="Nähere Bezeichnung *"
                placeholder="Nähere Bezeichnung eingeben"
                v-model="event.extendedDescription"
                list-type="EventExtendedDescription">
            </CodeEntryInputCombobox>

            <UiFormField
                v-if="operations.length > 0 || isOperationActive"
                label="Verknüpfter Einsatz"
                withTooltip
                tooltip-message="Für alle Ereignisse zur UEFA Euro 2024 ⚽">
                <UiUniversalSelect
                    class="relative"
                    v-model="event.operation"
                    :options="operations"
                    :is-loading="isLoading"
                    label-prop="name"
                    :withCancelButton="!isOperationActive" />
            </UiFormField>
            <UiIntervalInput label="Zeitraum *" v-model:start="event.dateFrom" v-model:end="event.dateUpTo" />
        </UiLoader>
    </fieldset>
</template>

<script lang="ts" setup>
    import { useVModel } from '@vueuse/core'
    import ListType from '~~/enums/listType'
    import EventOperationType from '~~/enums/event-operation-type'
    import { useCodeEntries } from '~~/queries/code-entries'

    const props = defineProps<{
        modelValue: ApiModel<'Event'>
    }>()

    const emit = defineEmits<{
        (event: 'update:modelValue', value: ApiModel<'Event'>)
    }>()

    const event = useVModel(props, 'modelValue', emit)
    const { data, operationSearch, isLoading } = useOperationSearch()
    operationSearch()

    const operations = computed<ApiModel<'Operation'>[]>(() => {
        return data.value?.items.length > 0 ? data.value.items : []
    })

    const isOperationActive = computed<boolean>(() => {
        return event.value.operation?.active == false && operations.value.length == 0
    })

    const { data: operationTypes, isLoading: loadingOperationTypes } = useCodeEntries(ListType.OperationType)

    const category = computed({
        get: () => {
            return event.value?.category
        },
        set: (value) => {
            if (value.value1 === EventOperationType.Operation) {
                event.value.apprenticeshipType = null
            }

            if (value.value1 === EventOperationType.Apprenticeship) {
                event.value.description = null
            }

            event.value.category = value
        }
    })

    const isApprenticeship = computed(() => {
        return event.value.category?.value1 === EventOperationType.Apprenticeship
    })

    const { data: apprenticeshipTypes, isFetching: isLoadingApprenticeshipTypes } = useApprenticeshipTypes()
</script>
