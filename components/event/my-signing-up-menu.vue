<template>
    <EventPlanningSigningUpChangeStatus :signing-up="signingUp" :event="event" @update="updateStatus" v-bind="$attrs">
        <template #default="{ status }">
            <slot v-bind="{ status }">
                <EventSigningUpStatus :status="status" />
            </slot>
        </template>
    </EventPlanningSigningUpChangeStatus>

    <EventPlanningSigningUpPeriodsDialog :controller="periodsDialog" :event="event" />
</template>

<script lang="ts" setup>
    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const { signingUp, updateStatus, periodsDialog } = useMyEventSignup(toRef(props, 'event'))
</script>

<style lang="pcss" scoped>
    .menu-item {
        @apply ui-disabled:opacity-40 ui-disabled:italic text-xs text-yellow-900 ui-disabled:cursor-not-allowed
    }
</style>
