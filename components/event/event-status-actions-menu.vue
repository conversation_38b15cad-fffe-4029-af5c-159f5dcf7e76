<template>
    <UiMenu
        class="group/interactive cursor-pointer"
        :button-style="'group-[.flex]/status:text-grey-900 hover:group-[.flex]/status:text-grey-900 hover:group-[.flex]/status:bg-blue-50'"
    >
        <template #button>
            <slot name="default">
                <EventStatus :status="event.status" :with-label="true" />
            </slot>
        </template>
        <UiMenuItem v-if="canClose" @click="action.closeEvent" icon="lock-closed">Abschließen</UiMenuItem>
        <UiMenuItem v-if="canReopen" @click="action.openEvent" icon="lock-open"> Erneut öffnen</UiMenuItem>
        <UiMenuItem v-if="canCancel" @click="action.cancelEvent" icon="exclamation"> Absagen</UiMenuItem>
        <UiMenuItem v-if="canReactivate" @click="action.reactivateEvent" icon="fast-forward">Wieder aktivieren</UiMenuItem>
    </UiMenu>
</template>

<script setup lang="ts">
    import { injectEventActions } from '~~/composables/event-actions'

    defineProps<{
        event: ApiModel<'Event'>
    }>()

    const { canClose, canCancel, canReactivate, canReopen } = inject(EventPermissionsKey)

    const action = injectEventActions()
</script>

<style scoped></style>
