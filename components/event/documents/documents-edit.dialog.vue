<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel">
        <template #title>Dokument bearbeiten</template>

        <template v-if="isRevealed">
            <EventDocumentsMetadataForm v-model="documentModel" />
        </template>

        <template #footer>
            <button class="form-button button-contained-secondary" @click="cancel">Abbrechen</button>
            <button class="form-button button-contained" @click="submit" :disabled="validator.$invalid">Änderungen speichern</button>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import useVuelidate from '@vuelidate/core'
    import { required } from '@vuelidate/validators'
    import { syncRef } from '@vueuse/core'
    import { DialogController } from '~~/composables/dialog-controller'

    const props = defineProps<{
        controller: DialogController<'editEventDocument'>
        document: ApiModel<'EventDocumentMeta'>
    }>()

    const { isRevealed, confirm, cancel } = props.controller

    const documentModel = ref<ApiModel<'EventDocumentMeta'>>(null)

    syncRef(toRef(props, 'document'), documentModel, {
        direction: 'ltr',
        transform: {
            ltr: (document) => useModelFactory('EventDocumentMeta').fromJson(JSON.parse(JSON.stringify(document)) as object)
        }
    })

    const rules = computed(() => {
        const _rules: Record<string, any> = {
            type: { required },
            permission: { required },
            fileName: { required },
            description: { required }
        }

        return _rules
    })

    const validator = useVuelidate(rules, documentModel)

    function submit() {
        confirm(documentModel.value)
    }
</script>
