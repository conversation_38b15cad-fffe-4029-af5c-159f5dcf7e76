<template>
    <div v-if="canReadDocuments">
        <h2 class="mb-4">Dokumente</h2>

        <TransitionGroup tag="div" name="list" class="mb-4 space-y-2">
            <EventDocumentsPanelItem
                v-for="document in sortedDocuments"
                :document="document"
                :key="document.id"
                @update:document="updateDocument"
                @remove:document="removeDocument"
                class="rounded-sm border p-2 text-sm" />
        </TransitionGroup>

        <button v-if="canManageDocuments" @click="createDocument" class="form-button button-sm">
            <IconPlusCircle class="h-auto w-5" />
            Hinzufügen
        </button>
        <div v-else-if="!hasDocuments" class="pl-4 text-sm">Keine Dokumente</div>
    </div>
</template>

<script lang="ts" setup>
    /**
     * This was basically taken from "event/details"
     *
     * Probably we have to rework it
     */
    import UploadComposer from './composer.vue'

    import { sortBy } from 'lodash-es'

    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    provide('event-id', props.event.id)

    const { canReadDocuments, canManageDocuments } = inject(EventPermissionsKey)

    const { data: documents } = useEventQuery('documents')
    const commit = useCommitEventMutation(props.event)

    const hasDocuments = computed(() => {
        return documents.value.length > 0
    })

    const sortedDocuments = computed(() => {
        return sortBy(documents.value, ['fileName', 'fileNameOriginal'])
    })

    async function updateDocument(document: ApiModel<'EventDocumentMeta'>) {
        commit('updateDocument', document)
    }

    async function removeDocument(document: ApiModel<'EventDocumentMeta'>) {
        commit('removeDocument', document)
    }

    async function createDocument() {
        const uploadComposer = useComposer(UploadComposer, { eventId: props.event.id })

        const { data, isCanceled } = await uploadComposer()

        if (!isCanceled) {
            commit('createDocument', data)
        }
    }

    // async function openUploadWindow() {
    //     const upload = useComposer(UploadComposer, { eventId: props.event.id })
    //     await upload()
    // }
</script>

<style lang="pcss" scoped>
    .list-move,  .list-enter-active, .list-leave-active {
        transition: all 0.3s ease;
    }

    .list-enter-from, .list-leave-to {
        opacity: 0;
        transform: translateX(16px);
    }

    /* ensure leaving items are taken out of layout flow so that moving
        animations can be calculated correctly. */
    .list-leave-active {
        position: absolute;
        display: none;
    }
</style>
