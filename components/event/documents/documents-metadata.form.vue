<template>
    <fieldset class="mb-4 grid grid-cols-1 gap-6">
        <UiFormField label="Art *">
            <CodeEntrySelect v-model="documentMeta.type" list="DocumentType" />
        </UiFormField>

        <UiFormField label="Beschreibung *">
            <input type="text" v-model="documentMeta.description" class="form-input box-border w-full" />
        </UiFormField>

        <UiFormField label="Dateiname">
            <input type="text" v-model="documentMeta.fileName" class="form-input box-border w-full" />
        </UiFormField>

        <UiFormField label="Wer kann das Dokument sehen? *">
            <CodeEntrySelect v-model="documentMeta.permission" list="DocumentPermission" />
        </UiFormField>
    </fieldset>
</template>

<script lang="ts" setup>
    import { useVModel } from '@vueuse/core'

    const props = defineProps<{
        modelValue: ApiModel<'EventDocumentMeta'>
    }>()

    const emit = defineEmits<{
        (event: 'update:modelValue', value: ApiModel<'EventDocumentMeta'>): any
    }>()

    const documentMeta = useVModel(props, 'modelValue', emit)
</script>
