<template>
    <div class="group flex items-center gap-2">
        <UiIcon name="document" class="h-6 w-6 text-blue-500" />
        <div class="grow">
            {{ document.fileName ? document.fileName : document.fileNameOriginal }}
            <span class="text-grey-500"> | {{ document.type.value2 }}</span>
            <p class="text-xs">{{ document.description }}</p>
        </div>
        <div class="hidden items-center gap-x-0 group-hover:flex">
            <a
                :href="download"
                :download="document.fileName ?? document.fileNameOriginal"
                title="Dokument herunterladen"
                class="form-button button-sm rounded-md">
                <UiIcon name="document-download" class="h-4 w-4" />
            </a>

            <button v-if="canManageDocuments" @click="editDocument.reveal" title="Metadaten bearbeiten" class="form-button button-sm rounded-md">
                <UiIcon name="pencil-alt" class="h-4 w-4" />
            </button>

            <button v-if="canManageDocuments" @click="confirmRemoval.reveal" title="Dokument löschen" class="form-button button-sm rounded-md">
                <UiIcon name="trash" class="h-4 w-4" />
            </button>
        </div>

        <template v-if="editDocument.isRevealed.value">
            <EventDocumentsEditDialog :document="document" :controller="editDocument" />
        </template>

        <UiConfirmDialog title="Datei löschen" :controller="confirmRemoval" confirm-with="Löschen" cancel-with="Behalten">
            Wenn Sie dieses Dokument löschen, wird es unwiderruflich aus der Liste entfernt.
        </UiConfirmDialog>
    </div>
</template>

<script lang="ts" setup>
    /**
     * This was basically taken from "event/details/document.vue"
     */

    import { useConfirmDialog } from '@vueuse/core'

    const props = defineProps<{
        document: ApiModel<'EventDocumentMeta'>
    }>()

    const { canManageDocuments } = inject(EventPermissionsKey)

    const eventId: number = inject('event-id', -1)

    const emit = defineEmits<{
        (event: 'update:document', value: ApiModel<'EventDocumentMeta'>): any
        (event: 'remove:document', value: ApiModel<'EventDocumentMeta'>): any
    }>()

    const download = computed(() => {
        return '/v/api/events/' + eventId + '/documents/' + props.document.id + '/download'
    })

    const editDocument = useDialogController('editEventDocument')
    editDocument.onConfirm((document) => {
        emit('update:document', document)
    })

    const confirmRemoval = useConfirmDialog()
    confirmRemoval.onConfirm(() => {
        emit('remove:document', props.document)
    })
</script>
