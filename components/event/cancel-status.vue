<template>
    <div
        v-if="event.status === 'CANCELED'"
        class="group mr-2 inline-flex"
        @mouseenter="showPopperHandler"
        @mouseleave="hidePopperHandler"
        @click.stop="togglePopperHandler">
            <IconInformationCircle ref="iconElement" class="ml-1 h-5 w-5 text-sky-500 hover:cursor-pointer" />
            <div
                class="pointer-events-none absolute z-10 rounded-lg bg-white pl-4 py-2 pr-4 text-sm text-gray-900 opacity-0 shadow-lg transition-opacity group-hover:opacity-100"
                :class="{'hidden': !popperIsShown,'inline-block': popperIsShown }"
                ref="tooltipElement">
                <p class="font-base font-semibold pb-.5">{{ reasonOfCancellation.value2 }}</p>
                <p v-if="remarkOfCancellation" class="font-light">{{ remarkOfCancellation  }}</p>
            </div>
    </div>
</template>

<script lang="ts" setup>
    import { PropType } from 'vue'

    const props = defineProps({
        event: {
            required: true,
            type: Object as PropType<ApiModel<'Event'>>
        }
    })

    const reasonOfCancellation = ref<ApiModel<'ReasonOfCancellation'>>(props.event.reasonOfCancellation)
    const remarkOfCancellation = ref<string>(props.event.remarkOfCancellation)

    const iconElement = ref<HTMLElement>(null)
    const tooltipElement = ref<HTMLElement>(null)
    const popperIsShown = ref(true)


    const popperInstance  = usePopper(iconElement, tooltipElement, { placement: 'auto' })

    const togglePopperHandler = () => {
        if(popperIsShown.value) {
        hidePopperHandler()
        } else {
        showPopperHandler()
        }
    }

    const showPopperHandler = () => {
        popperIsShown.value = true
        popperInstance.value.update()
    }

    const hidePopperHandler = () => popperIsShown.value = false
</script>
