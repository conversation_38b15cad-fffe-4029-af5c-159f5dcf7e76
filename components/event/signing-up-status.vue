<template>
    <span v-if="status === Status.AVAILABLE" class="group">
        <IconCheck class="icon bg-green-200" />
        <span class="label">verfügbar</span>
    </span>

    <span v-else-if="status === Status.PARTIAL" class="group">
        <IconMinus class="icon -rotate-45 bg-yellow-300" />
        <span class="label">teilweise verfügbar</span>
    </span>

    <span v-else-if="status === Status.UNAVAILABLE" class="group">
        <IconX class="icon bg-softred-300" />
        <span class="label">nicht verfügbar</span>
    </span>

    <span v-else-if="status === Status.UNKNOWN" class="group">
        <IconQuestionMark class="icon bg-blue-200" />
        <span class="label">offen</span>
    </span>
</template>

<script lang="ts" setup>
    import Status from '~~/enums/event-signing-up-status'

    defineProps<{
        status: Status
        signingUp?: ApiModel<'SigningUp'>
    }>()
</script>

<style lang="pcss" scoped>

    .group {
        @apply inline-flex items-center
    }
    .icon {
        @apply inline-flex h-4 aspect-square items-center justify-center rounded-full flex-none py-[0.25em];
        @apply group-data-[size=sm]:h-3;
        @apply group-data-[size=lg]:h-5;
        @apply group-data-[size=xl]:h-6;
    }

    .label {
        @apply group-data-[no-label]:sr-only ml-[0.5em] text-start leading-tight
    }
</style>
