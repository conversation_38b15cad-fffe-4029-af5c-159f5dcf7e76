<template>
    <p class="font-bold text-lg">Beginn des neuen Ereignisses</p>
        <UiFormField v-for="event in copyEvents" :key="event.id" label="Zeitraum: Beginn*" class="flex flex-col my-4">
            <div class="flex gap-4">
                <UiDateTimePicker v-model="event.dateFrom" class="w-full"/>
                <button v-if="copyEvents.length > 1" @click="removeEvent(event.id)" class="flex items-center form-button button-sm rounded-md">
                    <UiIcon name="trash" class="h-4 w-4" />
                </button>
            </div>
        </UiFormField>
        <button type="button" class="form-button ml-auto mt-2" @click="addEvent" :disabled="canAddEvent">+ weitere <PERSON> hinzufügen</button>
</template>

<script lang="ts" setup>
    import { useVModel } from '@vueuse/core'

    const props = defineProps<{
        modelValue: ApiModel<'Event'>[]
        event: ApiModel<'Event'>
        originalEventDuration: number
    }>()


    const emit = defineEmits<{
        (e: 'update:modelValue', value: number): void
    }>()

    const copyEvents = useVModel(props, 'modelValue', emit)

    const canAddEvent = computed(()=>{
        return copyEvents.value.length > 99
    })

    const { addEvent, removeEvent } = useCopyEvents(copyEvents, props.event)

</script>
