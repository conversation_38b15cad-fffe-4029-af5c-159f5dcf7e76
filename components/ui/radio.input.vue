<script lang="ts" setup>
    import { useVModel } from '@vueuse/core'
    import { uniqueId } from 'lodash-es'

    type Props = {
        modelValue: any
        value: any
        label: string
        name?: string
    }

    type Emit = {
        (e: 'update:modelValue', value: Props['value']): void
    }

    const id = uniqueId('radio-')

    const props = defineProps<Props>()

    const emit = defineEmits<Emit>()

    const picked = useVModel(props, 'modelValue', emit)
</script>

<template>
    <div class="relative flex items-start">
        <div class="flex h-5 items-center">
            <input :id="id" :aria-describedby="`${id}-description`" :name="name" type="radio"  v-model="picked" :value="value" class="form-radio" />
        </div>
        <div class="ml-2 w-full text-sm">
            <label :for="id" class="text-grey-900 cursor-pointer text-sm font-medium">{{ label }}</label>
            <p :id="`${id}-description`" class="text-grey-900 text-xs font-light">
                <slot></slot>
            </p>
        </div>
    </div>
</template>
