<template>
    <UiDialog :is-revealed="isRevealed" :title="title || 'Eingabe bestätigen'" class="z-50">
        <slot>Bestätigen Sie diese Eingabe?</slot>
        <template #buttons>
            <button type="button" class="form-button button-sm" @click="cancel">
                {{ cancelWith || 'nein' }}
            </button>
            <button
                type="button"
                class="form-button button-contained button-sm disabled:pointer-events-none disabled:opacity-50"
                :class="{'hidden' : withoutConfirmation}"
                :disabled="isConfirmDisabled"
                @click="confirm">
                {{ confirmWith || 'ja' }}
            </button>
        </template>
    </UiDialog>
</template>

<script setup lang="ts">
    import type { MaybeRef, UseConfirmDialogReturn } from '@vueuse/core'

    type Props = {
        controller: UseConfirmDialogReturn<any, any, any>
        title?: string
        confirmWith?: string
        confirmData?: MaybeRef<any>
        cancelWith?: string
        isConfirmDisabled?: boolean
        maxWidthClass?: string
        withoutConfirmation?: boolean
    }

    const props = defineProps<Props>()

    const { isRevealed, cancel, confirm: _confirm } = props.controller

    const confirm = function () {
        return _confirm(props.confirmData ?? null)
    }
</script>
