<script lang="ts" setup>
    import { useVModel } from '@vueuse/core'
    import { uniqueId } from 'lodash-es'

    type Props = {
        modelValue: boolean
        label: string
        circle?: boolean
        name?: string
        checkboxRef?: Ref
    }

    type Emit = {
        (e: 'update:modelValue', value: boolean): void
    }

    const id = uniqueId('checkbox-')

    const props = defineProps<Props>()

    const emit = defineEmits<Emit>()

    const value = useVModel(props, 'modelValue', emit)

    const checkbox= ref()

    function resetInputValue() {
        value.value = false;
        if(checkbox.value.checked) {
            checkbox.value.click()
        }
    }

</script>
<template>
    <div class="flex">
        <div class="flex items-center h-5">
            <input v-model="value" ref="checkbox" :id="id" :aria-describedby="`${id}-description`" type="checkbox" class="form-checkbox" />
        </div>
        <div class="w-full ml-2 text-sm">
            <label :for="id" class="text-sm font-medium cursor-pointer text-grey-900"> {{ label }}</label>
            <p :id="`${id}-description`" class="text-xs font-light text-grey-900">
                <span class="sr-only">  {{ label }} </span>
                <slot></slot>
            </p>
        </div>
        <button @click="resetInputValue" class="text-sm text-sky-500 no-underline hover:underline cursor-pointer">zurücksetzen</button>
    </div>
</template>
