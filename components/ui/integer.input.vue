<template>
    <fieldset class="relative my-3 flex gap-2 disabled:pointer-events-none disabled:opacity-40">
        <div v-if="topLabel" class="absolute -top-5 text-xs">{{ topLabel }}</div>
        <button
            v-if="withButtons"
            @click="
                () => {
                    typeof value == 'number' ? value-- : null
                }
            "
            class="form-button button-contained-secondary flex-none px-3"
            :class="{ 'pointer-events-none opacity-50': value <= min }">
            <IconMinus class="inline h-4 w-4 text-blue-500" />
        </button>
        <div class="w-16 flex-none">
            <input
                v-model="value"
                :min="min"
                :max="max"
                :id="id"
                :aria-describedby="`${id}-description`"
                type="number"
                class="form-input box-border w-full px-2 text-center" />
        </div>
        <button
            v-if="withButtons"
            @click="
                () => {
                    typeof value == 'number' ? value++ : null
                }
            "
            class="form-button button-contained-secondary flex-none px-3"
            :class="{ 'pointer-events-none opacity-50': max && value >= max }">
            <IconPlus class="inline h-4 w-4 text-blue-500" />
        </button>
        <label v-if="label" class="text-grey-600 flex-none py-3 text-base leading-4 empty:hidden">{{ label }}</label>
    </fieldset>
</template>
<script lang="ts" setup>
    import { uniqueId } from 'lodash-es'

    type Props = {
        modelValue: number | string | null
        label?: string
        topLabel?: string
        min?: number
        max?: number
        withButtons?: boolean
    }

    type Emit = {
        (e: 'update:modelValue', value: number): void
    }

    const id = uniqueId('integer-')

    const props = withDefaults(defineProps<Props>(),
        {
            withButtons: true
        }
    )

    const emit = defineEmits<Emit>()

    const value = ref(props.modelValue ?? 0)

    const min = props.min || 0
    const max = props.max || null

    watch(
        () => props.modelValue,
        (newVal) => {
            value.value = newVal ?? 0
        }
    )

    watch(value, () => {
        value.value = typeof value.value === 'string' ? 0 : value.value
        value.value = value.value % 1 === 0 ? value.value : Math.round(value.value)
        value.value = value.value < 0 ? 0 : value.value
        if (props.max) value.value = value.value > props.max ? props.max : value.value
        if (props.min) value.value = value.value < props.min ? props.min : value.value
        emit('update:modelValue', value.value)
    })
</script>
<style scoped>
    /* Disable native up and down arrows on form input */

    /* Chrome, Safari, Edge, Opera */
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    input[type='number'] {
        -moz-appearance: textfield;
    }
</style>
