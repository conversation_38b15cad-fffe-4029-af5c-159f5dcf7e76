<template>
    <SwitchGroup>
        <div class="my-3 flex items-center">
            <Switch
                v-model="value"
                :class="value ? 'bg-softred-500' : 'bg-gray-300'"
                class="relative inline-flex h-[1.375rem] w-10 cursor-pointer items-center rounded-full transition-colors">
                <span
                    :class="value ? 'translate-x-[1.3rem]' : 'translate-x-[0.2rem]'"
                    class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out" />
            </Switch>
            <SwitchLabel class="ml-3 cursor-pointer text-sm">{{ label }}</SwitchLabel>
        </div>
    </SwitchGroup>
</template>

<script lang="ts" setup>
    import { Switch, SwitchLabel, SwitchGroup } from '@headlessui/vue'
    import { useVModel } from '@vueuse/core'

    type Props = {
        modelValue: boolean
        label?: string
    }

    type Emit = {
        (e: 'update:modelValue', value: boolean): void
    }

    const props = defineProps<Props>()

    const emit = defineEmits<Emit>()

    const value = useVModel(props, 'modelValue', emit)
</script>
