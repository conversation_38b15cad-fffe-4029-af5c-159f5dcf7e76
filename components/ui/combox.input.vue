<template>
    <div>
        <Combobox as="div" v-model="selectedItems" :multiple="isMultiple" :nullable="isDatalist">
            <ComboboxLabel v-if="label" class="text-grey-900 mb-1 text-xs leading-4 empty:hidden">{{ label }}</ComboboxLabel>
            <div class="relative" ref="inputElement">
                <ComboboxInput
                    class="form-input form-input-has-control w-full"
                    autocomplete="off"
                    :placeholder="placeholder ? placeholder : 'Für Vorschläge tippen'"
                    :display-value="getDisplayValue"
                    :disabled="disabled"
                    :type="isNumberType ? 'number' : 'text'"
                    :min="isNumberType && minNumber ? minNumber : null"
                    :max="isNumberType && maxNumber ? maxNumber : null"
                    @change="_query = $event.target.value" />

                <ComboboxButton as="button">
                    <IconChevronDown class="form-input-control" aria-hidden="true" />
                </ComboboxButton>

                <div ref="optionsElement" class="absolute z-10 w-full">
                    <ComboboxOptions
                        as="ul"
                        class="mt-1 max-h-60 overflow-auto rounded-md bg-white text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                        <!-- When in used as datalist we are creating a custom option on the fly-->
                        <ComboboxOption v-if="customOption" :value="customOption" as="li" class="hidden">
                            <span class="">{{ customOption.label }}</span
                            >--
                        </ComboboxOption>

                        <ComboboxOption
                            v-for="option in filteredOptions"
                            :key="option.id"
                            :disabled="option.disabled"
                            :value="option"
                            v-slot="{ active, selected }"
                            as="template">
                            <li
                                class="text-grey-800 relative cursor-default select-none py-2 pl-3 pr-9"
                                :class="{ 'bg-grey-100 ': active, 'italic': selected, 'opacity-50': option.disabled }">
                                <span class="block" :class="[multiline ? 'break-words' : 'truncate']" v-text="option.label" />
                                <span v-if="selected" class="text-grey-500 absolute inset-y-0 right-0 flex items-center pr-4">
                                    <IconCheck class="h-5 w-5" aria-hidden="true" />
                                </span>
                            </li>
                        </ComboboxOption>

                        <ComboboxOption v-if="loadOption" as="div" :value="null" @click.stop>
                            <li
                                class="bg-blue-100 py-2 pl-3 pr-9 text-blue-600 hover:cursor-pointer hover:text-blue-800"
                                @click.prevent="emit('load', 'OK')">
                                Weitere Ergebnisse hochladen
                            </li>
                        </ComboboxOption>

                        <div v-if="!filteredOptions.length" class="text-grey-400 px-3 py-2">
                            <template v-if="!!query">
                                Keine Vorschläge für <span class="text-grey-600 italic">{{ query }}</span>
                            </template>
                            <template v-else> Keine Vorschläge </template>
                        </div>
                    </ComboboxOptions>
                </div>
            </div>
            <div v-if="summary && displaySummary" class="mt-2 flex w-full flex-wrap gap-2 text-xs">
                <span v-if="!summary.length" class="text-grey-900 border-grey-200 rounded-sm border px-2 py-1">Nichts ausgewählt</span>

                <UiLabel v-else v-for="{ id, label, reset } in summary" :remove="reset" :key="id" :title="label">
                    {{ label }}
                </UiLabel>
            </div>
        </Combobox>
    </div>
</template>

<script lang="ts" setup>
    import { Combobox, ComboboxInput, ComboboxOptions, ComboboxOption, ComboboxButton, ComboboxLabel } from '@headlessui/vue'

    import { useVModel } from '@vueuse/core'
    import { uniqueId } from 'lodash-es'
    import { useFuse } from '@vueuse/integrations/useFuse'

    type Props = {
        modelValue: Option | Option[] | null
        options: Option[]
        datalist?: boolean
        label?: string
        placeholder?: string
        displaySummary?: boolean
        disabled?: boolean
        isNumberType?: boolean
        minNumber?: number
        maxNumber?: number
        query?: string
        loadOption?: boolean
        multiline?: boolean
    }

    type Emit = {
        (e: 'update:modelValue', value: Props['modelValue']): void
        (e: 'update:query', value: Props['query']): void
        (e: 'load', value: string): void
    }

    const props = defineProps<Props>()

    const emit = defineEmits<Emit>()

    const inputElement = ref<HTMLElement>(null)
    const optionsElement = ref<HTMLElement>(null)

    usePopper(inputElement, optionsElement, { placement: 'auto' })

    const selectedItems = useVModel(props, 'modelValue', emit)

    const _query = ref(props.query)

    function isArray(input: Option | Option[]): input is Option[] {
        return Array.isArray(input)
    }

    const isMultiple = computed(() => {
        return !!isArray(selectedItems.value)
    })

    const isDatalist = computed(() => {
        return props.datalist !== undefined && props.datalist === true
    })

    const { results } = useFuse<Option>(_query, toRef(props, 'options'), {
        matchAllWhenSearchEmpty: true,
        fuseOptions: {
            keys: ['label'],
            includeScore: true,
            includeMatches: true,
            threshold: 0.4,
            distance: 800,
            findAllMatches: false,
            ignoreLocation: false
        }
    })

    const filteredOptions = computed(() => {
        return props.options.filter((option) => {
            if(_query.value) {
                return option.label.toLocaleLowerCase().includes(_query.value.toLocaleLowerCase())
            } else {
                return option.label.toLocaleLowerCase()
            }
        })
    })


    const customOption = computed(() => {
        return isDatalist.value ? { id: uniqueId('combobox-custom-'), label: _query.value } : null
    })

    if (isArray(selectedItems.value)) {
        const selectedIds = selectedItems.value.map((item) => item.id)
        selectedItems.value = props.options.filter((item) => selectedIds.includes(item.id))
    } else if (props.options.length > 0 && !!selectedItems.value) {
        const value = selectedItems.value
        selectedItems.value = props.options.find((item) => item.id === value.id)
    }

    const summary = computed(() => {
        const value = selectedItems.value

        if (isArray(value)) {
            return value.map((item) => {
                return {
                    ...item,
                    reset: () => (selectedItems.value = value.filter((selectedItems) => selectedItems !== item))
                }
            })
        } else {
            return null
        }
    })

    function getDisplayValue(value: Option | Option[]) {
        if (!!value && !isArray(value)) {
            return value?.label
        }
    }

    watch(_query, () => emit('update:query', _query.value))
</script>
