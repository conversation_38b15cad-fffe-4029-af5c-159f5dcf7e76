<template>
    <span
        class="flex items-center justify-center rounded-full border-2 border-white text-black"
        :class="{
            'h-8 w-8 min-w-[2rem] text-xs': size === 'sm',
            'h-10 w-10 min-w-[2.5rem] text-sm': size === 'base',
            'h-14 w-14 min-w-[3.5rem]': size === 'lg',
            'h-20 w-20 min-w-[5rem] text-2xl': size === 'xl',
            'bg-gold-400': color === 'gold',
            'bg-gray-400': color === 'grey'
        }">
        <span v-if="!visual">{{ nameOrInitials }}</span>
        <img v-else :src="visual" :alt="nameOrInitials" class="h-full w-full rounded-full object-cover" draggable="false">
    </span>
</template>

<script lang="ts" setup>
    import { MaybeRef } from '@vueuse/core'

    interface Props {
        name: MaybeRef<string>
        image?: MaybeRef<string>
        disableInitials?: boolean
        size?: 'sm' | 'base' | 'lg' | 'xl'
        color?: 'gold' | 'grey'
    }

    const props = withDefaults(defineProps<Props>(), {
        size: 'base',
        color: 'grey'
    })

    const visual = computed(() => {
        return unref(props.image) ?? false
    })

    /**
     * Basically from:
     *
     * https://github.com/ogerly/vue-avatar/blob/1faa9ffdc832b7e77a1c6c61dbccbeef14891837/src/Avatar.vue#L10
     */
    const initials = computed(() => {
        const parts = unref(props.name).split(/[ -]/)
        let initials = ''
        const initialsCount = 2
        for (const part of parts) {
            initials += part.charAt(0)
        }
        if (initials.length > initialsCount && initials.search(/[A-Z]/) !== -1) {
            initials = initials.replace(/[a-z]+/g, '')
        }
        initials = initials.substring(0, initialsCount).toUpperCase()
        return initials
    })

    const nameOrInitials = computed(() => (props.disableInitials ? unref(props.name) : unref(initials)))
</script>
