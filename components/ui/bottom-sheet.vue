<template>
    <TransitionRoot :appear="false" :show="isRevealed" as="template">
        <Dialog as="div" class="relative z-50">
            <TransitionChild
                as="div"
                enter="transition-all duration-300 ease-in-out transform"
                enter-from="translate-y-full"
                enter-to="translate-y-0"
                leave="transition-all duration-300 ease-in-out"
                leave-from="translate-y-0 "
                leave-to="translate-y-full"
                class="pointer-events-none fixed inset-y-0 right-0 flex max-w-full overflow-hidden pl-10">
                <DialogPanel class="pointer-events-auto h-screen w-screen max-w-xl">
                    <div class="relative flex h-full flex-col bg-white shadow-xl">
                        <div class="bg-softred-50 z-10 flex items-center justify-between px-6 py-6">
                            <DialogTitle class="text-2xl text-red-500">
                                <slot name="title" />
                            </DialogTitle>

                            <button
                                type="button"
                                title="Panel schließen"
                                @click="emit('cancel')"
                                class="hover:bg-softred-100 ml-3 flex h-8 w-8 items-center justify-center rounded-full text-red-500 transition-colors hover:text-red-600 active:text-red-700">
                                <span class="sr-only">Panel schließen</span>
                                <IconX class="h-7 w-7" aria-hidden="true" />
                            </button>
                        </div>
                        <div class="relative z-0 flex flex-1 flex-col overflow-y-scroll px-6 pt-6">
                            <slot />
                        </div>
                        <div class="flex-0 bg-grey-100 flex justify-end gap-x-4 px-6 py-4">
                            <slot name="footer">
                                <button @click="emit('cancel')" class="form-button button-contained-secondary">Abbrechen</button>
                            </slot>
                        </div>

                        <slot name="bottom-sheet" />
                    </div>
                </DialogPanel>
            </TransitionChild>
        </Dialog>
    </TransitionRoot>
</template>

<script lang="ts" setup>
    import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'

    defineProps<{
        isRevealed: boolean
    }>()

    const emit = defineEmits(['cancel'])
</script>
