<template>
    <div @drop.prevent="onDrop" class="mb-4 h-full border border-dashed border-red-100 p-8 pb-4 text-center">
        <p>
            <span v-if="label">{{ label }}</span
            ><span v-else>Datei auswählen</span>
        </p>
        <p class="text-grey-600 my-4 text-xs">Du kannst Dateien hochladen in diesen Formaten: JPG, JPEG, PDF, PNG, GIF und BMP.<br />Die Dateien sollten nur so groß sein wie nötig, höchstens aber 20 MB.</p>
        <button @click="openSelectDialog" class="form-button button-contained-secondary button-sm inline">Durchsuchen</button>
        <input ref="fileInput" type="file" class="hidden" @change="selectFiles" accept=".jpg,.jpeg,.pdf,.png,.gif,.bmp" />
        <p class="my-4 text-red-500">{{ error }}</p>
    </div>
</template>

<script setup lang="ts">
    type Props = {
        label?: string
    }
    const props = defineProps<Props>()

    const file = ref<any>([])

    const emit = defineEmits(['select'])
    const fileInput = ref(null)
    const error = ref('')

    const MAX_FILE_SIZE = 20 * 1024 * 1024 // 20MB

    function getFiles(f: any) {
        // we allow only one file to be uploaded. On File drop, we select the first file.
        file.value = [...f][0]

        if(validateFileSize(file, MAX_FILE_SIZE) && validateFileType(file)) {
            emit('select', file.value)
        }
    }

    function validateFileSize(file: any, max: number){
        if(file.value.size > max ) {
            error.value = `Die Datei nimmt mehr als ${max / 1024 / 1024} MB ein.`
            return false;
        }
        return true;
    }

    function validateFileType(file: any){
        const fileName = file.value.name;
        const fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1, fileName.length).toLowerCase();
        const allowedExtensions = ['jpg', 'jpeg', 'pdf', 'png', 'gif', 'bmp'];
        if (!allowedExtensions.includes(fileExtension)) {
            error.value = 'Ungültiger Dateityp'
            return false;
        }
        return true;
    }

    function onDrop(e: any) {
        getFiles(e.dataTransfer.files)

    }
    function selectFiles(e: any) {
        getFiles(e.target.files)
    }

    function openSelectDialog() {
        fileInput.value.click()
        error.value = ''
    }

    function preventDefaults(e: any) {
        e.preventDefault()
    }

    const events = ['dragenter', 'dragover', 'dragleave', 'drop']
    onMounted(() => {
        events.forEach((eventName) => {
            document.body.addEventListener(eventName, preventDefaults)
        })
    })

    onUnmounted(() => {
        events.forEach((eventName) => {
            document.body.removeEventListener(eventName, preventDefaults)
        })
    })
</script>
