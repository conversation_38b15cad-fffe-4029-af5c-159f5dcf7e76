<template>
    <li
        class="relative cursor-default select-none overflow-hidden border-b py-2 pl-3 pr-9"
        :class="{ 'bg-grey-100 ': active, 'italic': selected, 'opacity-50': option.disabled }">
        <div class="">
            <div class="text-grey-600 truncate text-sm">{{ description?.value2 ?? '' }}</div>
            <div class="my-1 truncate">{{ extendedDescription }}</div>
            <div class="text-grey-600 truncate text-sm">{{ $formatDateTime(new Date(dateFrom)) }}</div>
            <div class="text-grey-600 truncate text-sm">{{ $formatDateTime(new Date(dateUpTo)) }}</div>
            <div class="text-grey-700 mt-1 truncate text-sm">{{ organisationName }}</div>
        </div>
        <span v-if="selected" class="absolute inset-y-0 right-0 flex items-center pr-4 text-red-600">
            <IconCheck class="h-5 w-5" aria-hidden="true" />
        </span>
    </li>
</template>

<script lang="ts" setup>
    type Props = {
        active: boolean
        option: ExtendedOption<ApiModel<'Event'>>
        selected: boolean
    }

    const props = defineProps<Props>()

    const {
        description,
        extendedDescription,
        dateFrom,
        dateUpTo,
        organisation: { name: organisationName }
    } = props.option.item
</script>
