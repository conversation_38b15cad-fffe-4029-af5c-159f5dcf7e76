<script lang="ts" setup>
    import { Listbox, ListboxButton, ListboxOptions, ListboxOption, ListboxLabel } from '@headlessui/vue'
    import { useVModel } from '@vueuse/core'

    type Props = {
        modelValue: Option | null
        options: Option[]
        datalist?: boolean
        label?: string
        borderless?: boolean
        smallFont?: boolean
    }

    type Emit = {
        (e: 'update:modelValue', value: Props['modelValue']): void
    }

    const props = defineProps<Props>()

    const emit = defineEmits<Emit>()

    const selected = useVModel(props, 'modelValue', emit)

    const inputElement = ref<HTMLElement>(null)
    const optionsWrapper = ref<HTMLElement>(null)

    usePopper(inputElement, optionsWrapper, { placement: 'bottom' })
</script>

<template>
    <Listbox v-model="selected" as="div">
        <ListboxLabel class="text-grey-900 mb-1 text-xs leading-4 empty:hidden">{{ label }}</ListboxLabel>
        <div class="relative" ref="inputElement">
            <ListboxButton
                class="form-input form-input-has-control w-full cursor-default"
                :class="`${borderless ? 'border-0' : ''} ${smallFont ? 'text-xs' : ''}`"
                v-slot="{ open }">
                <span v-if="selected" class="block truncate">{{ selected.label }}</span>
                <span v-else class="input-placeholder block truncate">Bitte auswählen</span>
                <span role="button" class="flex">
                    <IconChevronDown class="form-input-control transition-transform" aria-hidden="true" :class="{ 'rotate-180': open }" />
                </span>
            </ListboxButton>
            <div ref="optionsWrapper" class="absolute z-10 w-full">
                <ListboxOptions
                    id="options-element"
                    as="ul"
                    class="mt-1 w-fit max-h-260 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                    :class="`${smallFont ? 'text-xs' : ''}`">
                    <ListboxOption v-for="option in options" :key="option.id" :value="option" v-slot="{ active, selected }" as="template">
                        <li
                            class="text-grey-800 relative cursor-default select-none py-2 pl-3 pr-9"
                            :class="{ 'bg-grey-100 ': active, 'italic': selected }">
                            <span class="block truncate" v-text="option.label" />
                            <span v-if="selected" class="text-grey-500 absolute inset-y-0 right-0 flex items-center pr-4">
                                <IconCheck class="h-5 w-5" aria-hidden="true" />
                            </span>
                        </li>
                    </ListboxOption>
                </ListboxOptions>
            </div>
        </div>
    </Listbox>
</template>
