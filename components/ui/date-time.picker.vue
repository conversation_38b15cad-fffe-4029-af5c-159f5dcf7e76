<template>
    <div class="flex flex-wrap gap-2">
        <div class="relative">
            <UiDatePicker v-model="date" ref="pickerRef" :disabled="disabled" :min="min" :max="max" @date-change="onDateChange" />
        </div>
        <div v-if="!hideTimeInput" class="relative">
            <UiTimePicker v-model="time" ref="timeRef" :disabled="disabled" @time-selected="$emit('complete')" />
        </div>
    </div>
</template>

<script setup lang="ts">
    import { useDateTimePicker } from '~/composables/ui/datetime-picker'
    import { useVModel } from '@vueuse/core'

    const props = defineProps<{
        modelValue: Date | null
        disabled?: boolean
        min?: Date
        max?: Date
        hideTimeInput?: boolean
    }>()

    const emit = defineEmits<{
        (e: 'update:modelValue', value: Date): any
        (e: 'complete'): void
    }>()

    const model = useVModel(props, 'modelValue', emit)

    const { date, time, timeRef, onDateChange } = useDateTimePicker(model)

    const pickerRef = ref<HTMLInputElement | null>(null)

    const focus = () => {
        pickerRef.value?.focus()
    }

    defineExpose({
        focus
    })
</script>
