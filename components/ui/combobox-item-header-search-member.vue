<template>
    <li
        class="relative cursor-default select-none overflow-hidden border-b py-2 pl-3 pr-9"
        :class="{ 'bg-grey-100 ': active, 'italic': selected, 'opacity-50': option.disabled }">
        <div class="grid grid-cols-10">
            <div class="col-span-9">
                <div class="truncate">{{ firstname }} {{ lastname }}</div>
                <div class="text-grey-600 truncate">{{ organisationName }}</div>
            </div>
            <div>
                <UiAvatar :image="avatar" :name="`${firstname} ${lastname}`" />
            </div>
        </div>
        <span v-if="selected" class="absolute inset-y-0 right-0 flex items-center pr-4 text-red-600">
            <IconCheck class="h-5 w-5" aria-hidden="true" />
        </span>
    </li>
</template>

<script lang="ts" setup>
    type Props = {
        active: boolean
        option: ExtendedOption<ApiModel<'MemberData'>>
        selected: boolean
    }

    const props = defineProps<Props>()

    const {
        id,
        firstname,
        lastname,
        leadingOrganisation: { name: organisationName }
    } = props.option.item

    const { data: avatar, getMemberProfileImage } = useGetMemberProfileImage()
    getMemberProfileImage(id)
</script>
