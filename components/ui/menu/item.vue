<template>
    <MenuItem
        :as="as"
        v-bind="$attrs"
        v-slot="{ close }"
        class="text-grey-500 ui-active:bg-grey-50 flex w-full items-center rounded-md px-2 py-1.5 text-left text-sm transition-colors">
        <UiIcon v-if="icon" :name="icon" class="mr-2 inline h-5 w-5 flex-none text-sky-500"/>
        <slot v-bind="{ close }" />
    </MenuItem>
</template>

<script lang="ts" setup>
    import { MenuItem } from '@headlessui/vue'
    import { UiIconName } from '~~/modules/ui'

    withDefaults(
        defineProps<{
            as?: string
            icon?: UiIconName
        }>(),
        {
            as: 'button'
        }
    )
</script>
