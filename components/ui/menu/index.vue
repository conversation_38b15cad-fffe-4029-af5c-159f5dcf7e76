<template>
    <Menu as="menu" class="inline-block" @click.stop v-slot="{ close, open }">
        <span ref="reference">
            <MenuButton
                @click.stop="updatePosition"
                :class="[
                    {
                        'menu-button': !withoutDefaultCss,
                        'button-xs': buttonSize === 'xs',
                        'button-sm': buttonSize === 'sm',
                        'button-md': buttonSize === 'md'
                    },
                    buttonStyle
                ]"
            >
                <slot name="button" v-bind="{ close, open }">
                    <IconDotsVertical class="h-5 w-5" aria-hidden="true" />
                </slot>
            </MenuButton>
        </span>

        <Transition
            enter-active-class="transition duration-100 ease-out"
            enter-from-class="transform scale-95 opacity-0"
            enter-to-class="transform scale-100 opacity-100"
            leave-active-class="transition duration-75 ease-in"
            leave-from-class="transform scale-100 opacity-100"
            leave-to-class="transform scale-95 opacity-0">
            <!-- Disabled now: unmount="false" is important because otherwise we can't encapsulate actions that need confirmations -->
            <div ref="flyout" class="z-10">
                <MenuItems as="div" class="menu-flyout">
                    <slot v-bind="{ close, open }" />
                </MenuItems>
            </div>
        </Transition>
    </Menu>
</template>

<script lang="ts" setup>
    import { Menu, MenuButton, MenuItems } from '@headlessui/vue'

    type Props = {
        buttonSize?: 'xs' | 'sm' | 'md'
        buttonStyle?: string,
        withoutDefaultCss?: boolean
    }

    withDefaults(defineProps<Props>(), {
        buttonSize: 'sm'
    })

    const reference = ref()
    const flyout = ref()

    const popper = usePopper(reference, flyout, {
        placement: 'bottom-end',
        modifiers: [
            {
                name: 'offset',
                options: {
                    offset: [2, 2]
                }
            }
        ]
    })

    async function updatePosition() {
        await popper.value?.update()
    }
</script>

<style lang="pcss" scoped>
    .menu-button {
        @apply form-button inline-flex items-center gap-[0.25em] rounded-md p-1 transition-all
    }

    .menu-button[data-headlessui-state~="open"] {
        @apply bg-blue-50 shadow-inner
    }

    .menu-flyout {
        @apply text-grey-500 z-50 w-56 rounded-md bg-white p-1 font-medium shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none
    }
</style>
