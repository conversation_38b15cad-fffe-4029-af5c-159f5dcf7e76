<script lang="ts" setup>
    import { useVModel } from '@vueuse/core'
    import { uniqueId } from 'lodash-es'

    type Props = {
        modelValue: boolean
        label: string
        circle?: boolean
        name?: string
        disabled?: boolean
    }

    type Emit = {
        (e: 'update:modelValue', value: boolean): void
    }

    const id = uniqueId('checkbox-')

    const props = defineProps<Props>()

    const emit = defineEmits<Emit>()

    const value = useVModel(props, 'modelValue', emit)
</script>
<template>
    <div v-if="circle" class="flex items-start w-full">
        <div class="flex items-center h-5 align-center">
            <input
                v-model="value"
                :id="id"
                :aria-describedby="`${id}-description`"
                :name="name"
                :disabled="disabled"
                type="checkbox"
                :class="{ 'icon-check-white': value }"
                class="w-4 h-4 rounded-full appearance-none outline-grey-400 checked:outline-softred-500 checked:bg-softred-500 outline outline-1 hover:cursor-pointer disabled:cursor-default disabled:icon-minus-white disabled:outline-softred-200 disabled:bg-softred-200" />
        </div>
        <div class="w-full ml-2 text-sm text-grey-900">
            <label :for="id" class="w-full text-sm font-medium cursor-pointer">{{ label }}</label>
            <p :id="`${id}-description`" class="text-xs font-light">
                <span class="sr-only">{{ label }}</span>
                <slot></slot>
            </p>
        </div>
    </div>

    <div v-else class="flex items-start">
        <div class="flex items-center h-5">
            <input
                v-model="value"
                :id="id" :aria-describedby="`${id}-description`"
                :name="name"
                type="checkbox"
                :disabled="disabled"
                class="form-checkbox disabled:cursor-default disabled:outline-gray-100 disabled:border-gray-200 disabled:bg-gray-100 disabled:c"
            />
        </div>
        <div class="w-full ml-2 text-sm">
            <label :for="id" class="text-sm font-medium cursor-pointer text-grey-900">{{ label }}</label>
            <p :id="`${id}-description`" class="text-xs font-light text-grey-900">
                <span class="sr-only">{{ label }}</span>
                <slot></slot>
            </p>
        </div>
    </div>
</template>
