<template>
    <input type="time" @keydown.space.prevent="" v-model="timeProxy" class="form-input h-10" :min="stringify(min)" :max="stringify(max)" :step="step * 60" />
</template>

<script lang="ts" setup>
    import { useVModel } from '@vueuse/core'
    import { DateTime } from 'luxon'

    const props = withDefaults(
        defineProps<{
            modelValue: Date
            forcedDay?: Date
            min?: Date
            max?: Date
            step?: 1 | 5 | 15
        }>(),
        {
            step: 5
        }
    )

    const emit = defineEmits<{
        (event: 'update:modelValue', value: Date): any
    }>()

    const model = useVModel(props, 'modelValue', emit)

    function stringify(value: Date) {
        if (!value) {
            return null
        }

        return DateTime.fromJSDate(value).toFormat('HH:mm')
    }

    const timeProxy = computed({
        get() {
            if (!model.value) {
                return null
            }

            return DateTime.fromJSDate(model.value).toFormat('HH:mm')
        },
        set(value) {
            if (!value) {
                model.value = null
            }

            const dateTime =
                props.forcedDay instanceof Date
                    ? //...
                      DateTime.fromJSDate(props.forcedDay)
                    : DateTime.now()

            const { minute, hour } = DateTime.fromFormat(value, 'HH:mm').toObject()
            model.value = dateTime.set({ hour, minute }).toJSDate()
        }
    })
</script>

<style lang="pcss" scoped>
    input[type=time]::-webkit-calendar-picker-indicator,
    input[type=time]::-webkit-inner-spin-button {
        display: none;
        -webkit-appearance: none;
        appearance: none;
    }

    /* input[type=time] {
        @apply icon-clock-gray-300 bg-[length:1.25rem_1.25rem] bg-[left_.25rem_center] bg-no-repeat pl-[2rem];
        &:focus {
            @apply icon-clock-blue-400
        }
    } */
</style>
