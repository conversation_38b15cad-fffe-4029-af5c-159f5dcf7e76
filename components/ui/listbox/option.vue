<template>
    <ComboboxOption v-slot="{ active, selected }" as="li" class="listbox-option">
        <slot v-bind="{ active, selectedItems: selected }" />
        <IconCheck aria-hidden="true" class="listbox-option-indicator" />
    </ComboboxOption>
</template>

<script lang="ts" setup>
    import { ListboxOption, ComboboxOption } from '@headlessui/vue'
</script>

<style lang="pcss" scoped>
    .listbox-option {
        @apply text-grey-800 relative cursor-default select-none py-2 pl-3 pr-9 leading-tight font-light first-of-type:rounded-t-md last:rounded-b-md;
    }

    .listbox-option[data-headlessui-state~=active] {
        @apply bg-yellow-50
    }

    .listbox-option[data-headlessui-state~=selected] {
        @apply underline decoration-blue-500
    }

    .listbox-option-indicator {
        @apply invisible text-grey-500 absolute right-2 top-2 h-5 w-5
    }

    .listbox-option[data-headlessui-state~=selected] > .listbox-option-indicator {
        @apply visible
    }
</style>
