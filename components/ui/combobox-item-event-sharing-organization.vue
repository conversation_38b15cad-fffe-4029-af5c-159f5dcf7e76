<template>
    <li
        @click.stop="tryRemoveSelf"
        class="text-grey-800 relative cursor-default select-none py-2 pl-3 pr-9"
        :class="[
            { 'bg-grey-100 ': active, 'italic': selected, 'opacity-50': option.disabled },
            option.item.depth === 0 ? 'border-t border-gray-100 pt-2' : '',
            option.item.depth === 1 ? (option.disabled ? '' : 'ml-2') : '',
            option.item.depth === 2 ? (option.disabled ? 'ml-2' : 'ml-4') : '',
            option.item.depth === 3 ? (option.disabled ? 'ml-4' : 'ml-6') : '',
            option.item.depth === 4 ? (option.disabled ? 'ml-6' : 'ml-8') : '',
            !option.disabled ? 'text-sm leading-[1.43]' : 'text-[10px] uppercase leading-[1.2]'
        ]">
        <span class="block truncate">{{ option.disabled && option.item.depth !== 0 ? '›&nbsp;&nbsp;' : '' }}{{ name }}</span>
        <span v-if="selected" class="absolute inset-y-0 right-0 flex items-center pr-4 text-blue-700">
            <IconCheck class="h-5 w-5" aria-hidden="true" />
        </span>
    </li>
</template>

<script setup lang="ts">
    import { ExtractedOrganization } from '~/components/event/sharing/community-search.panel.vue'

    type Props = {
        active: boolean
        option: ExtendedOption<ExtractedOrganization>
        selected: boolean
        modelValue: ExtractedOrganization[]
    }

    const props = defineProps<Props>()

    const emit = defineEmits<{ (e: 'update:modelValue', value: Props['modelValue']): void }>()

    const name = computed(() => props.option.item.value.name)

    function tryRemoveSelf() {
        if (props.selected) {
            setTimeout(() => {
                emit(
                    'update:modelValue',
                    props.modelValue.filter((selected) => selected.id !== props.option.item.id)
                )
            }, 0) //This might seem a bit hacky but HeadlessUI Combobox tries really hard to overwrite changes coming from inside the options. This makes sure that events fire in correct order
        }
    }
</script>
