<template>
    <div>
        <Combobox as="div" v-model="selectedItems" :multiple="isMultiple">
            <ComboboxLabel v-if="label" class="text-grey-900 mb-1 text-xs leading-4 empty:hidden">{{ label }}</ComboboxLabel>
            <div class="relative" ref="inputElement">
                <ComboboxInput
                    class="form-input form-input-has-control w-full pr-16"
                    autocomplete="off"
                    :placeholder="placeholder"
                    :display-value="getDisplayValue"
                    :disabled="disabled"
                    type="text"
                    @change="query = $event.target.value" />

                <ComboboxButton as="button">
                    <IconRefresh v-if="isLoading" class="h-5 w-5 animate-spin text-blue-700" />
                    <IconXCircle v-if="selectedItems && !isMultiple && !clearAfterSelecting" class="text-grey-400 hover:text-grey-600 mr-1 h-6 w-6" @click.stop="clear" />
                    <IconChevronDown class="form-input-control" aria-hidden="true" />
                </ComboboxButton>

                <div ref="optionsElement" class="absolute left-0 right-0 z-10 w-full">
                    <ComboboxOptions
                        as="ul"
                        class="mt-1 max-h-60 overflow-auto rounded-md bg-white text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                        <div v-if="isError">
                            <div class="text-softred-800 bg-softred-50 bg-opacity-100 py-2 text-center">
                                <p>Beim Herunterladen der Daten ist ein Problem aufgetreten.</p>
                            </div>
                        </div>

                        <div v-else>
                            <ComboboxOption
                                v-for="option in options"
                                :key="option.id"
                                :disabled="option.disabled"
                                :value="option"
                                v-slot="{ active }"
                                @click="handleSelected(option)"
                                as="template">
                                <slot
                                    name="item"
                                    :selected="selectedItemsArray.map((selected) => selected.id).includes(option.id)"
                                    :active="active"
                                    :option="option">
                                    <UiComboboxItemDefault
                                        :active="active"
                                        :selected="selectedItemsArray.map((selected) => selected.id).includes(option.id)"
                                        :option="option" />
                                </slot>
                            </ComboboxOption>

                            <div v-if="!options.length && !isLoading" class="text-grey-400 px-3 py-2 flex items-center gap-2">
                                <template v-if="!!query">
                                    <UiIcon name="ban" class="inline-block w-4 h-4" />
                                    Keine Vorschläge für <span class="text-grey-600 italic">{{ query }}</span>
                                </template>
                                <template v-else> <UiIcon name="ban" class="inline-block w-4 h-4" /> Keine Vorschläge </template>
                            </div>
                        </div>
                    </ComboboxOptions>
                </div>
            </div>
            <div v-if="summary && ((showSummary === null && isMultiple) || showSummary)" class="mt-2 flex w-full flex-wrap gap-2 text-xs">
                <span v-if="!summary.length" class="text-grey-900 border-grey-200 rounded-sm border px-2 py-1">Nichts ausgewählt</span>

                <UiLabel
                    v-else
                    v-for="{ id, label, reset } in summary"
                    :remove="summary.length > minValueLength ? reset : null"
                    :key="id"
                    :title="label">
                    {{ label }}
                </UiLabel>
            </div>
        </Combobox>
    </div>
</template>

<script lang="ts" setup>
    import { Combobox, ComboboxButton, ComboboxInput, ComboboxLabel, ComboboxOption, ComboboxOptions } from '@headlessui/vue'
    import { debounce } from 'lodash-es'

    // ModelType is set to any due to complex nature of making this generic type
    // Adjust when time allows. Link that might be useful below
    // https://logaretm.com/blog/generic-type-components-with-composition-api/
    type ModelType = any

    type Props = {
        modelValue: ModelType | null
        label?: string
        placeholder?: string
        showSummary?: null | boolean
        disabled?: boolean
        limit?: number
        delayMs?: number
        lettersThreshold?: number
        minValueLength?: number
        clearAfterSelecting?: boolean
        getOptionLabel: (option: ModelType) => string
        getOptionDisabled?: (option: ModelType) => boolean
        // mutation: () => ReturnType<typeof useMutation<ModelType[], unknown, QueryVariablesType>>
        mutation: () => any,
        initialLoading?: boolean
    }

    type Emit = {
        (e: 'update:modelValue', value: Props['modelValue']): void,
        (e: 'update:onSelectedItem'): void
    }

    const props = withDefaults(defineProps<Props>(), {
        showSummary: null,
        disabled: false,
        limit: 10,
        delayMs: 350,
        lettersThreshold: 1,
        minValueLength: 0,
        placeholder: 'Für Vorschläge tippen',
        getOptionDisabled: () => false,
        initialLoading: true,
        clearAfterSelecting: false
    })

    const emit = defineEmits<Emit>()

    const inputElement = ref<HTMLElement>(null)
    const optionsElement = ref<HTMLElement>(null)

    usePopper(inputElement, optionsElement, { placement: 'bottom' })

    const getOption = (item: ModelType) => ({
        id: item.id,
        label: props.getOptionLabel(item),
        disabled: props.getOptionDisabled ? props.getOptionDisabled(item) : false,
        item: item
    })

    const generateOptions = (items: ModelType[]) => {
        return Array.isArray(items) ? items.map((item) => getOption(item)) : getOption(items)
    }

    const selectedItems = computed({
        get() {
            return props.modelValue !== null ? generateOptions(props.modelValue) : null
        },
        set(value: ExtendedOption<any>[] | ExtendedOption<any>) {
            if (!value) emit('update:modelValue', null)
            else {
                if (Array.isArray(value)) {
                    value = value.filter((current, index, all) => all.findIndex((any) => any.id === current.id) === index)
                    emit(
                        'update:modelValue',
                        value.map((opt) => opt.item)
                    )
                } else emit('update:modelValue', value.item)
            }
        }
    })

    const selectedItemsArray = computed(() => {
        if (selectedItems.value === null) return []
        return Array.isArray(selectedItems.value) ? selectedItems.value : [selectedItems.value]
    })

    function handleSelected(item: ModelType){
        const exist = selectedItemsArray.value.find(ele => ele.id == item.id)
        if(exist && Array.isArray(selectedItems.value)) {
            selectedItems.value = selectedItems.value.filter(element => element.id !== item.id )
        }
    }

    watch(selectedItemsArray, ()=>{
        emit('update:onSelectedItem')
        if(selectedItems.value && props.clearAfterSelecting) {
            clear()
        }
    })

    const query = ref('')

    const isMultiple = computed(() => {
        return !!Array.isArray(props.modelValue)
    })

    const { mutate, data, isLoading, isError } = props.mutation()
    if (!!props.initialLoading) {
      mutate({ query: query.value, limit: props.limit })
    }

    const options = computed((): ExtendedOption<any>[] => {
        return unref(data)?.map((item) => getOption(item)).sort( (a,b) => {

            const firstDate =new Date(a.item.dateFrom).getTime()
            const secondDate = new Date(b.item.dateFrom).getTime()

            if(firstDate > secondDate) return -1
            else if (firstDate < secondDate) return 1
            return 0
        }) ?? []
    })

    const summary = computed(() => {
        const value = selectedItems?.value

        if (Array.isArray(value)) {
            return value.map((item) => {
                return {
                    ...item,
                    reset: () => (selectedItems.value = value.filter((selectedItems) => selectedItems !== item))
                }
            })
        } else {
            return null
        }
    })

    function getDisplayValue(value: Option | Option[]) {
        if (Boolean(value) && !Array.isArray(value)) {
            return value?.label
        } else {
            return query.value
        }
    }

    function getData() {
        mutate({ query: query.value, limit: props.limit })
    }

    const debounceGetData = debounce(getData, props.delayMs)

    watch(query, () => {
        if (query.value.length > props.lettersThreshold - 1 || query.value.length === 0) {
            debounceGetData()
        }
    })

    function clear() {
        selectedItems.value = Array.isArray(selectedItems.value) ? [] : null
        query.value = ''
    }

    watch(options, () => {
        if (options.value?.length > 0 && selectedItems.value !== null) {
            if (!Array.isArray(selectedItems.value)) {
                const optionIds = options.value.map((option) => option.id)
                selectedItems.value = optionIds.includes(selectedItems.value.id) ? selectedItems.value : null
            }
        }
    })
</script>
