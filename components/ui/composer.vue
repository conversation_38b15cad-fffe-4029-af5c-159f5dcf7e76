<script lang="ts" setup>
    import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
    import useDialogs from '~/composables/dialogs'

    type Props = {
        isRevealed: boolean
        isBackgroundVisible?: boolean
        emit?: (e: 'cancel') => any
    }

    const myEmit = defineEmits(['cancel'])

    const props = defineProps<Props>()
    const closeButton = ref(null)

    function close() {
        if (props.emit) {
            props.emit('cancel')
        } else {
            myEmit('cancel')
        }
    }

    // State, used to track whether this is the topmost dialog
    const dialogs = useDialogs()
    const dialogId = dialogs.newId()

    const isTopmostDialog = computed(() => {
        return dialogs.topmostDialog.value === dialogId
    })

    if (props.isRevealed) {
        dialogs.onOpen(dialogId)
    }

    watch(
        () => props.isRevealed,
        (current, previous) => {
            // When this changes, either we opened or closed the dialog.
            let opened = previous === false && current === true
            let closed = !opened

            if (opened) dialogs.onOpen(dialogId)
            if (closed) dialogs.onClose(dialogId)
        }
    )

    // Hack to ensure focus is set after transitions complete
    const handleAfterEnter = () => {
        setTimeout(() => {
            closeButton.value?.focus()
        }, 300)
    }
</script>

<template>
    <TransitionRoot :show="isRevealed && isTopmostDialog" as="template" @after-enter="handleAfterEnter">
        <TransitionChild>
            <Dialog :initial-focus="closeButton" as="div" class="relative z-50" @close="close">
                <UiOverlayBackdrop v-if="!isBackgroundVisible" />
                <TransitionChild
                    as="div"
                    enter="transition-all duration-300 ease-in-out transform"
                    enter-from="translate-x-full"
                    enter-to="translate-x-0"
                    leave="transition-all duration-300 ease-in-out"
                    leave-from="translate-x-0 "
                    leave-to="translate-x-full"
                    class="h-dvh pointer-events-none fixed inset-y-0 right-0 flex max-w-full overflow-hidden pl-0 lg:pl-10">
                    <DialogPanel class="h-dvh pointer-events-auto h-full w-screen max-w-xl">
                        <div class="relative flex h-full flex-col bg-white shadow-xl">
                            <div class="bg-softred-50 z-10 flex items-center justify-between px-6 py-6">
                                <DialogTitle class="text-2xl text-red-500">
                                    <slot name="title" />
                                </DialogTitle>
                                <button
                                    type="button"
                                    title="Panel schließen"
                                    @click="close"
                                    ref="closeButton"
                                    tabindex="0"
                                    class="hover:bg-softred-100 ml-3 flex h-8 w-8 items-center justify-center rounded-full text-red-500 transition-colors hover:text-red-600 active:text-red-700">
                                    <span class="sr-only">Panel schließen</span>
                                    <IconX class="h-7 w-7" aria-hidden="true" />
                                </button>
                            </div>
                            <div class="relative z-0 flex flex-1 flex-col overflow-y-auto px-6 pt-6 md:overflow-y-scroll">
                                <slot />
                            </div>
                            <div class="flex-0 bg-grey-100 flex justify-end gap-x-4 px-6 py-4">
                                <slot name="footer">
                                    <button @click="close" class="form-button button-contained-secondary">Abbrechen</button>
                                </slot>
                            </div>

                            <slot name="bottom-sheet" />
                        </div>
                    </DialogPanel>
                </TransitionChild>
            </Dialog>
        </TransitionChild>
    </TransitionRoot>
</template>
