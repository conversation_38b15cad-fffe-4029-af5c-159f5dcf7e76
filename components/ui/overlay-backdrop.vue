<template>
    <Teleport to="body">
        <TransitionChild
            as="div"
            class="fixed inset-0 z-40 bg-gray-400 mix-blend-multiply"
            enter="ease-out duration-300"
            enter-from="opacity-0"
            enter-to="opacity-100"
            leave="ease-in duration-150"
            leave-from="opacity-100"
            leave-to="opacity-0" />
    </Teleport>
</template>

<script lang="ts" setup>
    import { TransitionChild } from '@headlessui/vue'
    // This component should used inside <TransitionRoot /> component
    // This component needs to be teleported to body for mix-blend-multiply to behave correctly
</script>
