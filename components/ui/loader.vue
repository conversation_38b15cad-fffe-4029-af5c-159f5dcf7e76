<template>
    <div class="relative h-full w-full sm:max-w-screen sm:px-2 md:max-w-100% md:px-0">
        <div v-if="isLoading" class="absolute z-10 grid h-full w-full place-items-center bg-blue-300 bg-opacity-100 opacity-60"></div>
        <IconRefresh v-if="isLoading" class="absolute top-[calc(50%-1rem)] left-[calc(50%-1rem)] z-20 h-8 w-8 animate-spin text-blue-700" />
        <slot></slot>
    </div>
</template>

<script lang="ts" setup>
    type Props = {
        isLoading: boolean
    }

    const props = defineProps<Props>()
</script>
