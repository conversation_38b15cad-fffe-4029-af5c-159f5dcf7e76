<script lang="ts" setup>
    import { UiIconName } from '~~/modules/ui'

    defineProps<{
        icon?: UiIconName
    }>()
</script>

<template>
    <button class="flex cursor-pointer items-center gap-2 py-2 px-3 text-left transition-colors hover:bg-blue-50 active:bg-blue-100">
        <UiIcon v-if="icon" :name="icon" class="flex h-6 w-6 min-w-[1.5rem] text-center text-sky-500" />
        <slot />
    </button>
</template>
