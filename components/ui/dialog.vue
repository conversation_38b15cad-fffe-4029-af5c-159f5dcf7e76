<template>
    <TransitionRoot :appear="true" :show="isRevealed" as="template">
        <Dialog as="div" class="relative z-50">
            <TransitionChild
                as="template"
                enter="duration-300 ease-out"
                enter-from="opacity-0"
                enter-to="opacity-100"
                leave="duration-200 ease-in"
                leave-from="opacity-100"
                leave-to="opacity-0">
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div
                    class="flex min-h-full items-center justify-center text-center"
                    :class="{'p-4' : !noPaddingAndMargin}"
                >
                    <TransitionChild
                        as="template"
                        enter="duration-300 ease-out"
                        enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100"
                        leave="duration-200 ease-in"
                        leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95">
                        <DialogPanel
                            class="w-full max-w-lg transform rounded-md bg-white text-left shadow-xl transition-all"
                            :class="[
                                !allowOverflow && 'overflow-hidden',
                                !noPaddingAndMargin && 'p-6',
                                !noPaddingAndMargin && 'm-3 md:m-0'
                            ]">
                            <DialogTitle
                                as="h3"
                                class="text-lg font-semibold leading-6 text-red-500"
                                :class="{'mb-6' : !noPaddingAndMargin}"
                                >
                                {{ title }}
                            </DialogTitle>
                            <div
                                class="text-grey-500 text-sm"
                                :class="{'mt-2' : !noPaddingAndMargin}"
                            >
                                <slot />
                            </div>
                            <div
                                class="flex justify-end space-x-2"
                                :class="{'mt-4' : !noPaddingAndMargin}"
                            >
                                <slot name="buttons" />
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
    import { TransitionRoot, TransitionChild, Dialog, DialogPanel, DialogTitle } from '@headlessui/vue'

    type Props = {
        isRevealed: boolean
        title?: string
        allowOverflow?: boolean
        noPaddingAndMargin?: boolean
    }

    withDefaults(defineProps<Props>(), {noPaddingAndMargin: false})
</script>
