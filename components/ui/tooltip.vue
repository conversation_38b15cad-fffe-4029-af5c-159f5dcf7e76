<template>
    <Popover>
        <PopoverButton as="button" class="relative">
            <slot name="button">
                <IconInformationCircle class="ml-1 h-5 w-5 text-sky-500 hover:cursor-pointer" />
            </slot>
        </PopoverButton>

        <transition
            enter-active-class="transition duration-200 ease-out"
            enter-from-class="translate-y-1 opacity-0"
            enter-to-class="translate-y-0 opacity-100"
            leave-active-class="transition duration-150 ease-in"
            leave-from-class="translate-y-0 opacity-100"
            leave-to-class="translate-y-1 opacity-0">
            <PopoverPanel as="div" class="absolute z-10 origin-top-right" v-slot="{ close }">
                <div class="border-100-gray md:min-w-[240px] md:max-w-[280px] whitespace-break-spaces rounded border bg-white p-2 shadow" @click="close">
                    <div class="mt-1 text-xs text-gray-500">
                        <slot>
                            {{ content }}
                        </slot>
                    </div>
                </div>
            </PopoverPanel>
        </transition>
    </Popover>
</template>

<script lang="ts" setup>
    import { Popover, PopoverButton, PopoverPanel } from '@headlessui/vue'

    type Props = {
        content?: string
    }

    defineProps<Props>()
</script>
