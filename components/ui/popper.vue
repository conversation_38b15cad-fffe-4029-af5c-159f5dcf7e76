<template>
    <div
        class="group"
        @mouseenter="showPopperHandler"
        @mouseleave="hidePopperHandler"
        @click.stop="togglePopperHandler">
            <slot>

            </slot>
            <div v-if="showPopper"
                class="pointer-events-none absolute z-10 rounded-lg bg-white mt-4 px-3 py-2 text-sm text-gray-900 opacity-0 shadow-xl transition-opacity group-hover:opacity-100"
                :class="{'hidden': !popperIsShown,'inline-block': popperIsShown }"
                ref="tooltipElement">
                <div v-text="text"></div>
            </div>
    </div>
</template>

<script lang="ts" setup>

    const props = withDefaults(
        defineProps<{
            text: string
            showPopper?: boolean
        }>(),
        {
            showPopper: true
        }
    )

    const popperElement = ref<HTMLElement>(null)
    const tooltipElement = ref<HTMLElement>(null)
    const popperIsShown = ref(true)

    const popperInstance  = usePopper(popperElement, tooltipElement, { placement: 'auto' })

    const togglePopperHandler = () => {
        if(popperIsShown.value) {
        hidePopperHandler()
        } else {
        showPopperHandler()
        }
    }

    const showPopperHandler = () => {
        popperIsShown.value = true
        popperInstance.value.update()
    }

    const hidePopperHandler = () => popperIsShown.value = false
</script>
