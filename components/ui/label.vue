<template>
    <div
        class="text-grey-700 bg-grey-100 mr-2 flex cursor-default select-none items-center whitespace-nowrap rounded-full py-1 pl-2"
        :class="{
            'pointer-events-none opacity-50': disabled,
            'hover:cursor-pointer': clickable,
            'pr-1': !!remove,
            'pr-2': !remove,
            'bg-grey-500 border-grey-500 hover:bg-grey-500 text-white ': isActive,
            'border-grey-50 border bg-white shadow hover:border-sky-500 hover:bg-white': mode === 'light',
            '!text-grey-700 border-sky-500': isActive && mode === 'light',
            'border-softred-200 hover:bg-softred-200 !text-grey-800 border bg-white shadow': mode === 'warning',
            'bg-softred-200': isActive && mode === 'warning'
        }">
        <div class="max-w-[12rem] flex-1 overflow-hidden truncate">
            <span :class="{ 'text-xs': size === 'xs', 'text-sm': size === 'sm' }">
                <slot>{{ content }}</slot>
            </span>
        </div>
        <button v-if="remove" @click="remove" class="flex-0 hover:bg-grey-200 active:bg-grey-300 flex items-center rounded-full px-0.5">
            <IconX class="text-grey-700 inline h-4 w-4" />
        </button>
    </div>
</template>

<script lang="ts" setup>
    type Props = {
        size?: 'xs' | 'sm'
        content?: string
        isActive?: boolean
        disabled?: boolean
        clickable?: boolean
        mode?: 'light' | 'warning'
        remove?: () => void
    }

    const props = defineProps<Props>()
</script>
