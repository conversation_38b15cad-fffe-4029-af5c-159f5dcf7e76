<template>
    <form ref="form" @submit.prevent.stop="emit('select', { start, end })" class="flex flex-col sm:flex-row w-full sm:items-center gap-2" v-bind="$attrs">
        <UiListbox
            v-if="days.length < 8"
            :model-value="selectedDay"
            @update:model-value="setSelectedDay"
            :disabled="!days.length"
            class="flex-shrink-none">
            <template #button="{ value }">
                <span>{{ $formatDate(new Date(`${value}`), 'short') }}</span>
            </template>
            <UiListboxOption v-for="day in days" :key="day.toISOString()" :value="day">
                {{ $formatDate(day) }}
            </UiListboxOption>
        </UiListbox>

        <UiDateInput v-else :model-value="selectedDay" @update:model-value="setSelectedDay" :min="boundaries.start" :max="boundaries.end" />

        <UiIntervalInput v-model:start="start" v-model:end="end" :boundaries="boundariesOfSelectedDay" class="flex flex-col sm:flex-row sm:items-center  gap-2">
            <template v-slot="{ startInputAttrs, endInputAttrs }">
                <span class="hidden sm:block font-light"> , </span>
                <span class="block sm:hidden font-light"> von </span>
                <UiTimeInput :step='1' v-bind="startInputAttrs" :forced-day="selectedDay" class="w-full" />
                <span class="font-light"> bis</span>
                <UiTimeInput :step='1' v-bind="endInputAttrs" :forced-day="selectedDay" class="w-full" />
            </template>
        </UiIntervalInput>

        <button
            type="submit"
            :disabled="validator.$invalid"
            class="form-button button-outline flex-1 items-center rounded-sm pt-2="
            :class="{ 'animate-pulse': !validator.$invalid }">
            <UiIcon name="plus" class="h-5 w-5" />
            <span class="truncate">Hinzufügen</span>
        </button>
    </form>
</template>

<script lang="ts" setup>
    import useVuelidate from '@vuelidate/core'
    import { required } from '@vuelidate/validators'
    import { DateTime } from 'luxon'

    const props = defineProps<{
        boundaries: { start: Date; end: Date }
    }>()

    const emit = defineEmits<{
        (event: 'select', value: { start: Date; end: Date })
    }>()


    const days = computed(() => {
        return daysOfInterval(props.boundaries)
    })

    const selectedDay = ref<Date>(days.value.at(0))

    const boundariesOfSelectedDay = computed(() => {
        const other = toIntervalObject({
            start: DateTime.fromJSDate(selectedDay.value).startOf('day'),
            end: DateTime.fromJSDate(selectedDay.value).endOf('day')
        })

        const { start, end } = toIntervalObject(props.boundaries).intersection(other)

        return {
            start: start.toJSDate(),
            end: end.toJSDate()
        }
    })

    const start = ref<Date>(boundariesOfSelectedDay.value.start)
    const end = ref<Date>(boundariesOfSelectedDay.value.end)

    function setSelectedDay(value: Date) {
        selectedDay.value = value
        start.value = boundariesOfSelectedDay.value.start
        end.value = boundariesOfSelectedDay.value.end
    }

    const interval = computed(() => {
        return {
            start: start.value,
            end: end.value
        }
    })

    const validator = useVuelidate(
        {
            start: {
                required
            },
            end: {
                required
            }
        },
        interval
    )
</script>
