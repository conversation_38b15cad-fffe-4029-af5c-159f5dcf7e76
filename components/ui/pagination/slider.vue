<template>
    <div class="flex justify-center gap-x-1">
        <button
            title="Erste Seite"
            :disabled="!showFirst"
            @click="() => (currentPage = 1)"
            class="flex h-8 w-8 items-center justify-center rounded-full bg-white text-sky-500 hover:bg-blue-50 hover:text-sky-700 active:bg-blue-100 disabled:pointer-events-none disabled:text-sky-50">
            <IconChevronDoubleLeft class="h-auto w-4" />
        </button>

        <button
            :disabled="!hasPrev"
            @click="() => (currentPage = currentPage - 1)"
            class="flex h-8 w-8 items-center justify-center rounded-full bg-white text-sky-500 hover:bg-blue-50 hover:text-sky-700 active:bg-blue-100 disabled:pointer-events-none disabled:text-sky-50">
            <IconChevronLeft class="h-auto w-4" />
        </button>

        <TransitionGroup name="list">
            <button
                v-for="page in slider"
                :key="`page-${page}`"
                @click="() => (currentPage = page)"
                :aria-selected="page === currentPage"
                class="flex h-8 w-8 items-center justify-center rounded-full bg-white text-sm text-sky-500 hover:bg-blue-50 hover:font-semibold hover:text-sky-700 active:bg-blue-100 aria-selected:pointer-events-none aria-selected:bg-sky-500 aria-selected:text-white">
                {{ page }}
            </button>
        </TransitionGroup>

        <button
            :disabled="!hasNext"
            @click="() => (currentPage = currentPage + 1)"
            class="flex h-8 w-8 items-center justify-center rounded-full bg-white text-sky-500 hover:bg-blue-50 hover:text-sky-700 active:bg-blue-100 disabled:pointer-events-none disabled:text-sky-50">
            <IconChevronRight class="h-auto w-4" />
        </button>

        <button
            :disabled="!showLast"
            :title="`Seite ${pageCount}`"
            @click="() => (currentPage = pageCount)"
            class="flex h-8 w-8 items-center justify-center rounded-full bg-white text-sky-500 hover:bg-blue-50 hover:text-sky-700 active:bg-blue-100 disabled:pointer-events-none disabled:text-sky-50">
            <IconChevronDoubleRight class="h-auto w-4" />
        </button>
    </div>
</template>

<script lang="ts" setup>
    import { useVModel } from '@vueuse/core'

    type Props = {
        modelValue: number
        pageCount: number
    }

    type Emit = {
        (e: 'update:modelValue', value: Props['modelValue']): void
    }

    const props = defineProps<Props>()
    const emit = defineEmits<Emit>()

    const currentPage = useVModel(props, 'modelValue', emit)

    const { slider, showFirst, hasPrev, showLast, hasNext } = usePaginatorLinks(currentPage, toRef(props, 'pageCount'))
</script>

<style scoped>
    .list-enter-active {
        transition: all 0.4s ease;
    }
    /* .list-leave-active {

        } */
    .list-enter-from {
        opacity: 0;
        transform: translateX(18px);
    }

    /* .list-leave-to {

        } */
</style>
