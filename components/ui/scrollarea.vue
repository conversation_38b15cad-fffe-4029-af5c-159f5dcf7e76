<template>
    <OverlayScrollbarsComponent defer ref="scrollArea" class="scroll-shadows overflow-auto" @os-initialized="initialize" @os-scroll="update">
        <slot />
    </OverlayScrollbarsComponent>
</template>

<script lang="ts" setup>
    // import { useScroll } from '@vueuse/core'
    import { OverlayScrollbarsComponent } from 'overlayscrollbars-vue'
    import { type OverlayScrollbars } from 'overlayscrollbars'

    const props = defineProps<{
        currentPage?: number
        defaultScrollOffset?: number
        emitScrollOffset?: boolean
    }>()

    const emit = defineEmits<{
        (e: 'scroll-position', value: number): void
    }>()

    const scrollArea = ref<any>()

    const hasOverflow = ref<boolean>(false)

    const scrollViewport = ref<HTMLElement>()

    // const { arrivedState, directions } = useScroll(scrollViewport)

    function initialize(instance: OverlayScrollbars) {
        scrollViewport.value = instance.elements().viewport
        scrollViewport.value.classList.add('scroll-shadows')

        if(props.defaultScrollOffset !== 0 ) {
            nextTick(() => {
                const el = scrollViewport.value
                if (el) {
                    el.scrollTop = props.defaultScrollOffset
                }
            })
        }
    }

    function update(instance: OverlayScrollbars) {
        hasOverflow.value = instance.state().hasOverflow.y
        if(props.emitScrollOffset) {
            const scrollTop = instance.elements().viewport.scrollTop
            emit('scroll-position', scrollTop)
        }
    }

    /**
     * Check if currentPage changed and then reset scrollY position to top
     */
    watch(props, () => {
        scrollViewport.value.scrollTop = 0 // Scroll to the top
    })
</script>

<style lang="pcss" scoped>
    .scroll-shadows {

        /* @apply shadow-inner; */
        /* box-shadow: inset 0 -5px 5px -5px #333 */
    }
</style>
