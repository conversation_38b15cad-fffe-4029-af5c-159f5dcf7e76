<template>
    <TabGroup class="tab-group" as="div" :defaultIndex="defaultTabIndex">
        <TabList class="mb-8 flex">
            <Tab v-for="{ name, disabled } in tabs" :key="name" class="tab" :class="[tabWidth ? tabWidth : 'w-1/2']" :disabled="disabled">{{
                name
            }}</Tab>
        </TabList>
        <TabPanels>
            <slot></slot>
        </TabPanels>
    </TabGroup>
</template>

<script lang="ts" setup>
    import { TabGroup, TabList, Tab, TabPanels } from '@headlessui/vue'

    type TabType = {
        name: string
        disabled: boolean
    }

    const props = defineProps<{
        tabs: TabType[]
        defaultTabIndex?: number
        tabWidth?: string
    }>()
</script>

<style scoped lang="pcss">
    .tab {
        @apply md:px-4 py-2 text-center !mr-1 text-sm font-normal border-b border-b-sky-200 text-sky-500 whitespace-nowrap hover:border-b-sky-500;
        @apply ui-selected:font-medium ui-selected:border-b-2 ui-selected:border-b-sky-500;
        @apply disabled:opacity-50 disabled:pointer-events-none;
    }

    .tab-group {
        @apply w-full flex flex-col flex-1 overflow-y-scroll;
    }
</style>
