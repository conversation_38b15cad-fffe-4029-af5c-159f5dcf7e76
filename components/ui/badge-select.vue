<template>
    <div
        v-for="(item, index) in options"
        :key="index"
        class="my-2 mr-2 rounded-full px-2 py-1 text-xs"
        :class="{
            'bg-gold-100 text-gray-800': isSelected(item),
            'bg-gray-50 text-gray-500 hover:bg-gray-200 hover:cursor-pointer': !isSelected(item),
        }"
        @click="handleSelection(item)"
    >
        {{ handleLabel(item) }}
    </div>
</template>

<script setup lang="ts" generic="T extends any">
    const props = defineProps<{
        options: T[];
        selectionHandler?: (currentSelection: T | T[], selectedItem: T) => T[];
        labelHandler?: (label: T) => any
    }>();

    const modelValue = defineModel<T | T[]>();

    const isSelected = (item: T) => {
        if (Array.isArray(modelValue.value)) {
            return modelValue.value.includes(item);
        }
        return modelValue.value === item;
    };

    const handleLabel = (item: T) => {
        if(props.labelHandler) {
            return props.labelHandler(item)
        }
        return item
    }

    const handleSelection = (item: T) => {
        if (props.selectionHandler) {
            modelValue.value = props.selectionHandler(modelValue.value, item);
        } else {
            if (Array.isArray(modelValue.value)) {
                modelValue.value = modelValue.value.includes(item)
                    ? modelValue.value.filter((i) => i !== item)
                    : [...modelValue.value, item];
            } else {
                modelValue.value = modelValue.value === item ? null : item;
            }
        }
    };
</script>
