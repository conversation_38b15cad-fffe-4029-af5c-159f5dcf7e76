<template>
    <input type="date" v-on="inputListeners" v-bind="inputAttrs" max="9999-12-31" class="form-input w-full" />
</template>

<script lang="ts" setup>
    import { useVModel } from '@vueuse/core'

    import { useDatetimeInput } from '~~/composables/ui/datetime-input'

    const props = defineProps<{
        modelValue: Date | null
        min?: Date
        max?: Date
        step?: number
        isEndDate?: boolean
    }>()

    const emit = defineEmits<{
        (event: 'update:modelValue', value: Date)
    }>()

    const model = useVModel(props, 'modelValue', emit)

    const { inputAttrs, inputListeners } = useDatetimeInput(model, {
        type: 'date',
        ...toRefs(props)
    })
</script>
