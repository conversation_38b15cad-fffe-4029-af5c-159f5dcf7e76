<script lang="ts" setup>
    import { Dialog, DialogPanel, TransitionChild, TransitionRoot } from '@headlessui/vue'

    const emit = defineEmits(['cancel'])

    const props = defineProps<{
        isRevealed?: boolean
    }>()
</script>

<template>
    <TransitionRoot :appear="false" :show="isRevealed" as="template">
        <Dialog as="div" class="relative z-50 h-full w-full">
            <UiOverlayBackdrop />
            <TransitionChild
                as="div"
                enter="transition-all duration-300 ease-in-out transform"
                enter-from="translate-y-full"
                enter-to="translate-y-0"
                leave="transition-all duration-300 ease-in-out"
                leave-from="translate-y-0 "
                leave-to="translate-y-full"
                class="pointer-events-none fixed inset-y-0 m-0 flex w-full overflow-hidden pl-10">
                <DialogPanel class="pointer-events-auto m-auto w-screen max-w-4xl">
                    <div class="relative flex h-full max-h-screen flex-col overflow-auto rounded bg-white shadow-xl">
                        <button
                            type="button"
                            title="Panel schließen"
                            @click="emit('cancel')"
                            class="absolute right-6 top-6 z-10 ml-3 flex h-8 w-8 items-center justify-center rounded-full text-sky-500 transition-colors hover:bg-blue-50 active:bg-blue-100">
                            <span class="sr-only">Panel schließen</span>
                            <IconX class="h-7 w-7" aria-hidden="true" />
                        </button>
                        <div class="relative z-0 flex flex-1 flex-col p-8">
                            <slot />
                        </div>
                        <slot name="bottom-sheet" />
                    </div>
                </DialogPanel>
            </TransitionChild>
        </Dialog>
    </TransitionRoot>
</template>
