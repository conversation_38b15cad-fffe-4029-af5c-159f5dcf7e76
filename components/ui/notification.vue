<script lang="ts" setup>
    import { useTimeoutFn } from '@vueuse/core'

    const props = defineProps<{
        type: 'neutral' | 'error' | 'success' | 'default' | 'waiting' | 'soft-error' | 'cancel'
        content: string
        isFinal?: boolean
    }>()

    const emit = defineEmits(['dismissed', 'callback'])

    const { isPending, start, stop } = useTimeoutFn(() => emit('dismissed'), 5000, { immediate: false })

    const isCancel = ref<boolean>(false)

    watchEffect(() => {
        if (!!props.isFinal) {
            start()
        }
    })
</script>

<template>
    <div
        @mouseenter="(_event) => isPending && stop()"
        @mouseleave="(_event) => isFinal && start()"
        class="flex items-center justify-between gap-x-2 rounded-md px-8 py-2 shadow-md transition-all"
        :class="{
            'bg-sky-200 text-white': type === 'waiting' || type === 'cancel',
            'bg-grey-900 text-white': type === 'default',
            'bg-green-700 text-white': type === 'success',
            'bg-yellow-600 text-black': type === 'neutral',
            'bg-red-500 text-white': type === 'error',
            'bg-softred-50 text-black': type ==='soft-error'
        }">
        <span>
            <slot>{{ content }} </slot>
        </span>

        <div class="flex items-center gap-4">
            <button
                v-if="type === 'cancel'"
                @click="() => emit('callback')"
                class='px-4 border-l border-r border-white text-white hover:bg-sky-300'
            >
                <span v-if="!isCancel">Abbrechen</span>
                <span v-else>
                     <UiIcon name="refresh" class="h-4 w-4 animate-spin" />
                </span>
            </button>
            <UiIcon v-if="type === 'waiting' || type === 'cancel'" name="refresh" class="h-4 w-4 animate-spin" />
            <button v-else @click="(_event) => emit('dismissed')" class="flex-0">
                <IconX class="h-auto w-4" />
            </button>
        </div>
    </div>
</template>
