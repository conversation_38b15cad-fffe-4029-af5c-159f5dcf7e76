<template>
    <li
        class="text-grey-800 relative cursor-default select-none py-2 pl-3 pr-9"
        :class="{ 'bg-grey-100 ': active, 'italic': selected, 'opacity-50': option.disabled }">
        <span class="block truncate" v-text="option.label" />
        <span v-if="selected" class="text-grey-500 absolute inset-y-0 right-0 flex items-center pr-4">
            <IconCheck class="h-5 w-5" aria-hidden="true" />
        </span>
    </li>
</template>

<script lang="ts" setup>
    type Props = {
        active: boolean
        option: Option
        selected: boolean
    }

    const props = defineProps<Props>()
</script>
