<template>
    <Combobox as="div" :model-value="null" @update:model-value="select" class="combobox relative">
        <div ref="reference" class="relative flex items-center combobox-input gap-x-2">
            <ComboboxInput @change="query = $event.target.value" :placeholder="placeholder" class="w-full p-0 text-base outline-none" />
            <IconRefresh v-if="isFetching" class="w-4 h-4 text-gray-500 animate-spin" />
        </div>

        <div ref="popper" class="absolute z-50 w-full" :class="{ 'max-w-sm': !fullWidthPanel, 'max-w-[528px]': fullWidthPanel, 'transformX': customPosition}">
            <ComboboxOptions v-if="!!query" class="combobox-options">
                <UiScrollarea class="max-h-64">
                    <ComboboxOption
                        v-for="option in technicArticles"
                        :key="option.id"
                        :value="option"
                        :disabled="isDisabled(option)"
                        class="relative combobox-option ui-disabled:grayscale ui-disabled:opacity-40">
                        <ProvideTechnicArticle :technic-article="option" class="text-sm" />
                    </ComboboxOption>

                    <div v-if="hasNextPage || isFetchingNextPage" class="flex justify-center py-2 mx-3 border-t">
                        <button @click="() => fetchNextPage()" :disabled="isFetchingNextPage" class="form-button button-xs">
                            <span v-if="isFetchingNextPage">Weitere werden geladen</span>
                            <span v-else-if="hasNextPage" class="flex items-center gap-x-1">
                                <UiIcon name="plus" class="w-3 h-3" />
                                Weitere laden
                            </span>
                        </button>
                    </div>
                </UiScrollarea>

                <div v-if="!technicArticles.length && isFetching" class="px-3 py-2 text-sm font-light text-grey-400">
                    <UiIcon name="refresh" class="inline-block w-4 h-4 mr-2 animate-spin" />
                    Suche nach <span class="italic text-grey-600">{{ query }}</span>
                </div>

                <div v-if="!technicArticles.length && !isFetching" class="px-3 py-2 text-sm font-light align-baseline text-grey-400">
                    <UiIcon name="ban" class="inline-block w-4 h-4 mr-2" />
                    Keine Vorschläge für <span class="italic text-grey-600">{{ query }}</span>
                </div>
            </ComboboxOptions>
        </div>
    </Combobox>
</template>

<script lang="ts" setup>
    import { Combobox, ComboboxInput, ComboboxOptions, ComboboxOption, ComboboxLabel } from '@headlessui/vue'
    import { useInfiniteTechnicArticleSuggestions } from '~/composables/suggestions'

    const props = withDefaults(
        defineProps<{
            popperPosition?: 'bottom-start' | 'bottom-end' | 'top-start' | 'auto-start'
            placeholder?: string
            fullWidthPanel?: boolean
            customPosition?: boolean
            excludeArticles?: ApiModel<'TechnicArticle'>[]
        }>(),
        {
            customPosition: false,
            popperPosition: 'bottom-start',
            placeholder: 'Für Vorschläge tippen',
            fullWidthPanel: false
        }
    )

    const emit = defineEmits<{
        (event: 'select', value: ApiModel<'TechnicArticle'>): any
    }>()

    const { query, technicArticles, isFetching, isFetchingNextPage, fetchNextPage, hasNextPage } = useInfiniteTechnicArticleSuggestions()

    function select(value: ApiModel<'TechnicArticle'>) {
        emit('select', value)
        query.value = ''
    }

    function isDisabled(article: ApiModel<'TechnicArticle'>) {
        return !!props.excludeArticles?.find(({ id }) => id === article.id)
    }


    const reference = ref()
    const popper = ref()
    usePopper(reference, popper, {
        placement: props.popperPosition,
        modifiers: [
            {
                name: 'offset',
                options: {
                    offset: [0, 4]
                }
            },
            {
                name: 'preventOverflow'
            }
        ]
    })
</script>

<style scoped lang="pcss">
    .combobox {
        @apply relative
    }
    .combobox-input {
        @apply w-full form-input pl-7;
        @apply icon-search-blue-500 bg-[left_0.5rem_center] bg-[length:1rem_1rem] bg-no-repeat
    }
    .combobox-options {
        /* @apply absolute z-30 w-full mt-1 overflow-auto text-base bg-white rounded-md shadow-xl max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none; */
        @apply overflow-auto empty:hidden text-base bg-white rounded-md shadow-xl ring-1 ring-black ring-opacity-5 focus:outline-none;
    }

    .combobox-option {
        @apply  py-2 pl-3 cursor-default select-none pr-9 text-grey-800 ui-active:bg-gray-50;

        &[data-headlessui-state~="active"] {
            @apply icon-plus-slate-500 bg-no-repeat bg-[length:1em_1em] bg-[right_1em_center]
        }

    }

    /* We can use this class to overrite popper offset modifier */
    .transformX {
       transform: translate( 0px, 42px) !important;
    }
</style>
