<template>
  <div class="header_search relative"  >
    <div ref="reference" class="relative flex items-center header_search-input gap-x-2">
      <input
        v-model="query"
        @keydown.esc="query = ''"
        @keydown.arrow-down="handleArrowDown"
        @keydown.arrow-up="handleArrowUp"
        @keydown.enter="handleEnter"
        placeholder="Suchen"
        class="w-full p-0 text-base outline-none"
      />
      <IconRefresh v-if="isFetchingNextPage" class="w-4 h-4 text-gray-500 animate-spin" />
    </div>
    <div class="absolute z-50 w-full" v-if="!!query" ref="header_search">
        <div class="header_search-options">
            <div class="max-h-[50vh] md:max-h-[70vh] overflow-y-auto" ref="scrollContainer">
                <HeaderSearchOption
                    title="Ereignisse"
                    :options="events"
                    :isFetching="isFetchingEvents"
                    :hasNextPage="hasEventNextPage"
                    :fetchNextPage="fetchEventsNextPage"
                    :query="query"
                    :isFetchingNextPage="isFetchingEventNextPage"
                    :activeIndex="activeIndex"
                    :indexOffset="0"
                    @select="selectOption"
                    @hover="setActiveIndex"
                    ref="eventsRef"
                >
                    <template v-slot:default="{ option }">
                        <div class="flex flex-col gap-1 mb-2">
                            <ProvideEvent :event="option" v-slot="{ title, icon, description }">
                                <div class="flex">
                                    <div class="text-grey-900 bg-grey-100 flex items-center gap-x-2 rounded-xl px-2 py-1">
                                        <UiIcon :name="icon" class="h-auto w-5" />
                                        <span class="text-sm">{{ title }}</span>
                                    </div>
                                </div>
                                <h2 class="text-base">{{ description }}</h2>
                                <span class="text-sm empty:hidden" v-text="option.organisation?.name" />
                                <div class="flex-col sm:flex sm:flex-row flex-wrap">
                                    <span class="text-grey-600 text-sm flex flex-wrap">
                                        {{ $formatDateRangeWithAbbreviatedDayAndTime(option.dateFrom, option.dateUpTo) }}
                                    </span>
                                </div>
                            </ProvideEvent>
                        </div>
                    </template>
                </HeaderSearchOption>

                <HeaderSearchOption
                    title="Mitglieder"
                    :options="members"
                    :isFetching="isFetchingMembers"
                    :hasNextPage="hasMembersNextPage"
                    :fetchNextPage="fetchMembersNextPage"
                    :query="query"
                    :isFetchingNextPage="isFetchingMembersNextPage"
                    :activeIndex="activeIndex"
                    :indexOffset="events.length"
                    @select="selectOption"
                    @hover="setActiveIndex"
                    ref="membersRef"
                >
                    <template v-slot:default="{ option }">
                        <ProvideMember :member="option" class="text-sm" />
                    </template>
                </HeaderSearchOption>

                <HeaderSearchOption
                    title="Technik"
                    :options="technicArticles"
                    :isFetching="isFetchingTechnicArticle"
                    :hasNextPage="hasTechnicNextPage"
                    :fetchNextPage="fetchTechnicNextPage"
                    :query="query"
                    :isFetchingNextPage="isFetchingTechnicNextPage"
                    :activeIndex="activeIndex"
                    :indexOffset="events.length + members.length"
                    @select="selectOption"
                    @hover="setActiveIndex"
                    ref="technicArticlesRef"
                >
                    <template v-slot:default="{ option }">
                        <ProvideTechnicArticle :technic-article="option" class="text-sm" />
                    </template>
                </HeaderSearchOption>

                <HeaderSearchOption
                    title="Adressen"
                    :options="addressContacts"
                    :isFetching="isFetchingAddressContacts"
                    :hasNextPage="hasAddressContactsNextPage"
                    :fetchNextPage="fetchAddressContactsNextPage"
                    :query="query"
                    :isFetchingNextPage="isFetchingAddressContactsNextPage"
                    :activeIndex="activeIndex"
                    :indexOffset="events.length + members.length + technicArticles.length"
                    @select="selectOption"
                    @hover="setActiveIndex"
                    ref="addressContactsRef"
                >
                    <template v-slot:default="{ option }">
                        <ProvideAddress :address="option" />
                    </template>
                </HeaderSearchOption>
            </div>
        </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
    import { onClickOutside } from '@vueuse/core'

    const query = ref('');
    const activeIndex = ref(-1);
    const scrollContainer = ref<HTMLElement | null>(null);

    const {
        public: { portalUrl }
    } = useRuntimeConfig();

    const {
        query: eventQuery,
        events,
        isFetching: isFetchingEvents,
        fetchNextPage: fetchEventsNextPage,
        hasNextPage: hasEventNextPage,
        isFetchingNextPage: isFetchingEventNextPage,
        limit: limitEvents
    } = useInfiniteEventsSuggestions(true);

    const {
        query: memberQuery,
        members,
        isFetching: isFetchingMembers,
        fetchNextPage: fetchMembersNextPage,
        hasNextPage: hasMembersNextPage,
        isFetchingNextPage: isFetchingMembersNextPage,
        limit: limitMembers,
        scope: memberScope
    } = useInfiniteMemberSuggestions(true);

    const {
        query: technicQuery,
        technicArticles,
        isFetching: isFetchingTechnicArticle,
        isFetchingNextPage: isFetchingTechnicNextPage,
        fetchNextPage: fetchTechnicNextPage,
        hasNextPage: hasTechnicNextPage,
        limit: limitTechnicArticles
    } = useInfiniteTechnicArticleSuggestions(true);

    const {
        query: addressQuery,
        addressContacts,
        isFetching: isFetchingAddressContacts,
        isFetchingNextPage: isFetchingAddressContactsNextPage,
        fetchNextPage: fetchAddressContactsNextPage,
        hasNextPage: hasAddressContactsNextPage,
        limit: limitTechnicContacts
    } = useInfiniteAdressSuggestions(true);

    limitTechnicArticles.value = 5;
    limitEvents.value = 5;
    limitMembers.value = 5;
    memberScope.value = 'MY_MEMBERS';

    const isFetchingNextPage = computed(() =>
    isFetchingEventNextPage.value || isFetchingMembersNextPage.value || isFetchingTechnicNextPage.value || isFetchingAddressContactsNextPage.value
    );

    watch(query, () => {
        technicQuery.value = query.value.toLocaleLowerCase();
        memberQuery.value = query.value.toLocaleLowerCase();
        eventQuery.value = query.value.toLocaleLowerCase();
        addressQuery.value = query.value.toLocaleLowerCase();
        activeIndex.value = -1; // Reset active index when query changes
    });

    function handleArrowDown() {
    if (activeIndex.value < getTotalOptionsCount() - 1) {
        activeIndex.value++;
    } else {
        activeIndex.value = 0;
    }
    scrollToActiveOption();
    }

    function handleArrowUp() {
        if (activeIndex.value > 0) {
            activeIndex.value--;
        } else {
            activeIndex.value = getTotalOptionsCount() - 1;
        }
        scrollToActiveOption();
    }

    function handleEnter() {
        const selectedOption = getSelectedOption();
        if (selectedOption) {
            selectOption(selectedOption);
        }
    }

    function getSelectedOption() {
        const totalEvents = events.value.length;
        const totalMembers = members.value.length;
        const totalTechnic = technicArticles.value.length;

        if (activeIndex.value >= 0) {
            if (activeIndex.value < totalEvents) {
            return events.value[activeIndex.value];
            } else if (activeIndex.value < totalEvents + totalMembers) {
            return members.value[activeIndex.value - totalEvents];
            } else if (activeIndex.value < totalEvents + totalMembers + totalTechnic) {
            return technicArticles.value[activeIndex.value - totalEvents - totalMembers];
            } else {
            return addressContacts.value[activeIndex.value - totalEvents - totalMembers - totalTechnic];
            }
        }
        return null;
    }

    function getTotalOptionsCount() {
        return events.value.length + members.value.length + technicArticles.value.length + addressContacts.value.length;
    }

    function selectOption(option: ApiModel<'MemberData'> | ApiModel<'Event'> | ApiModel<'TechnicArticle'> | ApiModel<'AddressContact'>) {
        if (option instanceof useModelFactory('MemberData')) {
            openMember(option);
        } else if (option instanceof useModelFactory('Event')) {
            openEvent(option);
        } else if (option instanceof useModelFactory('TechnicArticle')) {
            openTechnicArticle(option);
        } else if(option instanceof useModelFactory('AddressContact')) {
            openAddress(option)
        }
        query.value = '';
    }

    function openAddress(address: ApiModel<'AddressContact'>) {
        window.open(`${portalUrl}/adr/drkadr--category/${address.category.id}/drkadr--address-contact/${address.id}`, '_blank');
    }

    function openMember(member: ApiModel<'MemberData'>) {
        window.open(`${portalUrl}/mv/drkadminmember--mitglied/${member.id}/drkadminmember--tab/stammdaten/`, '_blank');
    }

    function openEvent(event: ApiModel<'Event'>) {
        useRouter().push({ name: 'events-id-details', params: { id: event.id } });
    }

    function openTechnicArticle(article: ApiModel<'TechnicArticle'>) {
        window.open(`${portalUrl}/tec/drktechnik--article/${article.id}`, '_blank');
    }

    // use keyboard to change selected option and update scroll
    function scrollToActiveOption() {
    if (scrollContainer.value && activeIndex.value >= 0) {
        // this we can find in option element
        const options = scrollContainer.value.querySelectorAll('.header_search-option');

        if (options[activeIndex.value]) {
            const option = options[activeIndex.value] as HTMLElement;
            const container = scrollContainer.value as HTMLElement;
            const optionTop = option.offsetTop - 50;
            const optionBottom = optionTop + option.offsetHeight + 90;
            const containerTop = container.scrollTop;
            const containerBottom = containerTop + container.clientHeight;

            if (optionTop < containerTop) {
                // Option is above the viewport
                container.scrollTop = optionTop;
            } else if (optionBottom > containerBottom) {
                // Option is below the viewport
                container.scrollTop = optionBottom - container.clientHeight;
            }
        }
    }
    }

    function setActiveIndex(index: number) {
        activeIndex.value = index;
        scrollToActiveOption();
    }

    // close options and reset query when click outside
    const header_search = ref()
    onClickOutside(header_search, () => query.value = ""  )
</script>

<style scoped lang="pcss">
    .header_search {
      @apply relative;
    }
    .header_search-input {
        @apply w-full form-input pl-7;
        @apply icon-search-blue-500 bg-[left_0.5rem_center] bg-[length:1rem_1rem] bg-no-repeat;
    }
    .header_search-options {
        @apply overflow-auto empty:hidden text-base bg-white rounded-md shadow-xl ring-1 ring-black ring-opacity-5 focus:outline-none;
    }
    .header_search-option {
        @apply py-2 pl-3 cursor-default select-none pr-9 text-grey-800 ui-active:bg-gray-50;
    }
    .ui-active {
        @apply bg-gray-50;
    }
</style>

