<template>
    <UiLoader :is-loading="isFetching">
        <div class="lg:flex lg:h-full lg:flex-col">
            <div
                class="ring-1 ring-black/5 lg:flex lg:flex-col"
                :class="{'shadow' : view === 'month'}"
            >
                <div class="grid-cols-7 divide-x divide-white grid text-london-hue-950 bg-surface-calendar">
                    <div v-if="view === 'week'" class="col-end-1 w-10 lg:w-14" />
                    <button
                        v-for="(day, i) in weekdays"
                        :key="i"
                        type="button"
                        @click="view === 'week' ? selectDay(weekDates[i]) : null"
                        class="flex items-center flex-col lg:flex-row justify-center py-3 bg-surface-calendar text-xs"
                        :class="[
                            (isEqual(weekDates[i], selectedDay) && view === 'week') && '!bg-surface-calendar-selected',
                            view === 'week' || 'hover:cursor-default'

                        ]"

                    >
                        <span class="sm:hidden"> {{ format(day, 'EEEEE', { locale: de }) }}</span>
                        <span class="sr-only">{{ format(day, 'EEEE', { locale: de }) }}</span>
                        <span class="hidden sm:inline">{{ format(day, 'EEE', { locale: de }).replace(/\.$/, '').toUpperCase() }}</span>
                        <span
                            v-if="view === 'week'"
                            class="lg:ml-2 p-1 font-semibold"
                            :class="[
                                isEqual(weekDates[i], selectedDay) && 'text-white bg-london-hue-950 rounded-full w-6 h-6'
                            ]"
                        >
                            {{format(weekDates[i], "dd")}}
                        </span>
                        <span
                            v-if="view === 'week'"
                            class="lg:hidden p-1 rounded-full bg-sky-400 mt-2"
                            :class="[
                                amountOfEventsByDay[i] === 0 && 'invisible'
                            ]"
                        ></span>
                    </button>
                </div>
                <div v-if="view === 'week' && selectedDayEventsNumber > 0 " class="lg:hidden text-white font-semibold my-2 px-4">
                    <div class="rounded-full bg-sky-400 w-full flex items-center py-1 px-4">
                        <span class="inline-block w-2 h-2 rounded-full bg-white mr-2"></span> {{ selectedDayEventsNumber }} {{ selectedDayEventsNumber === 1 ? 'Ereignis' : 'Ereignisse' }}
                    </div>
                </div>
                <CalendarMonthView v-if="view === 'month' && !isFetching" :events="calendarEvents"/>
                <CalendarWeekView v-if="view === 'week' && !isFetching" :events="calendarEvents"   />
            </div>
        </div>
    </UiLoader>
</template>

<script setup lang="ts">
    import { format, isEqual, isWithinInterval, startOfDay, endOfDay, getDay, startOfWeek} from 'date-fns'
    import { de } from 'date-fns/locale'
    import { CalendarData } from '~/composables/calendar-events'
    import type { EventFilter } from '~~/composables/event-filter'

    const props = defineProps<{
        view: string
        filter: EventFilter
    }>()

    const {
        selectedDay,
        weekDates,
        weekdays,
        currentMonthDate,
        today,
        selectDay
    } = inject<CalendarData>('calendar-dates')

    const filter = computed(()=>{
        return props.filter
    })

    const { calendarEvents, isFetching }  = useCalendarEvents(currentMonthDate, filter)

    const sortedCalendarEventsByDay = computed(() => {
        const eventsByDay = new Map()
        weekDates.value.forEach(day => {
            eventsByDay.set(day, calendarEvents.value.filter(event => {
                    return isWithinInterval(day, {start: startOfDay(event.partDateFrom || event.dateFrom), end: endOfDay(event.partDateUpTo || event.dateUpTo)})
            }))
        })
        return eventsByDay
    })

    const amountOfEventsByDay = computed(() => {
        return Array.from(sortedCalendarEventsByDay.value.values()).map(events => events.length)
    })

    const selectedDayEventsNumber = computed(() => {
        return amountOfEventsByDay.value[getDay(selectedDay.value) === 0 ? 6 : getDay(selectedDay.value) - 1]
    })


</script>
