<template>
    <div class="flex items-center lg:justify-between order-1 lg:order-none lg:ml-auto lg:mr-8 flex-wrap lg:gap-2 gap-y-4">
        <button
            type="button"
            @click="selectDay(today)"
            class="form-button button-outline items-center rounded-full">
            Heute
        </button>
        <button
            type="button"
            @click="actionsDependsOnView[view].previous"
            class="text-blue-600 hover:text-blue-900 p-4">
            <span class="sr-only">{{ view === 'week' ? 'Previous week' : 'Previous month' }}</span>
            <UiIcon name="chevron-left" class="h-6 w-6" aria-hidden="true" />
        </button>
        <button
            type="button"
            @click="actionsDependsOnView[view].next"
            class="text-blue-600 hover:text-blue-900 p-4">
            <span class="sr-only">{{ view === 'week' ? 'Next week' : 'Next month' }}</span>
            <UiIcon name="chevron-right" class="h-6 w-6" aria-hidden="true" />
        </button>
        <div class="flex gap-4 items-center flex-row-reverse lg:flex-row">
            <time class="text-base font-semibold text-gray-900 min-w-[140px] text-center" :datetime="formattedCurrentMonth">{{ view === 'week' ? formattedCurrentWeek : formattedCurrentMonth }}</time>
            <span v-if="view === 'week'" class="bg-blue-50 py-1 px-3 rounded-full min-w-[72px]">KW {{ currentWeekNumber.toString().padStart(2, '0') }}</span>
        </div>
    </div>
</template>

<script lang="ts" setup>
    import { CalendarData } from '~/composables/calendar-events'

    const props = defineProps<{
        view: string
    }>()

    const actionsDependsOnView = {
        'week': { previous: () => setWeek(-1), next: () => setWeek(1) },
        'month': { previous: () => setMonth(-1), next: () => setMonth(1) }
    }

    const {
        currentWeekNumber,
        formattedCurrentMonth,
        formattedCurrentWeek,
        today,
        setWeek,
        selectDay,
        setMonth
    } = inject<CalendarData>('calendar-dates')
</script>
