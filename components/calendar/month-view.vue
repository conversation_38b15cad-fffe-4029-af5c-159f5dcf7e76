<template>
  <div>
    <div class="flex bg-gray-200 text-xs">
      <div
        class="hidden w-full lg:grid lg:grid-cols-7 lg:gap-px relative"
        :class="`row-count-${numberOfWeeksInCurrentMonth}`"
      >
        <!-- Multi-day events -->
        <template v-for="week in weekRows" :key="`week-${week}`">
          <div class="absolute z-10 w-full pointer-events-none" :style="`top: ${(week - 1) * 160}px; height: 160px;`">
              <template v-for="event in multiDayEventsForWeek(week)" :key="`multi-${event.id}`">
              <NuxtLink
                :to="{ name: 'events-id-details', params: { id: event.id }}"
                class="absolute px-2 py-1 rounded-md text-xs font-bold truncate shadow-md pointer-events-auto flex justify-between"
                :style="calculateWeekMultiDayEventPosition(event, week, days, hoveredEventId === event.id)"
                @mouseenter="hoveredEventId = event.id"
                @mouseleave="hoveredEventId = null"
              >
                <span class="w-20">
                  {{ formatEventStartTime(event) }}
                </span>
                <span class="truncate">
                  {{ event.title }}
                </span>
                <span class="w-20 flex justify-end">
                  {{ formatEventEndTime(event)  }}
                </span>
              </NuxtLink>
            </template>
          </div>
        </template>

         <!-- Every day can be selected -->
        <template v-for="day in days" :key="day.toString()">
            <div class="relative h-40">
                <button
                    type="button"
                    class="absolute top-0 left-0 h-full w-full z-5"
                    :class="[
                        isSameMonth(day, currentMonthDate) ? 'bg-white' : 'bg-gray-100 text-london-hue-800',
                        isSameMonth(day, today) ? 'text-gray-500' : 'text-gray-300',
                        isEqual(day, selectedDay) && 'text-white !bg-surface-calendar-subtitle',
                    ]"
                    @click="selectDay(day)">
                </button>
                <time
                    :datetime="formattedDateISO(day)"
                    class="absolute top-1 left-1 z-10 pointer-events-none"
                    :class="[
                        isToday(day)
                            ? 'inline-flex h-6 w-6 items-center justify-center rounded-full bg-london-hue-800 font-semibold text-white'
                            : undefined
                    ]"
                    >{{ format(day, 'd') }}</time>

                <!-- Regular events -->
                <ol
                    class="absolute left-1 w-full pr-2 z-10 font-semibold pointer-events-none mt-8"
                    v-if="getRegularEventsForDay(calendarEvents, day).length > 0"
                >
                    <template v-for="(event, index) in regularEventsMinusMultiDayEvents(calendarEvents, day)" :key="`single-${event.id}`">
                        <li :style="getRegularEventStyle(day, calendarEvents, days, index)">
                          <NuxtLink
                          :to="{ name: 'events-id-details', params: { id: event.id }}"
                          class="flex justify-between mr-2 px-2 py-1 rounded pointer-events-auto hover:bg-blue-50"
                        >
                            <div class="text-xs font-semibold truncate w-2/3">{{ event.title }}</div>
                            <div v-if="!event.isMultiDay" class="text-xs opacity-90">{{ format(event.dateFrom, 'HH:mm') }}</div>
                          </NuxtLink>
                        </li>
                    </template>
                    <li class="absolute top-[102px]">
                      <button
                        type="button"
                        v-if="getCombinedEventsForDay(calendarEvents, day).length > 4"
                        class="text-xs cursor-pointer hover:underline mt-1 px-2 pointer-events-auto hover:bg-blue-50 rounded-sm hover:ring-1 ring-blue-50"
                        @click.stop="openEventsList(day)"
                      >
                      <span>
                        {{ getCombinedEventsForDay(calendarEvents, day).length - 4 }}
                        {{ getCombinedEventsForDay(calendarEvents, day).length === 1 ? 'weiteres Ereignis' : 'weitere Ereignisse' }}
                      </span>
                    </button>
                    </li>
                </ol>
            </div>
        </template>
      </div>

      <!-- Mobile view grid -->
      <div
        class="grid w-full grid-cols-7 gap-px lg:hidden"
        :class="{'border-gray-100' : getCombinedEventsForDay(calendarEvents, selectedDay).length > 0}"
      >
        <button
            v-for="(day, dayIndex) in days"
            :key="day.toString()"
            type="button"
            @click="()=>{
              selectDay(day)
              openEventsList(day, true)
            }"
            :class="[
                dayIndex === 0 && getColClasses((getDay(day) + 6) % 7 ),
                isSameMonth(day, firstDayCurrentMonth) ? 'bg-white' : 'bg-gray-100',
                (isEqual(day, selectedDay) || isToday(day)) && 'font-semibold',
                isEqual(day, selectedDay) && 'text-white !bg-surface-calendar-subtitle',
                !isEqual(day, selectedDay) && isToday(day) && 'text-london-hue-950',
                !isEqual(day, selectedDay) && isSameMonth(day, firstDayCurrentMonth) && !isToday(day) && 'text-gray-900',
                !isEqual(day, selectedDay) && !isSameMonth(day, firstDayCurrentMonth) && !isToday(day) && 'text-gray-500',
                'flex h-14 flex-col px-3 py-2 focus:z-10 hover:bg-blue-50'
            ]">

            <time
                :datetime="formattedDateISO(day)"
                class="flex h-6 w-6 items-center justify-center rounded-full"
                :class="[
                    isToday(day) && '!bg-indigo-600 text-white',
                    isEqual(day, selectedDay) && !isToday(day) && '!bg-gray-900',
                    'ml-auto',
                     getCombinedEventsForDay(calendarEvents, day).length > 0 && 'bg-blue-400 text-white p-2 rounded-full'
                ]">
                {{ format(day, 'd') }}
            </time>
          </button>
      </div>
    </div>

    <!-- List of events in mobile view -->
    <div v-if="getCombinedEventsForDay(calendarEvents, selectedDay).length > 0" class="absolute w-full py-10 sm:px-6 lg:hidden">
      <ul class="overflow-hidden rounded-lg bg-white text-sm shadow-sm ring-1 ring-black/5 flex flex-col gap-2 p-2">
        <template v-for="event in getCombinedEventsForDay(calendarEvents, selectedDay).filter(e => e.isMultiDay)" :key="`modal-multi-${event.id}`">
          <li class="p-2 rounded bg-neptune-100 hover:bg-neptune-300">
            <NuxtLink
              :to="{ name: 'events-id-details', params: { id: event.id }}"
              class="flex justify-between items-center"
            >
              <div class="font-medium text-black w-2/3 break-all">{{ event.title }}</div>
              <div class="text-xs text-black opacity-90">{{ format(event.dateFrom, 'dd.MM') }} - {{ format(event.dateUpTo || event.dateFrom, 'dd.MM') }}</div>
            </NuxtLink>
          </li>
        </template>
        <template v-for="event in getCombinedEventsForDay(calendarEvents, selectedDay).filter(e => !e.isMultiDay)" :key="`modal-single-${event.id}`">
          <li class="p-2 rounded hover:bg-blue-50">
            <NuxtLink
              :to="{ name: 'events-id-details', params: { id: event.id }}"
              class="flex justify-between items-center"
            >
              <div class="font-medium text-black w-2/3">{{ event.title }}</div>
              <div class="text-xs opacity-90">{{ formatEventTimeRange(event) }}</div>
            </NuxtLink>
          </li>
        </template>
      </ul>
    </div>

    <!-- Modal for showing all events of a day -->
    <UiDialog :is-revealed="showDayModal" :no-padding-and-margin="true" class="relative z-50">
        <slot>
          <div class="bg-white rounded-md overflow-y-auto">
            <div class="asbolute top-0 lef-0 flex items-center text-black font-bold mb-4 p-2">
              <span class="w-full flex justify-start"> {{ format(selectedModalDay, 'EEEE dd MMMM').toUpperCase() }}</span>
              <span class="sr-only">{{ format(selectedModalDay, 'EEEE') }}</span>
              <button @click="showDayModal = false" class="bg-gray-200 rounded-full p-1 text-black self-end">
                <IconX class="h-4 w-4" aria-hidden="true" />
              </button>
            </div>
            <ul class="space-y-2 p-2">
              <template v-for="event in modalEvents.filter(e => e.isMultiDay)" :key="`modal-multi-${event.id}`">
                <li class="p-2 rounded bg-neptune-100 hover:bg-neptune-300">
                  <NuxtLink :to="{ name: 'events-id-details', params: { id: event.id }}" class="flex justify-between items-center">
                    <div class="font-bold text-black w-2/3 break-all">{{ event.title }}</div>
                    <div class="text-xs text-black opacity-90">{{ format(event.dateFrom, 'dd.MM') }} - {{ format(event.dateUpTo || event.dateFrom, 'dd.MM') }}</div>
                  </NuxtLink>
                </li>
              </template>
              <template v-for="event in modalEvents.filter(e => !e.isMultiDay)" :key="`modal-single-${event.id}`">
                <li class="p-2 rounded hover:bg-blue-50">
                  <NuxtLink :to="{ name: 'events-id-details', params: { id: event.id }}" class="flex justify-between items-center">
                    <div class="font-medium text-black w-2/3">{{ event.title }}</div>
                    <div class="text-xs opacity-90">{{ formatEventTimeRange(event) }}</div>
                  </NuxtLink>
                </li>
              </template>
            </ul>
          </div>
        </slot>
    </UiDialog>
  </div>
</template>

<script lang="ts" setup>
  import {
    format,
    isToday,
    isSameMonth,
    startOfMonth,
    endOfMonth,
    differenceInCalendarWeeks,
    isEqual,
    getDay,
    startOfWeek,
    differenceInCalendarDays,
    getMilliseconds,
  } from 'date-fns'
  import {
    getMultiDayEventsForDay,
    getRegularEventsForDay,
    getCombinedEventsForDay,
    formatEventStartTime,
    formatEventEndTime,
    formatEventTimeRange,
    calculateWeekMultiDayEventPosition,
    assignEventRows,
    getMultiDayEventsForWeek,
    getRegularEventStyle,
    CalendarData
  } from '~/composables/calendar-events'
  import { ref, computed } from 'vue'

  const props = defineProps<{
      events: ApiModel<'CalendarEvent'>[]
  }>()

  const calendarEvents = computed(() => props.events)
  const hoveredEventId = ref<string | number | null>(null)

  function getWeeksInMonth(date: Date): number {
      const start = startOfMonth(date);
      const end = endOfMonth(date);
      return (
          differenceInCalendarWeeks(end, start, { weekStartsOn: 1 }) + 1
      );
  }

  const colStartClasses = [
      '',
      'col-start-2',
      'col-start-3',
      'col-start-4',
      'col-start-5',
      'col-start-6',
      'col-start-7',
  ]

  function getColClasses(index: number): string {
      return colStartClasses[index]
  }

  const {
      today,
      days,
      firstDayCurrentMonth,
      selectedDay,
      currentMonthDate,
      formattedDateISO,
      selectDay
  } = inject<CalendarData>('calendar-dates')


  function regularEventsMinusMultiDayEvents(calendarEvents: ApiModel<'CalendarEvent'>[], day: Date ) {
    return getRegularEventsForDay(calendarEvents, day).slice(0, (4 - Math.min(4, getMultiDayEventsForDay(calendarEvents, day).length)))
  }

  const numberOfWeeksInCurrentMonth = computed(() => {
    return getWeeksInMonth(currentMonthDate.value)
  })

  const showDayModal = ref(false)
  const showDayMobileEvents = ref(false)
  const selectedModalDay = ref(new Date())
  const modalEvents = ref<ApiModel<'CalendarEvent'>[]>([])

  const weekRows = computed(() => {
      return numberOfWeeksInCurrentMonth.value
  })

  const openEventsList = (day: Date, mobile = false) => {
    selectedModalDay.value = day
    modalEvents.value = getCombinedEventsForDay(calendarEvents.value, day)
    mobile ?  showDayMobileEvents.value = true : showDayModal.value = true
  }

  const filterMultiDayEventsForMonthView = (events: ApiModel<'CalendarEvent'>[], week: Date[]) => {
    const sortedEvents = [...events].sort((a, b) =>
      getMilliseconds(a.dateFrom) - getMilliseconds(b.dateFrom)
    );

    const weekStart = startOfWeek(week[0]);
    const filteredEvents = [];

    // how many events are assigned to each day
    const dailyEventCounts = Array(7).fill(null).map(() => []);

    for (const event of sortedEvents) {
      // which events in current week
      const eventStart = event.dateFrom;
      const eventEnd = event.dateUpTo;

      // Get the start and end day indices within the week
      const startDayIndex = differenceInCalendarDays(eventStart, weekStart);
      const endDayIndex = Math.min(
        differenceInCalendarDays(eventEnd, weekStart),
        6
      );

      // Determine the actual span within this week
      const weekStartDay = Math.max(0, startDayIndex);
      const weekEndDay = Math.min(6, endDayIndex);

      // Check if we can fit this event (all affected days must have < 4 events)
      let canFitEvent = true;
      for (let dayIndex = weekStartDay; dayIndex <= weekEndDay; dayIndex++) {
        if (dailyEventCounts[dayIndex].length >= 4) {
          canFitEvent = false;
          break;
        }
      }

      // update the daily counts
      if (canFitEvent) {
        filteredEvents.push(event);

        // Add this event to all days
        for (let dayIndex = weekStartDay; dayIndex <= weekEndDay; dayIndex++) {
          dailyEventCounts[dayIndex].push(event);
        }
      }
    }

    return filteredEvents;
  };

  const multiDayEventsForWeek = (weekOfTheMonth: number) => {
    const week = days.value.slice((weekOfTheMonth-1)*7,((weekOfTheMonth-1)*7)+7)
    const multiDayEvents = getMultiDayEventsForWeek(calendarEvents.value, week);
    const weekStart = startOfWeek(week[0]);
    const eventsForAWeek = assignEventRows(multiDayEvents, week, 'month').map(event => {
      event.totalColumnsInSlot = Math.min(differenceInCalendarDays(event.dateUpTo, weekStart), 7)
      return event;
    });

    const filteredEvents = filterMultiDayEventsForMonthView(eventsForAWeek, week);
    return filteredEvents;
  };

</script>
