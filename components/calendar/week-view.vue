<template>
  <div
    class="relative flex flex-auto flex-col"
    :style="{ height: `${WEEK_VIEW_ROW_HEIGHT * (defaultEndHour - defaultStartHour) + multiEventsCount  * MULTI_DAY_EVENT_HEIGHT}px`}"
    >
    <!-- Multi-day events -->
    <div class="relative grid" v-if="multiEventsCount > 0">
      <button v-if="isMoreThanTwoRows" type="button" @click="showAllMultiDayEvents" class="absolute bottom-1 left-0.5 lg:left-2 form-button button-outline items-center w-fit p-2">
        <UiIcon name="chevron-down" class="h-4 w-4 lg:h-5 lg:w-5 transition duration-300 ease-in-out" :class="{'rotate-180': showMoreMultipleEvents}" aria-hidden="true"/>
      </button>
      <div class="ml-10 lg:ml-14 col-start-1 row-start-1 hidden lg:grid grid-cols-7 grid-rows-1">
        <div
          v-for="(day, i) in weekdays"
          :key="i"
          class="row-span-full border-l border-r border-gray-100"
          :class="[
            `col-start-${i + 1}`,
            isEqual(weekDates[i], selectedDay) && 'bg-surface-calendar-subtitle',
          ]"
        ></div>
      </div>
      <div
        class="col-start-1 row-start-1 w-full flex items-start mb-2 transition-height duration-100 ease-in-out overflow-y-auto"
        :style="{ height: multiEventsCount > 0 ? `${multiEventsCount * MULTI_DAY_EVENT_HEIGHT}px` : '0px', maxHeight: `${maxVisibleNumberOfMultidayEvents * MULTI_DAY_EVENT_HEIGHT}px` }"
      >
        <div class="sticky left-0 z-10 w-10 lg:w-14 flex-none bg-white ring-1 ring-gray-100" />
        <div class="flex-1 relative">
          <template v-for="(event, index) in multiDayEvents" :key="`multi-${index}`">
            <template v-if="event.weekViewRow === 2 && !showMoreMultipleEvents">
              <div
                v-for="(indicator, day) in numberOfMultiEventsInOneDay "
                :key="indicator.id"
                class="absolute text-xs font-semibold pl-1 pt-1"
                :class="{'hidden': !indicator.showEvents}"
                :style="{top: `${2 * MULTI_DAY_EVENT_HEIGHT}px`, width: `${100/7}%`, left: `${day * 100/7}%`}"
              >
                 + {{ indicator.numberOfEventsOneDay }}
                 <span class="hidden lg:inline">
                  {{indicator.numberOfEventsOneDay === 1 ? 'Ereignis' : 'Ereignisse' }}
                 </span>
              </div>
            </template>
              <NuxtLink
                v-else
                :to="{ name: 'events-id-details', params: { id: event.id }}"
                class="absolute bg-neptune-100 hover:bg-neptune-300 cursor-pointer rounded-md p-1 text-xs text-gray-900 shadow-md flex justify-between"
                :class="{ 'hidden' : event.weekViewRow > 2 && !showMoreMultipleEvents}"
                :style="calculateMultiDayEventPosition(event, weekDates, isMobileView)"
              >
                <span class="w-20">
                  {{ formatEventStartTime(event) }}
                </span>
                <span class="truncate font-bold px-1">
                  {{ event.title }}
                </span>
               <span class="w-20 flex justify-end">
                  {{ formatEventEndTime(event)  }}
                </span>
              </NuxtLink>
          </template>
        </div>
      </div>
    </div>

    <!-- Early events indicator -->
    <div
      v-if="earlyEvents.length > 0"
      class="absolute z-50 pointer-events-none right-0 bg-blue-50 w-10 ml-auto mr-3 text-gray-700 py-1 text-xs flex justify-center"
      :style="earlyEventsIndicatorStyle()"
    >
      + {{ earlyEvents.length }}
    </div>

    <UiScrollarea class="relative" ref="scrollAreaRef" :defaultScrollOffset="scrollOffset" :emitScrollOffset='true' @scroll-position="updateActiveScrollOffset">
      <div class="flex flex-auto overflow-y-auto">
        <!-- Time slots -->
        <div class="w-10 lg:w-14 bg-white ring-1 ring-gray-100 z-40">
            <template v-for="hour in 24" :key="hour">
              <div :style="`${rowStylesCss}`">
                <div class="pr-2 text-right text-xs/5 text-gray-400">
                  {{ (hour - 1).toString().padStart(2, '0') }}:00
                </div>
              </div>
            </template>
        </div>
        <!-- Calendar grid -->
        <div class="grid flex-auto">
          <div class="col-start-1 row-start-1">
            <template v-for="hour in 24" :key="hour">
              <div class="border border-1 border-gray-100" :style="`${rowStylesCss}`">
              </div>
            </template>
          </div>
          <div class="relative col-start-1 row-start-1 hidden lg:grid grid-cols-7 grid-rows-1 divide-x divide-gray-100">
            <button
              v-for="(day, i) in weekdays"
              :key="i"
              type="button"
              @click="selectDay(weekDates[i])"
              class="row-span-full"
              :class="[
                `col-start-${i + 1}`,
                isEqual(weekDates[i], selectedDay) && 'bg-surface-calendar-subtitle',
              ]"
            ></button>
          </div>
          <!-- Regular events -->
          <div class="col-start-1 row-start-1 relative">
            <template v-for="event in regularEvents" :key="event.partId || event.id">
                <NuxtLink
                  :to="{ name: 'events-id-details', params: { id: event.id }}"
                  class="absolute bg-neptune-100 border border-white cursor-pointer rounded-lg text-xs text-gray-900 shadow-md shadow-gray-200 overflow-hidden transition-all duration-100"
                  :style="calculateRegularEventPosition(event, weekDates, {
                    isHovered: hoveredEventId === (event.partId || event.id),
                    isMobileView,
                    multiEventsCount: 0,
                    hiddenEvents: hiddenEventsData
                  })"
                  @mouseenter="hoveredEventId = event.partId || event.id"
                  @mouseleave="hoveredEventId = null"
                >
                  <div class="font-semibold leading-[1.5] truncate bg-neptune-200 rounded-lg p-0.5 pl-1">
                    {{ event.titleForWeekEvent }}
                  </div>
                  <div class="leading-[1.5] pl-1 truncate">
                    {{ event.extendedDescription }}
                  </div>
                  <div class="text-xs opacity-90 pl-1 truncate">{{ formatEventTimeRange(event) }}</div>
              </NuxtLink>
            </template>

            <!-- More events indicators -->
            <template v-for="[slotKey, slotData] in hiddenEventsData" :key="`more-${slotKey}`">
              <button
                type="button"
                @click="handleMoreEventsClick(slotData.events, slotData.timeSlot.hour, slotData.timeSlot.minutes)"
                class="absolute bg-neptune-300 border border-white z-30 hover:z-50 rounded-md py-0.5 px-1 text-xs font-medium shadow-sm transition-all duration-200 "
                :style="calculateMoreEventsIndicatorPosition({ timeSlot: slotData.timeSlot, dayIndex: slotData.dayIndex, showIndicator: slotData.showIndicator}, isMobileView)"
              >
                +{{ slotData.count  }}
              </button>
            </template>
          </div>
        </div>
      </div>
    </UiScrollarea>

    <!-- Late events indicator -->
    <div
      v-if="lateEvents.length > 0"
      class="absolute z-50 right-0 pointer-events-none  bg-blue-50 w-10 ml-auto mr-3 text-gray-700 py-1 text-xs flex justify-center"
      :style="lateEventsIndicatorStyle()"
    >
      + {{ lateEvents.length }}
    </div>

    <!-- Modal for showing all events of a day -->
    <UiDialog :is-revealed="showDayModal" :no-padding-and-margin="true" class="relative z-50">
        <slot>
          <div class="bg-white rounded-md overflow-y-auto pb-2">
            <div class="asbolute top-0 lef-0 flex items-center mb-4 text-white px-4 pt-4">
              <span class="w-full flex justify-start text-black font-bold text-sm">{{ modalTitle }}</span>
              <button @click="showDayModal = false" class="bg-gray-200 rounded-full p-1 text-black self-end">
                <IconX class="h-4 w-4" aria-hidden="true" />
              </button>
            </div>
            <ul class="space-y-2 px-2">
              <template v-for="event in modalEvents.filter(e => e.isMultiDay)" :key="`modal-multi-${event.id}`">
                <li class="p-2 rounded bg-neptune-100 hover:bg-neptune-300">
                  <NuxtLink :to="{ name: 'events-id-details', params: { id: event.id }}" class="flex justify-between items-center">
                    <div class="font-bold text-black w-2/3 break-all">{{ event.title }}</div>
                    <div class="text-xs text-black opacity-90">{{ format(event.dateFrom, 'dd.MM') }} - {{ format(event.dateUpTo || event.dateFrom, 'dd.MM') }}</div>
                  </NuxtLink>
                </li>
              </template>
              <template v-for="event in modalEvents.filter(e => !e.isMultiDay)" :key="`modal-single-${event.id}`">
                <li class="p-2 rounded hover:bg-blue-50">
                  <NuxtLink :to="{ name: 'events-id-details', params: { id: event.id }}" class="flex justify-between items-center">
                    <div class="font-medium text-black w-2/3">{{ event.title }}</div>
                    <div class="text-xs opacity-90">{{ formatEventTimeRange(event) }}</div>
                  </NuxtLink>
                </li>
              </template>
            </ul>
          </div>
        </slot>
    </UiDialog>
  </div>
</template>

<script lang="ts" setup>
import { isEqual, isSameDay, isWithinInterval, startOfDay, endOfDay, format} from 'date-fns'
import {
  processEventsForColumnLayout,
  assignEventRows,
  calculateRegularEventPosition,
  calculateMultiDayEventPosition,
  calculateMoreEventsIndicatorPosition,
  getHiddenEventsData,
  formatEventTimeRange,
  formatEventEndTime,
  formatEventStartTime,
  eventIntersectsWeek,
  CalendarData,
  WEEK_VIEW_ROW_HEIGHT,
  MULTI_DAY_EVENT_HEIGHT
} from '@/composables/calendar-events'
import { uuid4 } from '@sentry/utils';

const {
  weekdays,
  weekDates,
  selectedDay,
  selectDay,
} = inject<CalendarData>('calendar-dates')

const props = defineProps<{
  events: ApiModel<'CalendarEvent'>[]
  allowCollapsebleDates?: boolean
  defaultStartHour?: number
  defaultEndHour?: number
}>()

const isMobileView = ref(window.innerWidth < 1024)
const hoveredEventId = ref<string | number | null>(null)
const scrollAreaRef = ref<HTMLElement>()
const defaultStartHour = computed(() => props.defaultStartHour ?? 7)
const defaultEndHour = computed(() => props.defaultEndHour ?? 19)
const scrollOffset = computed(()=> WEEK_VIEW_ROW_HEIGHT * defaultStartHour.value)
const activeScrollOffset = ref(scrollOffset.value)
const dayStart = computed(()=>startOfDay(weekDates.value[0]))
const dayEnd = computed(()=>endOfDay(weekDates.value[weekDates.value.length - 1]))

onMounted(() => {
  window.addEventListener('resize', handleResize)
  handleResize()
})

const showDayModal = ref(false)

const rowStylesCss = `height: ${WEEK_VIEW_ROW_HEIGHT}px`

function handleResize() {
  isMobileView.value = window.innerWidth < 1024
}

const modalEvents = ref<ApiModel<'CalendarEvent'>[]>([])
const modalTitle = ref<string>(null)

function handleMoreEventsClick(hiddenEventsData: ApiModel<'CalendarEvent'>[], hour: number, minutes: number) {
  showDayModal.value = true
  const date = hiddenEventsData[0].partDateFrom || hiddenEventsData[0].dateFrom
  const visibleEvents = regularEvents.value.filter(event => {
    const eventDate = event.partDateFrom || event.dateFrom
    return eventDate.getHours() === hour && eventDate.getMinutes() === minutes && isSameDay(eventDate, date)
  })
  modalEvents.value = [...hiddenEventsData, ...visibleEvents]
  modalTitle.value = `Termine für ${hour}:${minutes === 0 ? '00' : minutes} Uhr`
}

const maxVisibleNumberOfMultidayEvents = ref(20)

const multiDayEvents = computed(() => {
  if (!weekDates.value || weekDates.value.length === 0) return []

  const weekStart = weekDates.value[0]
  const weekEnd = weekDates.value[6]

  const multiEvents = props.events
    .filter(event => {
      return event.isMultiDay && eventIntersectsWeek(event, weekStart, weekEnd)
    }).map(event => useModelFactory('CalendarEvent').create(event))

  if(isMobileView.value) {
    const days = multiEvents.filter(event => {
      return isWithinInterval(selectedDay.value, {start: startOfDay(event.dateFrom), end: endOfDay(event.dateUpTo)})
    })
    return assignEventRows(days, weekDates.value, 'week')
  }

  return assignEventRows(multiEvents, weekDates.value, 'week')
})

const isMoreThanTwoRows = computed(() => {
  if (multiDayEvents.value.length > 0) {
   return !!multiDayEvents.value.find(event => {
      return event.weekViewRow > 1
    })
  }
  return false
})

const showMoreMultipleEvents = ref(false)

const showAllMultiDayEvents = () => {
  showMoreMultipleEvents.value = !showMoreMultipleEvents.value
}

const multiEventsCount = computed(() => {
  if (multiDayEvents.value.length > 0) {
    if(isMoreThanTwoRows && !showMoreMultipleEvents.value) {
      return 3
    }

    const maxRow = Math.max(...multiDayEvents.value.map(e => e.weekViewRow || 0))
    return maxRow + 1
  }
  return 0
})

const numberOfMultiEventsInOneDay = computed(() => {
  const numberOfEvents = []
  const numberOfInterations = isMobileView.value ? 1 : 7
  for(let i = 0; i < numberOfInterations; i++) {
    const dateToCompare = isMobileView.value ? selectedDay.value : weekDates.value[i]
    const events = multiDayEvents.value.
      filter(event => isWithinInterval(dateToCompare, {start: startOfDay(event.dateFrom), end: startOfDay(event.dateUpTo)}))
      .filter(event=> event.weekViewRow >1)

    let showEvents = events.filter(event=> event.weekViewRow > 1).length

    numberOfEvents[i] = {
      numberOfEventsOneDay: events.length,
      showEvents,
      id: uuid4()
    }
  }
  return numberOfEvents
})


const regularEvents = computed(() => {
  const events = props.events.filter(
    event => !event.isMultiDay
  ).map(event => useModelFactory('CalendarEvent').create(event))

  const processed = processEventsForColumnLayout(events, weekDates.value)

  if (isMobileView.value) {
    return processed.filter(event => {
      const dayIndex = weekDates.value.findIndex(day =>
        isSameDay(event.partDateFrom || event.dateFrom, day)
      )
      return dayIndex !== -1 &&
        isSameDay(selectedDay.value, weekDates.value[dayIndex])
    })
  }

  return processed
})


// Get hidden events data for indicators
const hiddenEventsData = computed(() => {
  const events = props.events.filter(event => !event.isMultiDay)
  return getHiddenEventsData(events, weekDates.value, selectedDay.value)
})



// Numbers of events that are before defaultStartHour
const earlyEvents = computed(() => {
  return regularEvents.value.filter(event => {
    const eventDate = event.partDateFrom || event.dateFrom
    const eventEndDate = event.partDateUpTo || event.dateUpTo
    return eventDate.getHours() < defaultStartHour.value
      && eventEndDate.getHours() < defaultStartHour.value
      && isWithinInterval(eventDate, {start: dayStart.value, end: dayEnd.value})
  })
})

// Numbers of events that are after  defaultEndHour
const lateEvents = computed(() => {
  return regularEvents.value.filter(event => {
    const eventDate = event.partDateFrom || event.dateFrom
    return eventDate.getHours() >= defaultEndHour.value
      && isWithinInterval(eventDate, {start: dayStart.value, end: dayEnd.value})
  })
})

// Used with show/hide events indicators
function updateActiveScrollOffset(offset: number) {
  activeScrollOffset.value = offset
}

const indicatorOpacityOffset = ref(50) // offset for indicators for better UX
function earlyEventsIndicatorStyle() {
  return {
    top: `${multiEventsCount.value * MULTI_DAY_EVENT_HEIGHT + 12}px`,
    opacity: activeScrollOffset.value >= scrollOffset.value - indicatorOpacityOffset.value ? '1' : '0',
    transition: 'all 0.5s ease',
  }
}

function lateEventsIndicatorStyle() {
  return {
    top: `${WEEK_VIEW_ROW_HEIGHT * (defaultEndHour.value - defaultStartHour.value)}px`,
    opacity: activeScrollOffset.value <= scrollOffset.value + indicatorOpacityOffset.value ? '1' : '0',
    transition: 'all 0.5s ease',
  }
}
</script>
