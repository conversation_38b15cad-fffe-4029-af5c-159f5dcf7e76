<template>
    <AddressCustom :address="address" />
</template>

<script setup lang="ts">
    const props = defineProps<{
        location: ApiModel<'AddressLocation'>
    }>()

    const address = computed(() => {
        const line1 = [props.location.street, props.location.streetnumber].filter((value) => !!value).join(' ')
        const line2 = [props.location.zipCode, props.location.city].filter((value) => !!value).join('  ')
        const line3 = [props.location.country].filter((value) => !!value)

        return [line1, line2, line3].filter((value) => !!value.length).join('\n')
    })
</script>
