// eslint-disable-next-line no-undef
module.exports = {
    root: true,
    env: {
        browser: true,
        es2021: true,
        node: true
    },
    extends: [
        'plugin:vue/vue3-essential',
        'eslint:recommended',
        'plugin:@typescript-eslint/eslint-recommended',
        'plugin:@typescript-eslint/recommended',
        'prettier'
    ],
    parser: 'vue-eslint-parser',
    parserOptions: {
        parser: {
            'js': 'espree',
            'ts': '@typescript-eslint/parser',
            '<template>': 'espree'
        },
        sourceType: 'module',
        ecmaVersion: 2020
    },
    plugins: ['vue', '@typescript-eslint'],
    rules: {
        //the rules that should be turned on (deleted) and fixed
        '@typescript-eslint/no-unused-vars': 'off',
        '@typescript-eslint/no-explicit-any': 'off',
        '@typescript-eslint/no-var-requires': 'off',
        '@typescript-eslint/no-extra-semi': 'off',
        '@typescript-eslint/ban-types': 'off',
        '@typescript-eslint/no-unnecessary-type-constraint': 'off',
        '@typescript-eslint/no-empty-interface': 'off',
        'vue/multi-word-component-names': 'off',
        'vue/no-mutating-props': 'off',
        'vue/no-setup-props-destructure': 'off',
        'vue/require-valid-default-prop': 'off',
        'vue/require-v-for-key': 'off',
        'vue/no-v-text-v-html-on-component': 'off',
        'vue/no-parsing-error': 'off',
        'no-undef': 'off',
        'no-extra-boolean-cast': 'off',
        'no-prototype-builtins': 'off',
        'no-irregular-whitespace': 'off',
        //rules not to delete
        'vue/max-attributes-per-line': 'off'
    }
}
