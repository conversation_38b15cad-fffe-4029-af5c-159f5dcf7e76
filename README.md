# DRKServer Ereignismanagement Light (EM-Light)

This is a Nuxt3 3 project. We recommend to look at the [documentation](https://v3.nuxtjs.org).

## Setup

### NVM & Node versions

We use strict-engine mode for Node, npm and yarn. It`s recommended to install the needed node version with [nvm](https://github.com/nvm-sh/nvm).

It's also recommended to configure your shell environment so that your system switches node version [automatically when you enter a directory that contains an .nvmrc file](https://stackoverflow.com/questions/23556330/run-nvm-use-automatically-every-time-theres-a-nvmrc-file-on-the-directory). (Which is the case here).

More information on this: https://github.com/nvm-sh/nvm#zsh

### Dependencies

Make sure to install the dependencies

```bash
yarn install
```

### IDE & Editors

#### VS Code

It's assumed that Visual Studio code will be used for development. All settings (extensions, linter configurations etc.) should be applied to the current workspace. The files in [.vscode](.vscode/settings.json) are versioned and are part of this project.

To properly use autocomplete in .ts and .vue files, [Volar](https://github.com/johnsoncodehk/volar) should run in take-over mode. For this, **Vetur must be disabled in the project**.

How to achieve this: [Disable Vetur](https://github.com/johnsoncodehk/volar/discussions/471)

#### Vim & NeoVIM

The [COC Plugin](https://github.com/neoclide/coc.nvim) is recommended for Vim and NeoVIM with the following extensions:

```
coc-tailwindcss3, coc-prettier, coc-tsserver, coc-json, coc-html, coc-volar, coc-volar-tools
```

The Project Configuration in `.vim.coc-settings.json` should be applied automatically.


## Configuration

The most important aspects of the application can be controlled via environment variables.

By default, the application is configured to make requests to the demo API.

-   Keycloak: https://login.drkserver.org/auth/realms/**drkdemo**
-   API: https://demo-api.drkserver.org

The list of possible .env variables can be seen in this file:

-   [.env.example](.env.example)

> 🗝
> For authentication with Keycloak a client secret must be set in `KEYCLOAK_CLIENT_SECRET`. This key is MUST NOT be part of the source code project and therefore has to be deposited by each developer herself. Please refer to this [Keeper-Entry](https://keepersecurity.eu/vault/#detail/kfVYLqvuNKG0NWVs4ubcig)

### Sessions & Redis

Sessions are written to an [LRU cache](https://github.com/isaacs/node-lru-cache) by default. This store is rebuilt after each server restart, so sessions are lost in the process.

In production environments, Redis should be used to store sessions. The adapter is automatically activated if `REDIS_URL` is set in the environment.

## Development

Create a local `.env` file first

```bash
cp .env.develop .env
```

Then replace 'OUR SECRET' with value from this [Keeper-Entry](https://keepersecurity.eu/vault/#detail/kfVYLqvuNKG0NWVs4ubcig) in `.env`

```bash
KEYCLOAK_CLIENT_ID=OUR-SECRET
```

🔥 Start the development server on http://localhost:3000

```bash
yarn dev
```

### Alternatives

If you want to start the server on another domain or port, you have to actively announce this to the application. This can be done bey setting `BASE_URL`

_Example_: Start development server on port 8080

```bash
BASE_URL=http://localhost:8080 yarn dev -p 8080
```

_Example_: Start development server _without_ .env configurations

```bash
KEYCLOAK_CLIENT_ID=OUR-SECRET yarn dev
```

### Use Redis during development

Although not an absolute necessity, it is recommended to install Redis locally and enable it in the application.

```bash
REDIS_URL = 127.0.0.1:6379
```

## Important Modules

-   [~/modules/sentry](modules/sentry/README.md)
-   [~/modules/auth](modules/auth/README.md)
-   [~/modules/api](modules/api/README.md)

## Deployment

### Testing

A preview system is spawned for each active branch. This happens via GitLab CI pipelines. Please refer to these files and directories for further details:

-   [.gitlab-ci.yml](.gitlab-ci.yml)
-   [deployment/docker/](deployment/docker/)

Preview domains are created according to this pattern: https://[BRANCH-NAME].drkserver.mogic-server.de

Examples:

-   Branch develop: https://develop.drkserver.mogic-server.de
-   Branch main: https://main.drkserver.mogic-server.de

All instances are secured by basic auth:

> 🥳 drkserver 🗝 XF64HkzgbF5fqpzn

#### Environment variables

The domain is automatically added to the environment as BASE_URL. All other variables are synced from [Keeper](https://keepersecurity.eu/vault/#folder/fPSv-N9hI5aRO2nyJDkZwA) to Vault and provided during deployment.

-   See: [env-template](deployment/templates/env-template)

### DRK-Systems

-   A branch sync from Mogic-Gitlab to DRK-Gitlab is set up for the ```stable``` branch.
- To deploy the application in one of the DRK-systems, an Merge Request must be performed on the DRK-Gitlab in one of these branches or the CI can be triggered manually:
    - production
    - training
    - stage
    - demo

#### Sentry tunnel

The tunnel option must be used for the DRK-Sentry. The Sentry project ID is permanently set in the backend routing. A different Sentry project can therefore only be set by adjusting the CI variable SENTRY_DSN on the DRK-Gitlab and also adjusting the routing in the backend.
