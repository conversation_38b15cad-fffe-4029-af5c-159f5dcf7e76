{"editor.formatOnSave": true, "files.associations": {"*.ts": "typescript"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "tailwindCSS.experimental.configFile": "modules/ui/tailwind.config.js", "tailwindCSS.classAttributes": ["class", "className", "outer-class", "wrapper-class", "label-class", "input-class", "message-class", "messages-class"], "tailwindCSS.includeLanguages": {}, "vue3snippets.enable-compile-vue-file-on-did-save-code": false}