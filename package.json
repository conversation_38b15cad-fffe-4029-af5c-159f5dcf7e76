{"private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "typecheck": "nuxt typecheck", "start": "node .output/server/index.mjs", "lint": "eslint ./ --ext .ts,.vue,.js", "lint:fix": "eslint ./ --ext .ts,.vue,.js --fix", "format": "prettier --write ."}, "engines": {"npm": ">=8.1.0", "node": ">=20.0.0", "yarn": "^1.22.0"}, "devDependencies": {"@headlessui/vue": "^1.7.11", "@popperjs/core": "^2.11.5", "@sentry/browser": "^6.19.6", "@sentry/node": "^6.19.6", "@types/lru-cache": "^7.6.1", "@types/node": "^18.14.6", "@types/tailwindcss": "^3.0.10", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^5.27.1", "@typescript-eslint/parser": "^5.27.0", "@vueuse/components": "^7.6.2", "@vueuse/core": "10", "@vueuse/integrations": "^8.9.4", "@vueuse/nuxt": "^7.6.2", "camelcase": "^6.0.0", "console": "^0.7.2", "defu": "^6.0.0", "eslint": "^8.17.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-vue": "^9.1.0", "fuse.js": "^6.6.2", "ioredis": "^5.0.4", "lru-cache": "^7.8.1", "nuxt": "^3.6.5", "openid-client": "^5.2.1", "p-memoize": "^6.0.1", "postcss-inline-svg": "^5.0.0", "prettier": "^2.8.4", "prettier-plugin-tailwindcss": "^0.2.4", "rimraf": "^3.0.2", "typescript": "^4.9.5", "typescript-require": "^0.3.0", "ufo": "^0.8.3", "uuid": "^8.3.2", "vue-tsc": "^1.2.0"}, "dependencies": {"@headlessui/tailwindcss": "^0.1.2", "@lukemorales/query-key-factory": "^1.2.0", "@nuxt/kit": "^3.2.3", "@nuxtjs/tailwindcss": "^6.6.6", "@sentry/vite-plugin": "^2.3.0", "@sentry/vue": "^7.56.0", "@tailwindcss/forms": "^0.5.2", "@tanstack/vue-query": "^4.26.1", "@types/lodash-es": "^4.17.6", "@types/luxon": "^3.3.0", "@vue/compiler-dom": "^3.2.37", "@vuelidate/core": "^2.0.0", "@vuelidate/validators": "^2.0.0", "console-stamp": "^3.1.2", "date-fns": "^2.28.0", "h3-zod": "^0.4.2", "ical-generator": "^4.0.0", "just-flatten-it": "^5.2.0", "just-merge": "^3.2.0", "just-order-by": "^1.0.0", "lodash-es": "^4.17.21", "luxon": "^3.3.0", "overlayscrollbars": "^2.2.0", "overlayscrollbars-vue": "^0.5.1", "reka-ui": "^2.3.1", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "tailwindcss-animate": "^1.0.6", "vue-virtual-scroller": "^2.0.0-beta.8", "vuedraggable": "^4.1.0", "zod": "^3.21.4"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}