type DependentType = { id: number; name: string }

/**
 * The expected type of `module`, `category` and `type`
 *
 * The hook "useTechnicDependentTypes" expects getters for `module`, `type` and `category`
 * as an argument. These are used for the retrievung the respective "computed property".
 *
 * The hook "useTechnicDependentTypes" can be provided with setters for `module`, `type` and `category`
 * as an argument. These are used for updates on the "computed property".
 *
 * The setter is optional.
 */
type ComputedOption<T> = {
    get: (collection: T[]) => T | null
    set?: (type: T | null) => void
}

type HookOptions<T extends any> = {
    module: ComputedOption<T>
    type: ComputedOption<T>
    category: ComputedOption<T>
}

/**
 * Retrieve Modul/Art/Typ for the given event post
 *
 * It looks complicated because it is complicated.
 *
 * "TechnicDependTypes" is a nested data structure that looks like this:
 * ```
 * {
 *   id: number
 *   name: string
 *   unitTypes: {
 *      id: number
 *      name: string
 *      unitCategories: {
 *          id: number
 *          name: string
 *      }[]
 *   }[]
 * }[]
 * ```
 *
 * For event posts the entries for module, type, category are stored in the fields
 * "technicArticleTypes", "technicArticleUnitTypes" and "technicArticleUnitCategories".
 * Namely as numeric ID within an array. Something like: `{ technicArticleTypes: [23323] }`
 *
 * TechnicArticles are provided with attributes `type` (module), `unitType` (type) and `unitCategory` (category).
 * These values are stored as a data structure like: { id: 123, name: "Rettungshund" }
 *
 * ---
 *
 * The hook "useTechnicDependentTypes" expects getters and  (optional) setters for `module`, `type` and `category`
 * as an argument. These are used for retrieving and updating the value of the respective "computed property".
 *
 * Each getter is provided with an argument containing the collection
 * of available entries, so that the matching entry can be identified.
 *
 */
export function useTechnicDependentTypes({ module, type, category }: HookOptions<DependentType>) {
    /**
     * The date is fetched on startup within "Inventory" plugin and
     * can be accessed by `useNuxtApp().$dependentTypes`
     */
    const { $dependentTypes: data } = useNuxtApp()

    const moduleState = computed<DependentType>({
        get: () => module.get(modules.value),
        set: (value) => (module.set ? module.set(value) : null)
    })

    const typeState = computed<DependentType>({
        get: () => type.get(types.value),
        set: (value) => (type.set ? type.set(value) : null)
    })

    const categoryState = computed<DependentType>({
        get: () => category.get(categories.value),
        set: (value) => (category.set ? category.set(value) : null)
    })

    const modules = computed(() => {
        return data.value || []
    })

    const types = computed(() => {
        return modules.value?.find(({ id }) => id === unref(moduleState)?.id)?.unitTypes || []
    })

    const categories = computed(() => {
        return types.value?.find(({ id }) => id === unref(typeState)?.id)?.unitCategories || []
    })

    watch(moduleState, () => {
        typeState.value = null
        categoryState.value = null
    })

    watch(typeState, () => {
        categoryState.value = null
    })

    return {
        module: moduleState,
        modules,
        type: typeState,
        types,
        category: categoryState,
        categories
    }
}
