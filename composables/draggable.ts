import { MaybeRef, unrefElement, useEventListener } from '@vueuse/core'

/**
 * @Todo At the moment, everything here refers to "SigningUps". But it doesn't have to stay that way.
 */

/**
 * This is the "system wide" state
 */
const dragData = ref<ApiModel<'SigningUp'>>(null)

const currentDraggable = ref<HTMLElement>(null)

const currentDropzone = ref<HTMLElement>(null)

/**
 * useDraggable
 *
 * Use this to attach draggable behaviour to a HTML element.
 *
 * This hook returns an template reference that should be applied to an HTML element.
 * Visual effects can be applied via stylesheets. Examples:
 *
 * ```
 * .elClass[data-ui-draggable-state~=active] {
 *    @apply opacity-40
 * }
 * ```
 *
 * @param signingUp
 */
export function useDraggable(signingUp: MaybeRef<ApiModel<'SigningUp'>>) {
    const element = ref<HTMLElement>()

    onMounted(() => {

        // Check if it's a touch device. We do not want to have draggable on touch devices like mobile
        const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

        element.value?.setAttribute('draggable', isTouchDevice ? 'false' : 'true')
    })

    const draggableState = computed(() => {
        const states = []

        if (currentDraggable.value === unrefElement(element)) {
            states.push('active')
        }
        return states
    })

    watchEffect(() => {
        element.value?.setAttribute('data-ui-draggable-state', draggableState.value.join(' '))
    })

    useEventListener(element, 'dragstart', (event: DragEvent) => {
        dragData.value = unref(signingUp)
        currentDraggable.value = element.value


        // Calculate the offset relative to where the user clicked
        const rect = element.value.getBoundingClientRect()
        const offsetX = event.clientX - rect.left
        const offsetY = event.clientY - rect.top


        // custom drag image (via cloning) should ensure consistent behavior and appearance across various browsers
        const clone = currentDraggable.value?.cloneNode(true) as HTMLElement
        clone.style.position = 'absolute'
        clone.style.pointerEvents = 'none'
        clone.style.top = `${rect.top}px`  // Adjust based on element's top
        clone.style.left = `${rect.left}px`  // Adjust based on element's left
        clone.style.width = `${element.value.offsetWidth}px`
        clone.style.height = `${element.value.offsetHeight}px`
        clone.style.opacity = '0.7'

        document.body.appendChild(clone)

        // Calculate center position
        // const centerX = element.value.offsetWidth / 2
        // const centerY = element.value.offsetHeight / 2

        event.dataTransfer?.setDragImage(clone, offsetX, offsetY)
        // Remove cloned child after calling setDragImage to not leave it in
        setTimeout(() => {
            document.body.removeChild(clone)
        }, 0)
    })

    useEventListener(element, 'dragend', (event: DragEvent) => {
        dragData.value = null
        currentDraggable.value = null
        currentDropzone.value = null
    })

    return element
}

type DropzoneState =
    | 'idle' // No sigining  up is being dragged
    | 'enabled' // A "valid" signing up is being dragged
    | 'active' // A valid siging up is dragged over the dropzone
    | 'disabled' // A "invalid" signing up isbeing  dragged

type UseDropzoneInput = {
    onValidate?(signingUp: ApiModel<'SigningUp'>): boolean
    onDrop(signingUp: ApiModel<'SigningUp'>): Promise<any>
    debug?: boolean
}

/**
 * UseDropzone
 *
 * Use this to attach dropzone behaviour to a HTML element.
 *
 * This hook returns an template reference that should be applied to an HTML element.
 *
 * Visual effects can be applied via stylesheets. Examples:
 *
 * ```
 * .elClass[data-ui-dropzone-state~=disabled] {
 *    @apply opacity-40
 * }
 *
 * .elClass[data-ui-dropzone-state~=active] {
 *    @apply bg-gold-200
 *  }
 * ```
 *
 * Important: Pointer events have to be disabled when this dropzone is enabled.
 * Otherwise we cannot detect `active` state.
 *
 * ```
 * :where([data-ui-dropzone-state~=enabled]) > * {
 *      pointer-events: none;
 * }
 * ```
 */
export function useDropzone({ onDrop, onValidate = () => true, debug = false }: UseDropzoneInput) {
    const element = ref<HTMLElement>()

    const _dropzoneState = computed<DropzoneState>(() => {
        if (dragData.value !== null) {
            return onValidate(dragData.value) ? 'enabled' : 'disabled'
        }

        return 'idle'
    })

    const dropzoneState = computed<DropzoneState[]>(() => {
        const states = [_dropzoneState.value]

        if (_dropzoneState.value === 'enabled' && currentDropzone.value === unrefElement(element)) {
            states.push('active')
        }
        return states
    })

    watchEffect(() => {
        unrefElement(element)?.setAttribute('data-ui-dropzone-state', dropzoneState.value.join(' '))
    })

    useEventListener(element, 'dragover', (event: DragEvent) => {
        if (_dropzoneState.value === 'enabled') {
            // prevent default to allow drop
            event.preventDefault()
        }
    })

    useEventListener(element, 'dragenter', (event: DragEvent) => {
        currentDropzone.value = unrefElement(element)
    })

    useEventListener(element, 'dragleave', (event: DragEvent) => {
        currentDropzone.value = null
    })

    useEventListener(element, 'drop', (event: DragEvent) => {
        if (_dropzoneState.value === 'enabled') {
            event.preventDefault()
            onDrop(dragData.value)
        }
    })

    return element
}
