import { MaybeRef } from '@vueuse/core'
import Status from '~~/enums/event-signing-up-status'

/**
 * Get current status of users signing up and provide method for changing that.
 *
 * @param input  ApiModel<"Event"> | Ref<ApiModel<'Event'>>
 * @param periodsDialog
 */
export const useMyEventSignup = function (input: MaybeRef<ApiModel<'Event'>>, periodsDialog = useDialogController('selectSigningUpPeriods')) {
    const event = computed(() => unref(input))

    const { retrieveCodeEntry, retrieveStatus, retrieveAssignment, updateSigningUpModel } = useSigningUpStates()

    const { $user } = useNuxtApp()
    const signingUp = computed(() => {
        return (
            event.value.mySigningUp ??
            useModelFactory('SigningUp').create({
                status: retrieveCodeEntry(Status.UNKNOWN),
                periods: [],
                resource: useModelFactory('Resource').create({
                    type: 'PERSONAL',
                    external: false,
                    member: $user.basicData
                })
            })
        )
    })

    const status = computed(() => {
        return retrieveStatus(signingUp.value)
    })

    const assignment = computed(() => {
        return retrieveAssignment(signingUp.value)
    })

    const remark = computed(() => {
        return signingUp.value.remark || null
    })

    const periods = computed(() => {
        return signingUp.value.periods || []
    })

    const { createSigningUp } = useEventMutations(event)

    async function updateStatus(status: Status) {
        switch (status) {
            case Status.UNAVAILABLE:
            case Status.AVAILABLE: {
                await createSigningUp(
                    // Update signing up with matching code entry
                    updateSigningUpModel(signingUp.value, status)
                )
                break
            }

            case Status.PARTIAL: {
                const { data: periods, isCanceled } = await periodsDialog.reveal(signingUp.value)

                if (!isCanceled) {
                    await createSigningUp(
                        updateSigningUpModel(
                            // Update signing up with matching code entry
                            signingUp.value,
                            status,
                            periods
                        )
                    )
                }
                break
            }
        }
    }

    return {
        status,
        remark,
        assignment,
        periods,
        signingUp,
        periodsDialog,
        updateStatus
    }
}
