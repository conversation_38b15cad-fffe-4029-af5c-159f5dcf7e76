import type { EventFilter } from '~~/composables/event-filter'
import { pickBy } from 'lodash-es'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { Ref } from 'vue'
import { useOffsetPagination } from '@vueuse/core'

/**
 * Return filter as simple params that can be handled by our API
 *
 * @param filter
 */
export const toParams = function (filter: EventFilter) {
    /**
     * We apply many filters locally. This means that we do not need to trigger a request to the API.
     *
     * !! This is not the case anymore, so we could probably remove this check.
     *
     * These are the filters that we currently want the server to calculate:
     */
    const allowedFilterParams = [
        'start',
        'end',
        'type',
        'extendedDescription',
        'executedByMyOrganisations',
        'executedBy',
        'withOpenEventPosts',
        'responseStatus',
        'assignedToEventPost',
        'assignedToEventPostWithExtendedPermissions',
        'responsibleFor',
        'withDecisionsOfChainOfCommand',
        'registratorFor',
        'status',
        'limit',
        'eventTagSearchCriteria'
    ]

    type AllowedApiParameter = (typeof allowedFilterParams)[number]

    function isAllowedApiParameter(param: string): param is AllowedApiParameter {
        return allowedFilterParams.includes(param)
    }

    /**
     * Filter all empty values and omit parameters that have no effect
     */
    const sanitized: EventFilter<'params'> = pickBy(filter, function (value, key) {
        if (!!!value) {
            return false
        }

        if (!isAllowedApiParameter(key)) {
            return false
        }

        return !(key === 'responseStatus' && value === 'ALL')
    })

    /**
     * Extract ids from organizations for a clean list of params
     *
     * (Will be transformed back into an array in our api handler if needed)
     */

    return sanitized
}

/**
 * Get or create the global page state
 */
export const useEventListPage = function () {
    return useState<number>('my-current-page', () => 1)
}

/**
 * Get or create the page size state
 */
const usePageSize = function () {
    return useState<10 | 20 | 50>('my-current-page-size', () => 10)
}

/**
 * Retrieve all events
 *
 * When no filter is set we'll use event filters from state
 *
 * @param filterInput Optional filter
 */
export const useEventList = function (filterInput?: Ref<EventFilter>) {
    const filter = filterInput ? filterInput : useEventFilters()

    const page = useEventListPage()
    const pageSize = usePageSize()
    const offset = computed(() => {
        return Math.max(0, page.value - 1) * pageSize.value
    })

    /**
     * Transform filters to a list of allowed API parameters so that we can generate useful query keys
     */
    const params = computed(() => {
        return {
            offset: unref(offset),
            limit: unref(pageSize),
            ...toParams(filter.value),
        }
    })

    const { data, suspense, ...queryResult } = useQuery({
        ...queries.events.list(params),
        staleTime: 10 * 60 * 1000, // 10 Minutes is more than cache time
        keepPreviousData: true,
        retry: (failureCount: number, error: any) => {
            const status = error?.response?.status
            if (status === 403 || status === 410) return false
            return failureCount < 3
        }
    })

    const events = computed(() => {
        return unref(data)?.items || []
    })

    const totalItems = computed(() => {
        return unref(data)?.totalItems || 0
    })

    const pagination = computed(() => {
        return useOffsetPagination({ total: totalItems, page, pageSize })
    })

    /**
     * Reset page when filter changes
     */
    watch(filter, () => (page.value = 1), { deep: true })

    return {
        events,
        filter,
        page,
        pageSize,
        ...unref(pagination),
        ...queryResult
    }
}

/**
 * What is that needed for?
 *
 * @todo Check it
 */
export const useEventSearch = function () {
    return useMutation(async ({ query, limit }: QueryVariablesType) => {
        const payload = await $fetch<{ items: ApiModel<'Event'>[] }>(`/api/events`, {
            query: {
                offset: 0,
                limit,
                searchTerm: query
            },
            method: 'get'
        })

        return payload.items
    })
}
