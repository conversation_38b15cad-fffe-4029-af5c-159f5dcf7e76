import type { Ref } from 'vue'
import { useOffsetPagination } from '@vueuse/core'
import { range } from 'lodash-es'

/**
 * Paginate the incoming items array
 *
 * @param items
 * @param pageSize
 * @param page
 */
export const usePagination = function (items: Ref<any[]>, pageSize: Ref<number> | number = 10, page = 1) {
    const total = computed(() => unref(items)?.length || 0)

    const { currentPage, currentPageSize, pageCount, isFirstPage, isLastPage, prev, next } = useOffsetPagination({ total, page, pageSize })

    const chunk = computed(() => {
        const start = (currentPage.value - 1) * currentPageSize.value
        const end = start + currentPageSize.value
        return (unref(items) || []).slice(start, end)
    })

    return {
        currentPage,
        currentPageSize,
        pageCount,
        isFirstPage,
        isLastPage,
        prev,
        next,
        chunk
    }
}

/**
 * Get a list of pages that should be part of a pagination control
 *
 * The idea was taken from Laravel/Illuminate
 *
 * @see https://github.com/laravel/framework/blob/6.x/src/Illuminate/Pagination/UrlWindow.php
 *
 * @param currentPage
 * @param pageCount
 * @param onEachSide
 */
export const usePaginatorLinks = function (currentPage: Ref<number>, pageCount: Ref<number>, onEachSide = 2) {
    const window = computed(() => onEachSide * 2)

    const slider = computed(() => {
        // If the current page is very close to the beginning of the page range, we will
        // just render the beginning of the page range, followed by the last of the
        // links in this list, since we will not have room to create a full slider.
        if (currentPage.value <= window.value) {
            return range(1, Math.min(window.value + 1, pageCount.value) + 1)
        }

        // If the current page is close to the ending of the page range we will just get
        // this first couple pages, followed by a larger window of these ending pages
        // since we're too close to the end of the list to create a full on slider.
        if (currentPage.value > pageCount.value - window.value) {
            return range(Math.max(1, pageCount.value - window.value), pageCount.value + 1)
        }

        // If we have enough room on both sides of the current page to build a slider we
        // will surround it with both the beginning and ending caps, with this window
        // of pages in the middle providing a Google style sliding paginator setup.
        return range(Math.max(1, currentPage.value - onEachSide), Math.min(pageCount.value, currentPage.value + onEachSide) + 1)
    })

    const hasPrev = computed(() => {
        return currentPage.value > 1
    })

    const hasNext = computed(() => {
        return currentPage.value < pageCount.value
    })

    const showFirst = computed(() => {
        return currentPage.value > 2
    })

    const showLast = computed(() => {
        return currentPage.value < pageCount.value - 1
    })

    return {
        showFirst,
        hasPrev,
        slider,
        hasNext,
        showLast
    }
}
