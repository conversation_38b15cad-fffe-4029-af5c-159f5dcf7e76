import { MaybeRef } from '@vueuse/core'
import { uniqueId } from 'lodash-es'

type UiNotificationType = 'neutral' | 'error' | 'success' | 'default' | 'waiting' | 'cancel'

export type UiNotification = {
    id: string
    type: MaybeRef<UiNotificationType>
    content: MaybeRef<string>
    isFinal?: MaybeRef<boolean>
    cb?: () => void
}

/**
 * Use the notification stack when you want to display global notifications
 */
export const useNotifications = () => {
    const stack = useState<UiNotification[]>('notificaions-state', () => [])

    function dismiss(id: UiNotification['id']) {
        stack.value = stack.value.filter((item) => item.id !== id)
    }

    return {
        schedule: function (type: UiNotification['type'] = 'neutral', message?: string, cb?: () => void) {
            const notification = {
                id: uniqueId('notification_'),
                type: ref(type),
                content: ref(message || null),
                isFinal: ref(false),
                cb
            }

            return {
                show: (message: string = null, type: UiNotificationType = null, pinToBottom = false) => {
                    if (message) {
                        notification.content.value = message
                    }
                    notification.type.value = type || 'neutral'
                    if (pinToBottom) {
                        stack.value.push(notification)
                    } else {
                        stack.value.unshift(notification)
                    }
                },
                makeSuccess: (message?: string) => {
                    if (message) {
                        notification.content.value = message
                    }
                    notification.isFinal.value = true
                    notification.type.value = 'success'
                },
                makeError: (message?: string) => {
                    if (message) {
                        notification.content.value = message
                    }
                    notification.isFinal.value = true
                    notification.type.value = 'error'
                },
                makeFinal: (message?: string) => {
                    if (message) {
                        notification.content.value = message
                    }
                    notification.isFinal.value = true
                },
                hide: () => {
                    dismiss(notification.id)
                },
                setCallback: (callback: () => void) => {
                    notification.cb = callback
                }
            }
        },

        push: function (input: Partial<UiNotification>) {
            const notification: UiNotification = {
                id: input.id ?? uniqueId('notification_'),
                type: input.type ?? 'default',
                content: input.content,
                isFinal: input.isFinal ?? true
            }
            stack.value.push(notification)
        },

        dismiss,

        clear() {
            stack.value = []
        },

        stack
    }
}

export function useRunWithNotification() {
    const { schedule } = useNotifications()

    return async function <T extends any>(
        method: () => Promise<T>,
        messages: { pending: string; error: string; success?: string }
    ): Promise<T | Error> {
        const message = schedule()

        message.show(messages.pending, 'waiting')

        try {
            const result = await method()

            if (messages.success) {
                message.makeSuccess(messages.success)
            } else {
                message.hide()
            }

            return result
        } catch (error) {
            message.makeError(messages.error)
            return error
        }
    }
}

export function useRunWithProgressNotification() {
    const { schedule } = useNotifications()

    return async function <T extends any>(
        items: T[],
        handler: (item: T, index: number) => Promise<any>,
        messages: {
            progress?: (done: number, total: number) => string
            success?: string
            error: (done: number, total: number) => string
            canceled?: (done: number, total: number) => string
            additionalError?: (done: number, total: number) => string
        }
    ) {
        const total = items.length
        const errors: any[] = []
        const canceled: any[] = []
        const results: any[] = []

        const perItemMessages = items.map(() => schedule())

        for (let i = 0; i < total; i++) {
            const message = perItemMessages[i]
            const progressText = messages.progress?.(i + 1, total) ?? `Element ${i + 1}/${total} wird kopiert`
            const errorText = messages.error?.(i + 1, total) ?? `Element ${i + 1}/${total} konnte nicht kopiert werden`
            const canceledText = messages.canceled?.(i + 1, total) ?? `Element ${i + 1}/${total} wurde abgebrochen`
            const additionalDataCancelText = messages.additionalError?.(i + 1, total) ?? `Element ${i + 1}/${total}. Beim Kopieren der zusätzlichen Daten ist ein Fehler aufgetreten`

            message.show(progressText, 'waiting')

            try {
                const result = await handler(items[i], i)
                if (result === null || result === undefined) {
                    canceled.push(items[i])
                    message.makeError(canceledText)
                } else {
                    results.push(result)
                    message.makeSuccess(progressText)
                }
            } catch (err: any) {
                if (err?.name === 'AbortError') {
                    canceled.push(items[i])
                    message.makeError(canceledText)
                } else if (err?.name === 'CopyAdditionalDataError') {
                    canceled.push(items[i])
                    message.makeError(additionalDataCancelText)
                } else {
                    errors.push(err)
                    message.makeError(errorText)
                }
            }
        }

        return { results, errors, canceled }
    }
}

