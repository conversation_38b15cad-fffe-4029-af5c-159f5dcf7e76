import { DateTime, Interval } from 'luxon'

type IntervalLike = { start: Date | DateTime; end: Date | DateTime }

export function toIntervalObject(intervalLike: IntervalLike) {
    return Interval.fromDateTimes(intervalLike.start, intervalLike.end)
}

export function mergeIntervals(...intervals: { start: Date; end: Date }[]) {
    return (
        Interval.merge(
            // Transform to luxon intervals
            intervals.map(toIntervalObject)
        )
            // Transform back to object of JS Dates
            .map(({ start, end }) => ({
                start: start.toJSDate(),
                end: end.toJSDate()
            }))
    )
}

/**
 * A generator that walks through all days of an interval
 */
export function* eachDayOfInterval(input: IntervalLike) {
    const interval = toIntervalObject(input)

    let cursor = interval.start.startOf('day')
    while (cursor < interval.end) {
        yield cursor.toJSDate()
        cursor = cursor.plus({ days: 1 })
    }
}

export function daysOfInterval(input: IntervalLike) {
    return Array.from(eachDayOfInterval(input))
}
