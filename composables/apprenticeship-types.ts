import { useQuery } from '@tanstack/vue-query'

export const useApprenticeshipTypes = function () {
    return useQuery(
        ['apprenticeship-types'],
        async () => {
            return await $fetch<object>(`/api/apprenticeship-types`)
        },
        {
            select: (data: ApiModelProps<'Apprenticeship'>[]) => useModelFactory('Apprenticeship').from<PERSON>son(data),
            staleTime: Infinity,
            cacheTime: Infinity,
            placeholderData: []
        }
    )
}
