import { MaybeRef } from '@vueuse/core'
import { every } from 'lodash-es'

type EventPostFilter = {
    descriptionContains: string | null
    resourceTypeEquals: 'TECHNIC' | 'PERSONAL' | null
    isIncomplete: boolean
}

export function useEventPostFilter(input: MaybeRef<ApiModel<'EventPost'>[]>) {
    const eventPostFilter = reactive<EventPostFilter>({
        descriptionContains: null,
        resourceTypeEquals: null,
        isIncomplete: false
    })

    const eventPosts = computed(() => {
        return unref(input) ?? []
    })

    /**
     * This filters by
     */
    const filteredEventPosts = computed(() => {
        return eventPosts.value.filter(({ description, desiredValue, currentValue }) => {
            return every([
                !eventPostFilter.descriptionContains || description.toLowerCase().includes(eventPostFilter.descriptionContains.toLowerCase()),
                !eventPostFilter.isIncomplete || desiredValue !== currentValue
            ])
        })
    })

    const visibleEventPosts = computed(() => {
        return filteredEventPosts.value.filter(({ eventPostResourceType }) => {
            return !eventPostFilter.resourceTypeEquals || eventPostResourceType === eventPostFilter.resourceTypeEquals
        })
    })

    const typeFacets = computed(() => {
        return [
            {
                value: 'ALL',
                name: 'Alle',
                count: filteredEventPosts.value.length,
                active: eventPostFilter.resourceTypeEquals === null,
                set: () => (eventPostFilter.resourceTypeEquals = null)
            },
            {
                value: 'PERSONAL',
                name: 'Personal',
                count: filteredEventPosts.value.filter(({ eventPostResourceType }) => {
                    return eventPostResourceType === 'PERSONAL'
                }).length,
                active: eventPostFilter.resourceTypeEquals === 'PERSONAL',
                set: () => (eventPostFilter.resourceTypeEquals = 'PERSONAL')
            },
            {
                value: 'MATERIAL',
                name: 'Material',
                count: filteredEventPosts.value.filter(({ eventPostResourceType }) => {
                    return eventPostResourceType === 'TECHNIC'
                }).length,
                active: eventPostFilter.resourceTypeEquals === 'TECHNIC',
                set: () => (eventPostFilter.resourceTypeEquals = 'TECHNIC')
            }
        ]
    })

    return {
        eventPostFilter: eventPostFilter,
        typeFacets,
        visibleEventPosts
    }
}
