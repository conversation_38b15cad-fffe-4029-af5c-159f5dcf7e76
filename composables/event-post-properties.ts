type PersonalEventPostResourceType = "PERSONAL"
type TechnicEventPostResourceType = "TECHNIC"
type EventPostResourceType = PersonalEventPostResourceType | TechnicEventPostResourceType

interface BaseEventPostProperties {
  eventPostResourceType: EventPostResourceType,
  description: string,
  dateFrom: Date,
  dateUpTo: Date,
}

interface PersonalEventPostProperties extends BaseEventPostProperties {}

interface TechnicEventPostProperties extends BaseEventPostProperties {
  technicArticleTypes: number[],
  technicArticleUnitTypes: number[],
  technicArticleUnitCategories: number[]
}


export function useEventPostProperties<T extends ApiModel<"Resource">, R = T['type'] extends 'TECHNIC' ? TechnicEventPostProperties : PersonalEventPostProperties>(
  resource: T,
  dateFrom: Date,
  dateUpTo: Date
): R {

  const { $resourceName } = useNuxtApp();

  const name = $resourceName(resource);
  const eventPostProperties: BaseEventPostProperties = {
    eventPostResourceType: resource.type,
    description: name,
    dateFrom,
    dateUpTo,
  };

  if (resource.type === 'TECHNIC') {
    return {
      ...eventPostProperties,
      technicArticleTypes: resource.external ? [resource.externalArticle.type.id] : [resource.article.type.id],
      technicArticleUnitTypes: resource.external ? [resource.externalArticle.unitType.id] : [resource.article.unitType.id],
      technicArticleUnitCategories: resource.external ? [resource.externalArticle.unitCategory.id] : [resource.article.unitCategory.id],
    } as R;
  }

  return eventPostProperties as R;
}
