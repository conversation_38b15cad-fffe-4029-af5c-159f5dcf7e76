import { useQuery } from '@tanstack/vue-query'

type MemberQueryContext = ReturnType<typeof memberQueries.detail>['_ctx']

type ContextKey = keyof MemberQueryContext

type ContextQueryParams<T extends ContextKey> = Parameters<MemberQueryContext[T]>

class MemberQuery extends Function {
    constructor(private member: number) {
        super()
    }

    public details = () => {
        return useQuery({
            ...memberQueries.detail(this.member),
            staleTime: Infinity,
            cacheTime: Infinity,
            enabled: !!this.member
        })
    }

    public drivingLicenses = (...args: ContextQueryParams<'drivingLicenses'>) => {
        return useQuery({
            ...memberQueries.detail(this.member)._ctx['drivingLicenses'](...args),
            staleTime: Infinity,
            cacheTime: Infinity,
            enabled: !!this.member
        })
    }

    public drivingLicensesByType = (...args: ContextQueryParams<'drivingLicensesByType'>) => {
        return useQuery({
            ...memberQueries.detail(this.member)._ctx['drivingLicensesByType'](...args),
            staleTime: Infinity,
            cacheTime: Infinity,
            enabled: !!this.member
        })
    }

    public operationQualifications = (...args: ContextQueryParams<'operationQualifications'>) => {
        return useQuery({
            ...memberQueries.detail(this.member)._ctx['operationQualifications'](...args),
            staleTime: Infinity,
            cacheTime: Infinity,
            enabled: !!this.member
        })
    }
}

export function useMemberQueries(member: number) {
    return new MemberQuery(member)
}
