type LifecycleActions = {
    internalPublicationInviteHelp: (publication?: ApiModel<'InternalPublication'>) => Promise<Error | ApiModel<'InternalPublication'>>
    internalPublicationShowInvitations: () => Promise<Error | void>
    removeInternalPublication: (publication: ApiModel<'InternalPublication'>) => Promise<Error | void>
    editEvent: () => Promise<Error | ApiModel<'Event'>>
    copyEvent: () => Promise<Error | void>
    closeEvent: () => Promise<Error | ApiModel<'Event'>>
    canNotCloseEvent: () => Promise<Error | void>
    openEvent: () => Promise<Error | ApiModel<'Event'>>
    cancelEvent: () => Promise<Error | ApiModel<'Event'>>
    removeEvent: () => Promise<Error | void>
    reactivateEvent: () => Promise<Error | ApiModel<'Event'>>
    downloadReports: () => Promise<Error | any | void>
    downloadPrintableDocument: () => Promise<Error | any | void>
    downloadCalendarEntry: () => Promise<Error | any | void>
    viewResources: () => Promise<Error | any | void>
}

const LifecycleActionsKey = Symbol() as InjectionKey<LifecycleActions>

export function provideEventActions(actions: LifecycleActions) {
    provide(LifecycleActionsKey, actions)
}

export function injectEventActions() {
    return inject(LifecycleActionsKey)
}

