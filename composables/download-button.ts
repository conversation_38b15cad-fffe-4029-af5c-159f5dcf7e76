import { type MaybeRef, useEventListener, useObjectUrl } from '@vueuse/core'

export function useDownloadButton(fileName: MaybeRef<string>, { data, refresh }: ReturnType<typeof useAsyncData>) {
    const button = ref<HTMLButtonElement>()

    const objectUrl = useObjectUrl(data)

    async function trigger(_event?: MouseEvent) {
        await refresh()

        const anchor = document.createElement('a')

        // Obviously no need to append/remove anchor to/from body

        anchor.setAttribute('href', unref(objectUrl))
        anchor.setAttribute('download', unref(fileName))

        // anchor.dispatchEvent(event) does not work everywhere. Don't know why.
        anchor.click()
    }

    useEventListener(button, 'click', trigger)

    return {
        button,
        trigger
    }
}
