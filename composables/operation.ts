import { useMutation } from '@tanstack/vue-query'

type SearchListResponse = {
    items: ApiModel<'Operation'>[]
}

export const useOperationSearch = function () {
    const {
        mutate: operationSearch,
        isLoading,
        isError,
        data
    } = useMutation(async () => {
        return await $fetch<SearchListResponse>(`/api/operations`, {
            body: {
                offset: 0,
                limit: 1000,
                active: true
            },
            method: 'post'
        })
    })

    return {
        operationSearch,
        isLoading,
        isError,
        data
    }
}
