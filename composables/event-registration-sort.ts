import { type MaybeRef, reactiveComputed } from '@vueuse/core'
import orderBy from 'just-order-by'

const sortingOptions = {
    'a-z': { label: 'Name A-Z', direction: 'asc' },
    'z-a': { label: 'Name Z-A', direction: 'desc' },
    'date-from-asc': { label: 'Zuerst gekommen oben', direction: 'asc' },
    'date-from-desc': { label: 'Zuletzt gekommen oben', direction: 'asc' }
} as const

type SortKey = keyof typeof sortingOptions

export function useEventRegistrationSorter(input: MaybeRef<ApiModel<'EventRegistration'>[]>) {
    // Default sorting is "by date"
    const selectedKey = ref<SortKey>('date-from-asc')

    const selectedOption = computed(() => {
        return sortingOptions[selectedKey.value]
    })

    const alternatives = computed(() => {
        const keys = Object.keys(sortingOptions).filter((key) => {
            return key !== selectedKey.value
        }) as SortKey[]

        return keys.map((key) => {
            return {
                key,
                label: sortingOptions[key].label,
                set: () => (selectedKey.value = key)
            }
        })
    })

    const sortedEventRegistrations = computed(() => {
        switch (selectedKey.value) {
            case 'date-from-asc': {
                return orderBy(unref(input), [
                    {
                        property(eventRegistration) {
                            return eventRegistration.start
                        },
                        order: 'asc'
                    },
                    {
                        property(eventRegistration) {
                            if(eventRegistration.resource.external) {
                                return eventRegistration.resource.externalPerson?.lastname.toLocaleLowerCase()
                            } else {
                                return eventRegistration.resource.member?.lastname.toLocaleLowerCase()
                            }
                        },
                        order: 'asc'
                    }
                ])
            }

            case 'date-from-desc': {
                return orderBy(unref(input), [
                    {
                        property(eventRegistration) {
                            return eventRegistration.start
                        },
                        order: 'desc'
                    },
                    {
                        property(eventRegistration) {
                            if(eventRegistration.resource.external) {
                                return eventRegistration.resource.externalPerson?.lastname.toLocaleLowerCase()
                            } else {
                                return eventRegistration.resource.member?.lastname.toLocaleLowerCase()
                            }
                        },
                        order: 'desc'
                    }
                ])
            }

            case 'z-a': {
                return orderBy(unref(input), [
                    {
                        property(eventRegistration) {
                             if(eventRegistration.resource.external) {
                                return eventRegistration.resource.externalPerson?.lastname.toLocaleLowerCase()
                            } else {
                                return eventRegistration.resource.member?.lastname.toLocaleLowerCase()
                            }
                        },
                        order: 'desc'
                    },
                    {
                        property: 'id',
                        order: 'asc'
                    }
                ])
            }

            case 'a-z':
            default: {
                return orderBy(unref(input), [
                    {
                        property(eventRegistration) {
                             if(eventRegistration.resource.external) {
                                return eventRegistration.resource.externalPerson?.lastname.toLocaleLowerCase()
                            } else {
                                return eventRegistration.resource.member?.lastname.toLocaleLowerCase()
                            }
                        },
                        order: 'asc'
                    },
                    {
                        property: 'id',
                        order: 'asc'
                    }
                ])
            }
        }
    })

    const eventRegistrationSorter = reactiveComputed(() => {
        return {
            icon: selectedOption.value.direction === 'asc' ? ('sort-ascending' as const) : ('sort-descending' as const),
            label: selectedOption.value.label,
            alternatives: alternatives.value
        }
    })

    return {
        eventRegistrationSorter,
        sortedEventRegistrations
    }
}
