import { useMutation } from '@tanstack/vue-query'

// type SearchListResponse = {
//     items: ApiModel<'OrganizationWithHierarchy'>[]
// }

export const useCreateRegistrationExternal = function () {
    const {
        mutate: createRegistrationExternal,
        isLoading,
        isError,
        data
    } = useMutation(async ({ registrationCard, start, end, eventId }: { registrationCard: ApiModel<'RegistrationCard'>; start: string; end: string, eventId: number }) => {
        return await $fetch(`/api/events/${eventId}/registrations/registration-card`, {
            body: {
                registrationCard: registrationCard,
                start: start,
                end: end
            },
            method: 'post'
        })
    })

    return {
        createRegistrationExternal,
        isLoading,
        isError,
        data
    }
}
