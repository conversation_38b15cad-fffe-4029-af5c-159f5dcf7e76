import { type MaybeRef } from '@vueuse/core'
import { invert } from 'lodash-es'
import ListType from '~~/enums/listType'
import { operationQualifications } from '~/enums/code-entries-with-category'

/**
 * A map from "codeEntry.value1" (The qualification category) to a "codeEntry.listId" (The list of qualifications within that category)
 *
 * This is being exported because we preload the lists of qualifications within plugin `inventory/index.ts`
 */
export const qualificationsMap = {
    ['Fueh']: ListType.Leadingship, // Führung
    ['Sani']: ListType.Ambulance, // Sanitätsdienst
    ['Betr']: ListType.SupportService, // Betreuungsdienst
    ['IuK']: ListType.ITService, // IuK
    ['Wass']: ListType.WaterRescue, // Wasserwacht
    ['Pflege']: ListType.NursingService, // Pflegedienst
    ['jrk']: ListType.YouthRedCross, // Jugendrotkreuz
    ['EQSonst']: ListType.OtherOperationQualification, // Sonstige Einsatzqualifikationen
    ['ABC']: ListType.ABCService, // ABC-Dienst
    ['TeSi']: ListType.TechAndSafety, // Technik und Sicherheit (TeSi)
    ['kab']: ListType.InformationOnPersons, // Personenauskunftswesen
    ['PSNV']: ListType.PSNV, // PSNV
    ['Berg']: ListType.MountainRescue, // Bergwacht
    ['FL']: ListType.UnboundVolunteers // Ungebundene Helfer (is not available in staging system)
} as const

/**
 * We need this category to use it in universal select component
 */
export interface CodeEntryWithCategory extends ApiModel<'CodeEntry'> {
    category: string
}

/**
 * The inverted qualifications map
 *
 * It's being used to identify categories (Code Entries with {listId: ListType.QualificationType})
 */
const categoriesMap = invert(qualificationsMap)

/**
 * Build an array of `OperationQualifications` (a.k.a qualifictions grouped by their category)
 * based on the given qualifications.
 *
 * Thereby we have to identify the category a qualification belongs to and move
 * the respective qualification into the belonging qualifications array.
 *
 * @param qualifications (qualifictions as code entries)
 * @param repository (all available $codeEntries)
 */
function toGroups(qualifications: ApiModel<'CodeEntry'>[], repository: ApiModel<'CodeEntry'>[]) {
    let qualificationGroups: ApiModel<'OperationQualification'>[] = []

    qualifications.forEach((qualification) => {
        const category = repository?.find(({ value1 }) => {
            return value1 === categoriesMap[qualification.listId]
        })

        const qualificationGroup =
            qualificationGroups.find(({ kind }) => {
                return kind.id === category.id
            }) ||
            useModelFactory('OperationQualification').create({
                kind: category,
                qualifications: []
            })

        qualificationGroup.qualifications = [
            ...qualificationGroup.qualifications.filter((existing) => {
                return existing !== qualification
            }),
            qualification
        ]

        qualificationGroups = [
            ...qualificationGroups.filter(({ kind }) => {
                return kind.id !== category.id
            }),
            qualificationGroup
        ]
    })

    return qualificationGroups
}

/**
 * Extract simple qualification code entries from the given `OperationQualifications`
 *
 * @param groups an array of OperationQualifications (a.k.a qualifictions grouped by their category)
 * @param repository containing all available $codeEntries
 */
function toList(groups: ApiModel<'OperationQualification'>[], repository: ApiModel<'CodeEntry'>[]) {
    return (
        groups.reduce<ApiModel<'CodeEntry'>[]>((collection, { qualifications }) => {
            collection.push(
                ...qualifications
                    .flat()
                    .map(({ id }) => {
                        return repository?.find(({ id: codeEntryId }) => {
                            return id === codeEntryId
                        })
                    })
                    .filter((qualification) => !!qualification)
            )

            return collection
        }, []) || []
    )
}

type NumberArrayProp<T> = {
    [K in keyof T]: T[K] extends number[] ? K : never
}[keyof T]

/**
 * This provides a computed ref as a proxy for a list of code entry ids.
 *
 * Problem is: We wan't (and have) to work on objects of type `OperationQualification`.
 * Unfortunatly EventPosts store the qualification criteria as a simple list of ids.
 *
 * So we have to transform that into a suitable data structure.
 *
 * @param model any model
 * @param property any property that's typed as number[]
 */
export function useOperationQualificationsFacade<T extends any>(
    model: MaybeRef<T>,
    property: NumberArrayProp<T>,
    codeEntries: Ref<ApiModel<'CodeEntry'>[]>
) {
    return computed({
        get() {
            const qualifications = (unref(model)[property] as number[])
                .map((id) => {
                    return codeEntries.value?.find(({ id: codeEntryId }) => id === codeEntryId)
                })
                .filter((codeEntry) => !!codeEntry)

            return toGroups(qualifications, unref(codeEntries))
        },
        set(value) {
            const qualifications = toList(value, unref(codeEntries))
            unref(model)[property] = qualifications.map(({ id }) => id) as any
        }
    })
}

/**
 * This hook provides categories and their belonging qualifications
 *
 * It also extracts all selected qualifications into the variable `selectedQualifications`
 * while performing all needed changes on the (optionally) given `OperationQualifications`.
 *
 * @param model the model that should be modified
 */
export function useOperationQualifications(model: Ref<ApiModel<'OperationQualification'>[]> = ref([]), codeEntries: Ref<ApiModel<'CodeEntry'>[]>) {
    const qualifications = computed<CodeEntryWithCategory[]>(() => {
        return (
            codeEntries.value
                .filter((item) => {
                    if (item.listId !== ListType.QualificationType) return true
                    return false
                })
                .map((item) => {
                    return {
                        ...item,
                        category: operationQualifications[item.listId]
                    } as CodeEntryWithCategory
                }) || []
        )
    })

    const selectedQualifications = computed<CodeEntryWithCategory[]>({
        get() {
            const codeEntryList = toList(unref(model), unref(codeEntries))
            return codeEntryList.map((item) => {
                return {
                    ...item,
                    category: operationQualifications[item.listId]
                } as CodeEntryWithCategory
            })
        },

        set(value: ApiModel<'CodeEntry'>[]) {
            model.value = toGroups(value, unref(codeEntries))
        }
    })

    return {
        qualifications,
        selectedQualifications
    }
}
