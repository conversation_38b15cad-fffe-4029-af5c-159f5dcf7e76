export function calcHelpers(risk: number) {
    if (!risk || risk <= 2) return 2
    if (risk <= 4) return 3
    if (risk <= 13.5) return 5
    if (risk <= 22) return 10
    if (risk <= 40) return 20
    if (risk <= 60) return 30
    if (risk <= 80) return 40
    if (risk <= 100) return 80
    if (risk <= 110) return 100
    if (risk <= 120) return 120
    if (risk <= 140) return 160
}

export function calcPatientTransportVehicles(risk: number) {
    if (!risk || risk <= 4) return 0
    if (risk <= 13) return 1
    if (risk <= 25) return 2
    if (risk <= 40) return 3
    if (risk <= 60) return 4
    if (risk <= 80) return 5
    if (risk <= 100) return 6
    if (risk <= 110) return 7
    if (risk <= 120) return 8
    if (risk <= 140) return 10
}

export function calcAmbulancesAmount(risk: number) {
    if (!risk || risk <= 6) return 0
    if (risk <= 25.5) return 1
    if (risk <= 45.5) return 2
    if (risk <= 60.5) return 3
    if (risk <= 75.5) return 4
    if (risk <= 100) return 5
    if (risk <= 120) return 6
    if (risk >= 120.1) return 7
}

export function calcEmergencyDoctors(risk: number) {
    if (!risk || risk <= 13) return 0
    if (risk <= 30) return 1
    if (risk <= 60) return 2
    if (risk <= 90) return 3
    if (risk <= 120) return 4
    if (risk >= 120.1) return 5
}

export const weighting = [
    { label: 'Allgemeine Sportveranstaltung', value: 0.3 },
    { label: 'Ausstellung', value: 0.3 },
    { label: 'Basar', value: 0.3 },
    { label: 'Demonstration', value: 0.8 },
    { label: 'Feuerwerk', value: 0.4 },
    { label: 'Flohmarkt', value: 0.3 },
    { label: 'Flugveranstaltung', value: 0.9 },
    { label: 'Karnevalsveranstaltung', value: 0.7 },
    { label: 'Karnevalsumzug', value: 0.7 },
    { label: 'Kombi-Veranstaltung (Sport+Musik+Show)', value: 0.35 },
    { label: 'Konzert', value: 0.2 },
    { label: 'Kundgebung', value: 0.5 },
    { label: 'Langlauf', value: 0.3 },
    { label: 'Martinsumzug', value: 0.3 },
    { label: 'Messe', value: 0.3 },
    { label: 'Motorsportveranstaltung', value: 0.8 },
    { label: 'Musikveranstaltung', value: 0.5 },
    { label: 'Oper/Operette', value: 0.2 },
    { label: 'Radrennen', value: 0.3 },
    { label: 'Reitsportveranstaltung', value: 0.1 },
    { label: 'Rockkonzert', value: 1 },
    { label: 'Rockkonzert mit Boygroup', value: 1.2 },
    { label: 'Schauspiel/Theater', value: 0.2 },
    { label: 'Schützenfest', value: 0.5 },
    { label: 'Show', value: 0.2 },
    { label: 'Stadtteilfest', value: 0.4 },
    { label: 'Straßenfest', value: 0.4 },
    { label: 'Tanzsportveranstaltung', value: 0.3 },
    { label: 'Volksfest', value: 0.4 },
    { label: 'Weihnachtsmarkt', value: 0.3 }
]

export function calcPointsForMaxVisitors(maxVisitors: number, isClosedEnvironment: boolean) {
    const factor = 1 + Number(isClosedEnvironment)
    if (maxVisitors <= 500) return factor
    if (maxVisitors <= 1000) return 2 * factor
    if (maxVisitors <= 1500) return 3 * factor
    if (maxVisitors <= 3000) return 4 * factor
    if (maxVisitors <= 6000) return 5 * factor
    if (maxVisitors <= 10000) return 6 * factor
    if (maxVisitors <= 20000) return 7 * factor
    return (7 + Math.floor((maxVisitors - 1) / 10000) - 1) * factor
}

export const helperEventBody = {
    description: 'Sanitäter*in',
    eventPostResourceType: 'PERSONAL' as const,
    qualifications: [9093] as number[]
}

export const emergencyDoctorEventBody = {
    description: 'Notärzt*in',
    eventPostResourceType: 'PERSONAL' as const,
    qualifications: [929, 9065] as number[]
} as const

export const transportVehicleEventBody = {
    ambulance: {
        description: 'Krankentransportwagen (KTW)',
        eventPostResourceType: 'TECHNIC' as const,
        technicArticleTypes: [6] as number[],
        technicArticleUnitTypes: [33] as number[],
        technicArticleUnitCategories: [613] as number[]
    },
    paramedic: {
        description: 'Rettungssanitäter*in - KTW',
        eventPostResourceType: 'PERSONAL' as const,
        qualifications: [927]
    },
    driver: {
        description: 'Fahrer*in - KTW',
        eventPostResourceType: 'PERSONAL' as const,
        desiredValue: 1
    }
}

export const emergencyAmbulanceEventBody = {
    ambulance: {
        description: 'Rettungswagen (RTW)',
        eventPostResourceType: 'TECHNIC' as const,
        technicArticleTypes: [6] as number[],
        technicArticleUnitTypes: [33] as number[],
        technicArticleUnitCategories: [623] as number[]
    },
    emergencyParamedic: {
        description: 'Notfallsanitäter*in - RTW',
        eventPostResourceType: 'PERSONAL' as const,
        qualifications: [925] as number[]
    },
    paramedic: {
        description: 'Rettungssanitäter*in - RTW',
        eventPostResourceType: 'PERSONAL' as const,
        qualifications: [927] as number[]
    }
} as const
