class TreeNode<T> {
    value: T
    children: TreeNode<T>[]
    parent: TreeNode<T> | null
    depth: number
    extractIdentifier: (value: T) => number
    extractParentIdentifier: (value: T) => number | null

    get id(): number {
        return this.extractIdentifier(this.value)
    }

    get parentId(): number | null {
        return this.extractParentIdentifier(this.value)
    }

    toJSON() {
        return {
            value: this.value,
            id: this.id,
            children: this.children.map((child) => child.id),
            parent: this.parent ? this.parent.id : null,
            depth: this.depth
        }
    }

    assignDepthToChildren(currentDepth = 0) {
        this.depth = currentDepth
        this.children.forEach((child) => child.assignDepthToChildren(currentDepth + 1))
    }

    flat() {
        return [this, ...this.children.flatMap((child) => child.flat())]
    }

    isRoot() {
        return this.parent === null
    }

    constructor(value: T, extractIdentifier: (value: T) => number, extractParentIdentifier: (value: T) => number, parent: TreeNode<T> | null = null) {
        this.value = value
        this.children = []
        this.parent = parent
        this.depth = 0
        this.extractIdentifier = extractIdentifier
        this.extractParentIdentifier = extractParentIdentifier
    }
}

export const useForest = <T>(values: T[], extractIdentifier: (value: T) => number, extractParentIdentifier: (value: T) => number | null) => {
    const nodes = values.map((value) => new TreeNode(value, extractIdentifier, extractParentIdentifier))

    nodes.forEach((node) => {
        const parent = nodes.find((parent) => parent.id === node.parentId)
        if (parent) {
            node.parent = parent
            parent.children.push(node)
        }
    })

    const roots = nodes.filter((node) => node.isRoot())
    roots.forEach((root) => root.assignDepthToChildren())
    const flattened: T[] = roots.flatMap((root) => root.flat())

    return {
        roots,
        nodes,
        flattened
    }
}
