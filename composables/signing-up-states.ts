import EventSigningUpStatus from '~~/enums/event-signing-up-status'

/**
 * Utility to map code entries to signing up states
 *
 *
 * UNKNOWN = VALUELIST_EVENT_RESOURCESIGNINGUPSTATUS.id where value1 == "e" //Rückmeldung offen
 * AVAILABLE = VALUELIST_EVENT_RESOURCESIGNINGUPSTATUS.id where value1 == "v" // verfügbar
 * PARTIAL = 'PARTIAL',
 * UNAVAILABLE = VALUELIST_EVENT_RESOURCESIGNINGUPSTATUS.id where value1 == "nv" // nicht verfügbar
 *
 */
export function useSigningUpStates() {
    const { $signingUpStates: signingUpStates } = useNuxtApp()

    enum CodeEntryValue {
        UNKNOWN = 'e',
        AVAILABLE = 'v',
        REJECTED = 'agl',
        UNAVAILABLE = 'nv'
    }

    /**
     * Map SigningUpStatus to corresponding code entry
     */
    const codeEntries = computed<Record<EventSigningUpStatus, ApiModel<'CodeEntry'>>>(() => {
        return {
            [EventSigningUpStatus.UNKNOWN]: signingUpStates.find((state) => state.value1 === CodeEntryValue.UNKNOWN),
            [EventSigningUpStatus.AVAILABLE]: signingUpStates.find((state) => state.value1 === CodeEntryValue.AVAILABLE),
            [EventSigningUpStatus.PARTIAL]: signingUpStates.find((state) => state.value1 === CodeEntryValue.AVAILABLE),
            [EventSigningUpStatus.REJECTED]: signingUpStates.find((state) => state.value1 === CodeEntryValue.REJECTED),
            [EventSigningUpStatus.UNAVAILABLE]: signingUpStates.find((state) => state.value1 === CodeEntryValue.UNAVAILABLE)
        } as const
    })

    /**
     * Retrieve status from signing up data
     */
    function retrieveStatus(signingUp?: ApiModel<'SigningUp'>) {
        if (!signingUp) {
            return EventSigningUpStatus.UNKNOWN
        }

        const { status: codeEntry, periods } = signingUp

        if (!codeEntry || codeEntry.value1 === CodeEntryValue.UNKNOWN) {
            return EventSigningUpStatus.UNKNOWN
        }

        if (codeEntry.value1 === CodeEntryValue.REJECTED) {
            return EventSigningUpStatus.REJECTED
        }

        if (codeEntry.value1 === CodeEntryValue.UNAVAILABLE) {
            return EventSigningUpStatus.UNAVAILABLE
        }

        if (codeEntry.value1 === CodeEntryValue.AVAILABLE && !!periods?.length) {
            return EventSigningUpStatus.PARTIAL
        }

        if (codeEntry.value1 === CodeEntryValue.AVAILABLE) {
            return EventSigningUpStatus.AVAILABLE
        }
    }

    function retrieveCodeEntry(status?: EventSigningUpStatus) {
        return codeEntries.value[status]
    }

    /**
     * Retrieve assignment status including rejection check
     */
    function retrieveAssignment(signingUp?: ApiModel<'SigningUp'>): 'none' | 'assigned' | 'rejected' {
        if (!signingUp) {
            return 'none'
        }

        const { status: codeEntry, isAssigned } = signingUp

        if (codeEntry.value1 === CodeEntryValue.REJECTED) {
            return 'rejected'
        }

        if (isAssigned) {
            return 'assigned'
        }

        return 'none'
    }

    function createSigningUpModel(resource: ApiModel<'Resource'>, periods: ApiModel<'Period'>[] = []) {
        return useModelFactory('SigningUp').fromJson({
            status: codeEntries.value[EventSigningUpStatus.AVAILABLE],
            resource,
            dateOfResponse: new Date(),
            periods
        })
    }

    function updateSigningUpModel(
        signingUp: ApiModel<'SigningUp'>,
        status: EventSigningUpStatus.AVAILABLE | EventSigningUpStatus.UNAVAILABLE | EventSigningUpStatus.PARTIAL,
        periods: ApiModel<'Period'>[] = []
    ) {
        switch (status) {
            case EventSigningUpStatus.UNAVAILABLE:
            case EventSigningUpStatus.AVAILABLE: {
                return useModelFactory('SigningUp').fromJson({
                    ...signingUp,
                    status: codeEntries.value[status],
                    periods: []
                })
            }

            case EventSigningUpStatus.PARTIAL: {
                return useModelFactory('SigningUp').fromJson({
                    ...signingUp,
                    status: codeEntries.value[EventSigningUpStatus.AVAILABLE],
                    periods
                })
            }
        }
    }

    return {
        codeEntries,
        createSigningUpModel,
        updateSigningUpModel,
        retrieveCodeEntry,
        retrieveStatus,
        retrieveAssignment
    }
}
