import { EventStatusEnum, EventStatusInfoEnum, EventStatusLabelEnum, EventStatusType } from '~/enums/event-status-type'
import { SigningUp } from '~/modules/api/models'
import { UiIconName } from '~~/modules/ui'

export enum EventType {
    Apprenticeship,
    Service
}

export type SigningUpStatusValue = 'bad' | 'sufficient' | 'good' | 'ok'

export type SigningUpStatusType = ReturnType<typeof useEventStatistics>

export type EventStatusValue = ReturnType<typeof useEventStatus>

/**
 * Determine display attributes of an event
 *
 * Based on the type of the event, properties such as icon, title and description are determined.
 *
 * @param event ApiModel<"Event">
 */
export const useEventDescription = function (event: ApiModel<'Event'>) {
    const type = computed<EventType>(() => {
        return !!event.apprenticeshipType ? EventType.Apprenticeship : EventType.Service
    })

    const icon = computed<UiIconName>(() => {
        return type.value === EventType.Apprenticeship ? 'education' : 'truck-outlined'
    })

    const category = computed<string>(() => {
        return event.category.value2
    })

    const title = computed<string>(() => {
        return type.value === EventType.Apprenticeship ? event.apprenticeshipType.name : event.description?.value2
    })

    const description = computed<string>(() => {
        return event.extendedDescription
    })

    const duration = computed<number>(() => {
        return (event.dateUpTo.getSeconds() - event.dateFrom.getSeconds()) / 1000 / 3600
    })

    const isApprenticeship = computed(() => {
        return type.value === EventType.Apprenticeship
    })

    const isService = computed(() => {
        return type.value === EventType.Service
    })

    /**
     * Der Ort wird zum Beispiel (oder nur?) für iCal-Exporte genutzt.
     *
     * @todo This is maybe too simple
     */
    const location = computed(() => {
        return event.locations?.find(({ freeAddressText }) => !!freeAddressText)?.freeAddressText
    })

    return {
        type,
        icon,
        title,
        description,
        category,
        location,
        duration,
        isApprenticeship,
        isService
    }
}



/**
 * Based on the object "eventPostStatistics" the signup status of the event is determined.
 *
 * @param event  ApiModel<"Event">
 */
export const useEventStatistics = function (event: ApiModel<'Event'>) {
    const actual = computed<number>(() => {
        const mandatory = event.eventPostStatistics?.mandatoryPersonal.current || 0
        const optional = event.eventPostStatistics?.optionalPersonal.current || 0

        return mandatory + optional
    })

    const desired = computed<number>(() => {
        const mandatory = event.eventPostStatistics?.mandatoryPersonal.desired || 0
        const optional = event.eventPostStatistics?.optionalPersonal.desired || 0

        return mandatory + optional
    })

    /**
     * Klären: Vielleicht hat der Status auch nur was mit 'mandatory personal" zu tun
     */
    const status = computed<SigningUpStatusValue>(() => {
        if (actual.value > desired.value) return 'good'
        if (actual.value === desired.value) return 'ok'
        if (actual.value > 0) return 'sufficient'
        return 'bad'
    })

    return {
        actual,
        desired,
        status: status.value
    }
}

/**
 *
 *
 * @param event  ApiModel<"Event">
 */
export const useEventDateRange = function (event: ApiModel<'Event'>) {
    const dateFrom = computed<Date>(() => event.dateFrom)
    const dateUpTo = computed<Date>(() => event.dateUpTo)

    return {
        from: dateFrom.value,
        to: dateUpTo.value
    }
}

/**
 *
 *
 * @param event  ApiModel<"Event">
 */
export const useEventStatus = function (eventStatus: ApiModel<'Event'>['status']) {
    return {
        isApproved: eventStatus === EventStatusEnum[EventStatusEnum.APPROVED],
        isFinished: eventStatus === EventStatusEnum[EventStatusEnum.FINISHED],
        isCanceled: eventStatus === EventStatusEnum[EventStatusEnum.CANCELED],
        isWaitingForApproval: eventStatus === EventStatusEnum[EventStatusEnum.WAITING_FOR_APPROVAL],
        label: EventStatusLabelEnum[EventStatusEnum[eventStatus]],
        info: EventStatusInfoEnum[EventStatusEnum[eventStatus]]
    }
}
