import {
    endOfMonth,
    startOfMonth,
    addMonths,
    isSameDay,
    startOfWeek,
    endOfWeek,
    addDays,
    isBefore,
    isAfter,
    startOfDay,
    endOfDay,
    isWithinInterval,
    differenceInDays,
    max,
    min,
    format,
    differenceInCalendarWeeks,
    differenceInMinutes
} from 'date-fns'
import { useQueries } from '@tanstack/vue-query'
import { toParams } from './event-list'
import type { EventFilter } from '~~/composables/event-filter'

export interface CalendarData {
    today: Date
    days: ComputedRef<Date[]>
    weekDates: ComputedRef<Date[]>
    weekdays: ComputedRef<Date[]>
    firstDayCurrentMonth: ComputedRef<Date>
    currentWeekNumber: ComputedRef<number>
    selectedDay: Ref<Date>
    formattedCurrentMonth: ComputedRef<string>
    formattedCurrentWeek: ComputedRef<string>
    currentMonthDate: Ref<Date>
    currentWeekStart: Ref<Date>

    formattedDateISO: (date: Date) => string
    formattedDateLabel: (date: Date) => string

    setMonth: (direction: number) => void
    setWeek: (direction: number) => void
    selectDay: (date: Date) => void
}

export const WEEK_VIEW_ROW_HEIGHT = window.innerHeight / 22
export const MULTI_DAY_EVENT_HEIGHT = 28 //px
export const MAX_VISIBLE_EVENTS_PER_SLOT = 3
export const MINUTES_PER_SLOT = 15 // 15-minute time slots


export function useCalendarEvents(currentMonth: Ref<Date>, filterInput?: Ref<EventFilter>) {
    const monthsToFetch = computed(() => [
        addMonths(unref(currentMonth), -1),
        unref(currentMonth),
        addMonths(unref(currentMonth), 1),
    ])

    const calendarDataQueries = useQueries({
        queries: computed(() => {
            return monthsToFetch.value.map((month) => {
                const startDate = startOfWeek(startOfMonth(month), { weekStartsOn: 1 })
                const endDate = endOfWeek(endOfMonth(month), { weekStartsOn: 1 })
                return {
                    ...eventQueries.list({
                        ...toParams(unref(filterInput)),
                        start: startDate,
                        end: endDate,
                        limit: 1000,
                    }),
                    staleTime: 2 * 60 * 60 * 1000,
                }
            })
        })
    })

    const calendarEvents = computed<ApiModel<'CalendarEvent'>[]>(() => {
        const eventMap = new Map()
        calendarDataQueries.forEach(query => {
            if (query.data?.items) {
                const items = query.data.items

                items.forEach(event => {
                    const isAusbildung = event.category?.value2 === 'Ausbildung'
                    const apprenticeshipName = event.apprenticeshipType?.name ?? ''
                    const description = event.description?.value2 ?? ''
                    const extended = event.extendedDescription?.trim() || ''

                    const titleBase = isAusbildung ? apprenticeshipName : description

                    const title = extended
                        ? isAusbildung
                            ? `${titleBase} - ${extended}`
                            : `${extended} - ${titleBase}`
                        : titleBase

                    const isMultiDay = differenceInMinutes(event.dateUpTo, event.dateFrom) > 24 * 60

                    // When event is less than 24h and overlaps between to days we split it into 2 events to display it on both days
                    const calendarEvent = useModelFactory('CalendarEvent').create({
                        ...event,
                        partDateFrom: event.dateFrom,
                        partDateUpTo: event.dateUpTo,
                        title,
                        titleForWeekEvent: titleBase,
                        extendedDescription: extended,
                        isMultiDay
                    })

                    eventMap.set(event.id, calendarEvent)
                })
            }
        })
        return Array.from(eventMap.values())
    })

    // Fetching status based on current month
    const isFetching = computed(() =>
        calendarDataQueries[1].isFetching
    )

    return {
        calendarEvents,
        isFetching,
    }
}

/**
 * Utilities for views components
 */
export function isDayInMultiDayEvent(day: Date, event: ApiModel<'CalendarEvent'>): boolean {
    if (!event.dateUpTo) return false

    return isWithinInterval(day, {
        start: startOfDay(event.dateFrom),
        end: endOfDay(event.dateUpTo)
    })
}

export function eventIntersectsWeek(event: ApiModel<'CalendarEvent'>, weekStart: Date, weekEnd: Date): boolean {
    return isBefore(event.dateFrom, addDays(weekEnd, 1)) &&
        isAfter(event.dateUpTo, weekStart)
}

export function checkIfEventsOverlap(a: ApiModel<'CalendarEvent'>, b: ApiModel<'CalendarEvent'>): boolean {
    return a.partDateFrom < b.partDateUpTo && b.partDateFrom < a.partDateUpTo
}

export function getWeeksInMonth(date: Date): number {
    const start = startOfMonth(date);
    const end = endOfMonth(date);
    return differenceInCalendarWeeks(end, start, { weekStartsOn: 1 }) + 1;
}

// Helper function to get time slot for an event (rounds to 15-minute intervals)
export function getTimeSlot(date: Date): { hour: number, slot: number, slotMinutes: number, minutes: number } {
    const hour = date.getHours()
    const minutes = date.getMinutes()

    const slot = Math.floor(minutes / MINUTES_PER_SLOT)
    const slotMinutes = slot * MINUTES_PER_SLOT

    return { hour, slot, slotMinutes, minutes }
}

// Helper function to create a unique time slot key
export function getTimeSlotKey(date: Date, dayIndex: number): string {
    const { hour, slot, minutes } = getTimeSlot(date)
    return `${dayIndex}-${hour}-${slot}-${minutes}`
}

export function processEventsForStackingLayout(
    events: ApiModel<'CalendarEvent'>[],
    weekDates: Date[]
): {
    visibleEvents: ApiModel<'CalendarEvent'>[],
    hiddenEventsBySlot: Map<string, ApiModel<'CalendarEvent'>[]>
} {
    const processed = splitCrossDayEvents(events.filter(e => !e.isMultiDay))

    const hiddenEventsBySlot = new Map<string, ApiModel<'CalendarEvent'>[]>()
    const visibleEvents: ApiModel<'CalendarEvent'>[] = []

    // Group events by day and time slot
    const eventsByDayAndSlot = new Map<string, ApiModel<'CalendarEvent'>[]>()

    for (const event of processed) {
        const date = event.partDateFrom ?? event.dateFrom
        const dayIndex = weekDates.findIndex(day => isSameDay(day, date))

        if (dayIndex !== -1) {
            const slotKey = getTimeSlotKey(date, dayIndex)
            if (!eventsByDayAndSlot.has(slotKey)) {
                eventsByDayAndSlot.set(slotKey, [])
            }
            eventsByDayAndSlot.get(slotKey).push(event)
        }
    }

    eventsByDayAndSlot.forEach((slotEvents, slotKey) => {
        slotEvents.sort((a, b) => {
            const aStart = a.partDateFrom ?? a.dateFrom
            const bStart = b.partDateFrom ?? b.dateFrom
            return aStart.getTime() - bStart.getTime()
        })

        const visible = slotEvents.slice(0, MAX_VISIBLE_EVENTS_PER_SLOT)
        const hidden = slotEvents.slice(MAX_VISIBLE_EVENTS_PER_SLOT)

        // Assign column positions in a day. Max 3 events
        visible.forEach((event, index) => {
            event.slotColumn = index
            event.totalColumnsInSlot = visible.length
            event.timeSlotKey = slotKey
        })

        visibleEvents.push(...visible)

        if (hidden.length > 0) {
            hiddenEventsBySlot.set(slotKey, hidden)
        }
    })

    return { visibleEvents, hiddenEventsBySlot }
}

export function processEventsForColumnLayout(
    events: ApiModel<'CalendarEvent'>[],
    weekDates: Date[]
): ApiModel<'CalendarEvent'>[] {
    const { visibleEvents } = processEventsForStackingLayout(events, weekDates)
    return visibleEvents
}

export function getHiddenEventsData(
    events: ApiModel<'CalendarEvent'>[],
    weekDates: Date[],
    selectedDay: Date
): Map<string, { count: number, events: ApiModel<'CalendarEvent'>[], timeSlot: { hour: number, slot: number, minutes: number }, showIndicator: boolean, dayIndex: number }> {
    const { hiddenEventsBySlot } = processEventsForStackingLayout(events, weekDates)

    const result = new Map()

    hiddenEventsBySlot.forEach((hiddenEvents, slotKey) => {
        const [dayIndex, hour, slot, minutes] = slotKey.split('-').map(Number)
        const showIndicator = hiddenEvents.some(e => isSameDay(e.partDateFrom ?? e.dateFrom, selectedDay))

        result.set(slotKey, {
            count: hiddenEvents.length,
            events: hiddenEvents,
            timeSlot: { hour, slot, minutes },
            dayIndex,
            showIndicator
        })

    })

    return result
}

export function calculateRegularEventPosition(
    event: ApiModel<'CalendarEvent'>,
    weekDates: Date[],
    options: {
        isHovered?: boolean;
        isMobileView?: boolean;
        multiEventsCount?: number;
        isMoreEventsIndicator?: boolean
        hiddenEvents?: Map<string, { count: number, events: ApiModel<'CalendarEvent'>[], timeSlot: { hour: number, slot: number } }>
    } = {}) {

    const {
        isHovered = false,
        isMobileView = false,
        multiEventsCount = 0,
        hiddenEvents = new Map()
    } = options;

    const eventDate = event.partDateFrom ?? event.dateFrom
    const startHour = eventDate.getHours()
    const startMin = eventDate.getMinutes()
    const endHour = event.partDateUpTo?.getHours() ?? event.dateUpTo.getHours()
    const endMin = event.partDateUpTo?.getMinutes() ?? event.dateUpTo.getMinutes()

    // Snap to time slot
    const { hour: slotHour, slotMinutes } = getTimeSlot(eventDate)
    const snappedStartMinutes = slotHour * 60 + slotMinutes

    const endMinutes = endHour * 60 + endMin
    const theSameDay = isSameDay(event.dateUpTo, event.dateFrom)
    let duration = endMinutes - (startHour * 60 + startMin)

    if (!theSameDay) {
        if (event.partDateFrom?.getHours() > 1 || startHour > 1) {
            duration = (24 - startHour) * 60 - startMin
        } else if (event.partDateFrom?.getHours() === 0 || startHour === 0) {
            duration = endHour * 60 + endMin
        } else {
            duration = (24 - startHour) * 60
        }
    }

    // Calculate position depends on the time
    const multiDayOffset = multiEventsCount * MULTI_DAY_EVENT_HEIGHT
    const top = (snappedStartMinutes / 60) * WEEK_VIEW_ROW_HEIGHT + multiDayOffset
    const height = Math.max(20, (duration / 60) * WEEK_VIEW_ROW_HEIGHT) // Minimum 20px height for short duration events

    const dayIndex = weekDates.findIndex(day => isSameDay(eventDate, day))

    if (dayIndex === -1) {
        return { top: '0', left: '0', width: '0', height: '0', display: 'none' }
    }

    const totalColumns = isMobileView ? 1 : 7
    const dayColumnWidth = 100 / totalColumns


    // Calculate side-by-side positioning within the day column
    const slotColumn = event.slotColumn ?? 0
    const totalColumnsInSlot = event.totalColumnsInSlot ?? 1
    const dayLeft = isMobileView ? slotColumn : dayIndex * dayColumnWidth

    let placeForIndicator = false

    hiddenEvents.forEach(eventMap => {
        eventMap.events.some((e: ApiModel<'CalendarEvent'>) => {
            const startTime = e.partDateFrom ?? e.dateFrom
            if (startTime.getMinutes() === startMin) {
                placeForIndicator = true
                return true
            }
            return false
        })
    })

    let eventWidth = dayColumnWidth / totalColumnsInSlot

    if (placeForIndicator) {
        eventWidth = eventWidth - 0.7
    }

    if (placeForIndicator && isMobileView) {
        eventWidth = eventWidth - 3
    }
    const eventLeft = dayLeft + (slotColumn * eventWidth)


    const baseZIndex = 10
    const zIndex = baseZIndex


    if (isMobileView) {
        // In mobile view, show full width if it's the selected day
        return {
            top: `${top}px`,
            height: `${height}px`,
            left: `${eventLeft}%`,
            width: `${eventWidth}%`,
            transition: 'all 0.2s ease',
            zIndex,
            padding: height > 20 ? '8px' : '2px'
        }
    }

    if (isHovered) {
        return {
            top: `${top}px`,
            height: `${height}px`,
            left: `${eventLeft - (eventWidth * slotColumn)}%`,
            width: `${dayColumnWidth}%`,
            minHeight: `75px`,
            backgroundColor: '#B0D9D3', // neptune-300
            borderColor: '#2C9CE1', // sky-300
            zIndex: zIndex + 50,
            padding: height > 20 ? '8px' : '2px'
        }
    }

    return {
        top: `${top}px`,
        height: `${height}px`,
        left: `${eventLeft}%`,
        width: `${eventWidth}%`,
        transition: 'all 0.2s ease',
        zIndex,
        padding: height > 20 ? '8px' : '2px'
    }
}

export function calculateMoreEventsIndicatorPosition(
    timeSlotData: { timeSlot: { hour: number, slot: number }, dayIndex: number, showIndicator: boolean }, isMobileView: boolean,
) {
    const { timeSlot, dayIndex, showIndicator } = timeSlotData
    const slotMinutes = timeSlot.hour * 60 + timeSlot.slot * MINUTES_PER_SLOT
    const top = (slotMinutes / 60) * WEEK_VIEW_ROW_HEIGHT

    const totalColumns = isMobileView ? 1 : 7
    const dayColumnWidth = 100 / totalColumns
    const dayLeft = isMobileView ? 0 : dayIndex * dayColumnWidth

    // Position indicator where the 4th event would be (after the 3 visible events)
    const offset = isMobileView ? 3.3 : 3.5
    const indicatorLeft = dayLeft + (3 * (dayColumnWidth / offset))

    let display = 'block'

    if (isMobileView) {
        display = showIndicator ? 'block' : 'none'
    }

    return {
        top: `${top}px`,
        left: `${indicatorLeft}%`,
        display
    }
}

export function calculateMultiDayEventPosition(
    event: ApiModel<'CalendarEvent'>,
    weekDates: Date[],
    isMobileView: boolean
) {
    const firstVisibleDay = weekDates[0]
    const lastVisibleDay = weekDates[6]

    let visibleStart = event.partDateFrom
    let visibleEnd = event.dateUpTo

    if (isBefore(event.dateFrom, firstVisibleDay)) {
        visibleStart = firstVisibleDay
    }

    if (isAfter(event.dateUpTo, lastVisibleDay)) {
        visibleEnd = lastVisibleDay
    }

    const startDayIndex = Math.max(0, weekDates.findIndex(day => isSameDay(day, visibleStart)))
    const endDayIndex = Math.min(6, weekDates.findIndex(day => isSameDay(day, visibleEnd)))

    const columnWidth = 100 / 7
    const left = startDayIndex * columnWidth
    const width = (endDayIndex - startDayIndex + 1) * columnWidth

    const eventHeight = 24 // px
    const top = (event.weekViewRow || 0) * (eventHeight + 4)

    if (isMobileView) {
        return {
            left: '0%',
            width: '100%',
            top: `${top + 4}px`,
            height: `${eventHeight}px`,
        }
    }

    return {
        left: `${left}%`,
        width: `${width - 0.5}%`,
        top: `${top + 4}px`,
        height: `${eventHeight}px`,
    }
}

export function calculateWeekMultiDayEventPosition(
    event: ApiModel<'CalendarEvent'>,
    weekOfTheMonth: number,
    days: Date[],
    isHovered: boolean,

) {

    const week = days.slice((weekOfTheMonth - 1) * 7, ((weekOfTheMonth - 1) * 7) + 7)

    if (!week || week.length === 0) return { top: '0', left: '0', width: '0', height: '0' }

    const weekStart = startOfDay(week[0])
    const weekEnd = endOfDay(week[week.length - 1])

    const eventStartInWeek = max([startOfDay(event.dateFrom), weekStart])
    const eventEndInWeek = min([endOfDay(event.dateUpTo), weekEnd])

    const startDayIndex = Math.max(0, week.findIndex(day => isSameDay(day, eventStartInWeek)))
    const endDayIndex = Math.min(
        week.length - 1,
        week.findIndex(day => isSameDay(day, eventEndInWeek)) !== -1
            ? week.findIndex(day => isSameDay(day, eventEndInWeek))
            : week.length - 1
    )

    const validStartIndex = startDayIndex >= 0 ? startDayIndex : 0
    const validEndIndex = endDayIndex >= 0 ? endDayIndex : week.length - 1

    const left = `${(validStartIndex / 7) * 100}%`
    const width = `${((validEndIndex - validStartIndex + 1) / 7) * 100}%`
    const top = 35 + ((event.monthViewRow || 0) * 25) + 'px'
    // Using colors from /modules/ui/tailwind/colors.js

    // nepyun
    // 100: '#E0F0EE',
    // 300: '#B0D9D3'

    let bgColor = '#E0F0EE';
    if (isHovered) {
        bgColor = '#B0D9D3';
    }

    return {
        left,
        width,
        top,
        height: '22px',
        backgroundColor: bgColor
    }
}

export function getMultiDayEventsForWeek(events: ApiModel<'CalendarEvent'>[], week: Date[]): ApiModel<'CalendarEvent'>[] {
    if (!week || week.length === 0) return []

    const weekStart = week[0]
    const weekEnd = week[week.length - 1]

    return events
        .filter(event => {
            if (!event.dateUpTo) return false
            if (!event.isMultiDay) return false

            return isWithinInterval(event.dateFrom, { start: startOfDay(weekStart), end: endOfDay(weekEnd) }) ||
                isWithinInterval(event.dateUpTo, { start: startOfDay(weekStart), end: endOfDay(weekEnd) }) ||
                (isBefore(event.dateFrom, weekStart) && isAfter(event.dateUpTo, weekEnd))
        })
        .map(event => useModelFactory('CalendarEvent').create({
            ...event
        }))
}

export function getRegularEventsForDay(events: ApiModel<'CalendarEvent'>[], day: Date): ApiModel<'CalendarEvent'>[] {
    const splitEvents = splitCrossDayEvents(events)
    return splitEvents
        .filter(event => {
            // For events without an end date or same-day events
            if (!event.partDateUpTo) return isSameDay(day, event.partDateFrom)
            const duration = differenceInDays(event.partDateUpTo, event.partDateFrom)
            return duration === 0 && isSameDay(day, event.partDateFrom)
        })
        .map(event => useModelFactory('CalendarEvent').create({
            ...event,
            isMultiDay: false
        }))
        .sort((a, b) => a.partDateFrom.getTime() - b.partDateFrom.getTime())
}

export function getMultiDayEventsForDay(events: ApiModel<'CalendarEvent'>[], day: Date): ApiModel<'CalendarEvent'>[] {
    return events
        .filter(event => {
            if (!event.dateUpTo) return false
            const duration = differenceInMinutes(event.dateUpTo, event.dateFrom)
            return duration > 24 * 60 && isDayInMultiDayEvent(day, event)
        })
        .map(event => useModelFactory('CalendarEvent').create({
            ...event,
            isMultiDay: true
        }))
}

export function getCombinedEventsForDay(events: ApiModel<'CalendarEvent'>[], day: Date): ApiModel<'CalendarEvent'>[] {
    const multiDayEvents = getMultiDayEventsForDay(events, day)
    const singleDayEvents = getRegularEventsForDay(events, day)
    return [...singleDayEvents, ...multiDayEvents]
}

// When event is less then 24h but overlaps for 2 days we split it into 2 events
export function splitCrossDayEvents(events: ApiModel<'CalendarEvent'>[]): ApiModel<'CalendarEvent'>[] {
    return events.flatMap(event => {
        if (event.isMultiDay || isSameDay(event.dateFrom, event.dateUpTo)) {
            return [event];
        }

        const durationMinutes = differenceInMinutes(event.dateUpTo, event.dateFrom);
        if (durationMinutes > 24 * 60) {
            return [event];
        }

        const startDay = startOfDay(event.dateFrom);
        const endDay = startOfDay(event.dateUpTo);

        const firstEvent = useModelFactory('CalendarEvent').create({
            ...event,
            partId: `${event.id}-part1`,
            partDateUpTo: endOfDay(startDay),
        });

        const secondEvent = useModelFactory('CalendarEvent').create({
            ...event,
            partId: `${event.id}-part2`,
            partDateFrom: startOfDay(endDay),
        });

        return [firstEvent, secondEvent];
    });
}


// Calculate event position
export function assignEventRows(events: ApiModel<'CalendarEvent'>[], week: Date[], view: 'month' | 'week'): ApiModel<'CalendarEvent'>[] {
    if (!events.length) return events

    const weekStart = week[0]
    const weekEnd = week[6]

    const multiEvents = events
        .filter(event => event.isMultiDay && eventIntersectsWeek(event, weekStart, weekEnd))

    const assignedSlots: ApiModel<'CalendarEvent'>[][] = []

    for (const event of multiEvents) {
        const eventStart = startOfDay(event.dateFrom)
        const eventEnd = endOfDay(event.dateUpTo)

        let row = 0
        let placed = false

        while (!placed) {
            if (!assignedSlots[row]) {
                assignedSlots[row] = []
                placed = true
            } else {
                const hasOverlap = assignedSlots[row].some(existing => {
                    const existingStart = startOfDay(existing.dateFrom)
                    const existingEnd = endOfDay(existing.dateUpTo)
                    return !(isBefore(eventEnd, existingStart) || isAfter(eventStart, existingEnd))
                })

                if (!hasOverlap) {
                    placed = true
                } else {
                    row++
                }
            }
        }

        event[view === 'month' ? 'monthViewRow' : 'weekViewRow'] = row
        assignedSlots[row].push(event)
    }

    return multiEvents
}

export function formatEventStartTime(event: ApiModel<'CalendarEvent'>): string {
    return format(event.dateFrom, 'HH:mm');
}

export function formatEventEndTime(event: ApiModel<'CalendarEvent'>): string {
    return format(event.dateUpTo, 'HH:mm');
}

export function formatEventTimeRange(event: ApiModel<'CalendarEvent'>): string {
    return `${format(event.dateFrom, 'HH:mm')} - ${format(event.dateUpTo, 'HH:mm')}`;
}

export function getRegularEventStyle(day: Date, events: ApiModel<'CalendarEvent'>[], days: Date[], eventIndex: number): string {

    const weekIndex = Math.floor(days.findIndex(d => isSameDay(d, day)) / 7);
    const weekDays = days.slice(weekIndex * 7, (weekIndex + 1) * 7);

    const eventsWithRows = assignEventRows(
        getMultiDayEventsForWeek(events, weekDays),
        weekDays,
        'month'
    )

    const relevantMultiDayEvents = eventsWithRows.filter(event => isDayInMultiDayEvent(day, event));

    const occupiedRows: number[] = relevantMultiDayEvents.map(event => event.monthViewRow);
    const isOccupied = occupiedRows.includes(eventIndex)
    const isOccupiedNextRow = occupiedRows.includes(eventIndex + 1)
    const isOccupiedPrevRow = occupiedRows.includes(eventIndex - 1)

    let currentRow = eventIndex;
    let rowToUse = 0;

    // Position events when row is occupied by multiday event
    while (isOccupied && occupiedRows.includes(currentRow)) {
        currentRow++
        rowToUse++
    }

    if (!isOccupiedNextRow && isOccupiedPrevRow) {
        rowToUse = 0
    }

    if (isOccupiedNextRow && isOccupiedPrevRow) {
        rowToUse = 1
    }

    const baseMargin = 2;
    const rowHeight = 24;
    const marginTop = baseMargin +
        (isOccupied ?
            (rowToUse * rowHeight)
            :
            isOccupiedNextRow ?
                (rowToUse * rowHeight)
                :
                0);

    return `margin-top: ${marginTop}px`;
}

