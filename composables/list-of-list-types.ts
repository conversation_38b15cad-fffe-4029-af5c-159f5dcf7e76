import * as CodeEntries from '~/enums/code-entries-with-category';
import ListType from '~/enums/listType';

function getKeysAsListType(obj: object): ListType[] {
    return Object.keys(obj).map(key => Number(key) as ListType);
}

export function useListType() {
    return {
        statusAtDrk: getKeysAsListType(CodeEntries.statusAtDrk),
        typesOfMembership: getKeysAsListType(CodeEntries.typesOfMembership),
        membershipGroups: getKeysAsListType(CodeEntries.membershipGroups),
        membershipEntries: getKeysAsListType(CodeEntries.membershipEntries),
        membershipGroupDescriptions: getKeysAsListType(CodeEntries.membershipGroupDescriptions),
        operations: getKeysAsListType(CodeEntries.operations),
        operationDescriptions: getKeysAsListType(CodeEntries.operationDescriptions),
        functions: getKeysAsListType(CodeEntries.functions),
        operationQualifications: getKeysAsListType(CodeEntries.operationQualifications),
    };
}
