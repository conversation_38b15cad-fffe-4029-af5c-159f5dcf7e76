import { MaybeRef } from '@vueuse/core'
import { every } from 'lodash-es'
import { DateTime } from 'luxon'

type EventRegistrationsFilter = {
    descriptionContains: string | null
    isEndDateSet: boolean
    currentDate: Date
}

export function useEventRegistrationFilter(input: MaybeRef<ApiModel<'EventRegistration'>[]>) {

    const eventRegistrationFilter = reactive<EventRegistrationsFilter>({
        descriptionContains: null,
        isEndDateSet: false,
        currentDate: null
    })

    const eventRegistration = computed(() => {
        return unref(input) ?? []
    })

    /**
     * This filters by
     */
    const filteredEventPosts = computed(() => {
        return eventRegistration.value.filter(({end, resource}) => {

            // we need to check if resource is external or not and if was not deleted
            const member = resource.member?.firstname.toLowerCase().concat(' ').concat(resource.member?.lastname.toLowerCase())
            const externalPerson = resource.externalPerson?.firstname.toLowerCase().concat(' ').concat(resource.externalPerson?.lastname.toLowerCase())
            const person = member || externalPerson || ' '
            return every([
                !eventRegistrationFilter.descriptionContains || person.includes(eventRegistrationFilter?.descriptionContains.toLowerCase()),
                !eventRegistrationFilter.isEndDateSet || end === null || end > eventRegistrationFilter.currentDate
            ])

        })
    })

    const visibleRegistrations = computed(() => {
        return filteredEventPosts.value.filter(({ end }) => {
            return !eventRegistrationFilter.isEndDateSet || end === null || end > eventRegistrationFilter.currentDate
        })
    })

    return {
        eventRegistrationFilter: eventRegistrationFilter,
        visibleRegistrations
    }
}
