import { useQueryClient, useMutation, type MutationOptions } from '@tanstack/vue-query'
import { type MaybeRefDeep } from '@tanstack/vue-query/build/lib/types'

type EventListData<Model> = {
    items: Model[]
}

type OptimisticMutationOptions<TData, TError, TVariables, TContext> = Omit<
    MutationOptions<TData, TError, TVariables, TContext>,
    'onMutate' | 'onError' | 'onSettled'
>

type OptimisticMutationObserverOptions<TData = unknown, TError = unknown, TVariables = void, TContext = unknown> = {
    [Property in keyof OptimisticMutationOptions<TData, TError, TVariables, TContext>]: MaybeRefDeep<
        OptimisticMutationOptions<TData, TError, TVariables, TContext>[Property]
    >
} & {
    onPatch: (previous: ApiModel<'Event'>, mutation: TVariables) => ApiModel<'Event'>
    event: number
}

export function useOptimisticEventMutation<TData = unknown, TError = unknown, TVariables = unknown, TContext = unknown>(
    options: OptimisticMutationObserverOptions<TData, TError, TVariables, TContext>
) {
    const client = useQueryClient()

    const { onPatch: updater, event, ...mutationOptions } = unref(options)

    /**
     * Patch list and event details
     *
     * Return the affected queries so they can be stored in mutation context
     *
     * @param mutationData
     */
    async function patchAll(mutationData: TVariables) {
        const affectedQueries = await Promise.all([patchEvent(mutationData), patchList(mutationData)])

        return [...affectedQueries[0], ...affectedQueries[1]]
    }

    /**
     * Patch an individual event
     *
     * @param mutationData
     */
    async function patchEvent(mutationData: TVariables) {
        const queryKey = queries.events.detail(event).queryKey
        await client.cancelQueries({ queryKey })

        const affectedQueries = client.getQueriesData<ApiModel<'Event'>[]>({
            queryKey,
            type: 'active',
            exact: true
        })

        affectedQueries.forEach(([key, _]) => {
            client.setQueryData<ApiModel<'Event'>>(key, (previous) => updater(previous, mutationData))
        })

        return affectedQueries
    }

    /**
     * Find an individual event in current list and patch it's content
     *
     * @param mutationData
     */
    async function patchList(mutationData: TVariables) {
        const queryKey = queries.events.list._def
        await client.cancelQueries({ queryKey })

        const affectedQueries = client.getQueriesData<EventListData<ApiModel<'Event'>>[]>({
            queryKey,
            type: 'active',
            exact: false
        })

        affectedQueries.forEach(([key, _data]) => {
            client.setQueryData<EventListData<ApiModel<'Event'>>>(key, (previous) => {
                const { items, ...otherEventListData } = previous

                return {
                    items: items.map((item) => {
                        if (item.id !== event) {
                            return item
                        }

                        return updater(item, mutationData)
                    }),
                    ...otherEventListData
                }
            })
        })

        return affectedQueries
    }

    /**
     * Build the mutation
     */
    return useMutation({
        ...mutationOptions,

        onMutate: async (data) => {
            const queries = await patchAll(data)
            return { queries }
        },

        onSuccess: () => {
            return null
        },

        onError: (_, __, { queries }) => {
            queries.forEach(([key, data]) => {
                client.setQueryData(key, data)
            })
        },

        onSettled: (_, __, ___, { queries: _affectedQuery }) => {
            client.invalidateQueries({ queryKey: queries.events.list._def, refetchType: 'active', exact: false })
            client.invalidateQueries({ queryKey: queries.events.detail(event).queryKey, refetchType: 'active', exact: true })
        }
    })
}
