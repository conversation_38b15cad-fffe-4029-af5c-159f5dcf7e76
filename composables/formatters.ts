/**
 * Provides text formatting utilities: escaping HTML, converting URLs to anchors, and newlines to <br>.
 */
export function useFormatter() {

    function formatLinksToHtmlAnchor(text: string): string {
        const urlRegex = /((https?:\/\/[^\s]+))/g
        return text.replace(
            urlRegex,
            '<a href="$1" class="text-blue-600 underline" target="_blank" rel="noopener noreferrer">$1</a>'
        )
    }

    function formatToHtmlEntities(text: string): string {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
    }

    function formatNewLineToHtmlBreaks(text: string): string {
        return text.replace(/\n/g, '<br>')
    }

    return {
        formatLinksToHtmlAnchor,
        formatToHtmlEntities,
        formatNewLineToHtmlBreaks,
    }
}
