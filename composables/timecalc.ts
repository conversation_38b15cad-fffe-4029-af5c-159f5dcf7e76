import { isAfter, isBefore } from 'date-fns'

interface Timeframe {
    start: Date
    end: Date
}

/*
Calculate the intersection between timeframe and slot
*/

export const calculateTimeIntersection = function (timeframe: Timeframe, slot: Timeframe): Timeframe {
    // Slot is outside or equals of timeframe
    if (!isAfter(slot.end, timeframe.start) || !isBefore(slot.start, timeframe.end)) {
        return null
    }

    // Slot is larger then timeframe
    if (!isAfter(slot.start, timeframe.start) && !isBefore(slot.end, timeframe.end)) {
        return timeframe
    }

    // Slot ends after timeframe ends
    if (!isBefore(slot.end, timeframe.end)) {
        return {
            start: slot.start,
            end: timeframe.end
        }
    }

    // Slot starts before timeframe starts
    if (!isAfter(slot.start, timeframe.start)) {
        return {
            start: timeframe.start,
            end: slot.end
        }
    }

    // Slot is inside timeframe
    return {
        start: slot.start,
        end: slot.end
    }
}

/*
Calculate the intersection between slot and multiple timeframes.
*/

export const getTimeIntersections = function (slot: Timeframe, timeframes: Timeframe[]): Timeframe[] {
    const result = []

    for (const tf of timeframes) {
        const intersection = calculateTimeIntersection(tf, slot)
        if (intersection) {
            result.push(intersection)
        }
    }
    return result
}

/*
Calculate the difference between timeframe and slot
*/
export const calculateTimeDifference = function (timeframe: Timeframe, slot: Timeframe): Timeframe[] {
    // Slot is outside or equals of timeframe
    if (!isAfter(slot.end, timeframe.start) || !isBefore(slot.start, timeframe.end)) {
        return [timeframe]
    }

    // Slot is larger then timeframe
    if (!isAfter(slot.start, timeframe.start) && !isBefore(slot.end, timeframe.end)) {
        return []
    }

    // Slot ends after timeframe ends
    if (!isBefore(slot.end, timeframe.end)) {
        return [
            {
                start: timeframe.start,
                end: slot.start
            }
        ]
    }

    // Slot starts before timeframe starts
    if (!isAfter(slot.start, timeframe.start)) {
        return [
            {
                start: slot.end,
                end: timeframe.end
            }
        ]
    }

    // Slot is inside timeframe
    return [
        {
            start: timeframe.start,
            end: slot.start
        },
        {
            start: slot.end,
            end: timeframe.end
        }
    ]
}

/*
Calculate the difference between time frame and multiple slots.
*/
export const getTimeDifferences = function (timeframe: Timeframe, slots: Timeframe[]): Timeframe[] {
    let result = [timeframe]

    for (const cutRange of slots) {
        let newResult = []

        for (const range of result) {
            const difference = calculateTimeDifference(range, cutRange)

            newResult = [...newResult, ...difference]
        }

        result = newResult
    }

    return result
}
