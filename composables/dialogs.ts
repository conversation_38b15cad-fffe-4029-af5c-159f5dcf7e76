// we can not open multiply dialogs by defautl because of focusing problem
// we create a stack of open modals and check which modal was open as last one 'topmostDialog'. Then we close modals 'under' it.
import { computed, ref } from "vue";

const openDialogs = ref<string[]>([]);
const topmostDialog = computed<string | undefined>(() => {
    const open = openDialogs.value;
    return open.length > 0 ? open[open.length - 1] : undefined;
})

function newId() {
    return crypto.randomUUID();
}

/**
 * Registers a modal as open (on top of everything else already opened).
 * @return id of the opened modal that should be returned to onDialogClose();
 */
function onOpen(id: string) {
    openDialogs.value.push(id);
}

/**
 * Removes the top modal form the list.
 */
function onClose(id: string) {
    openDialogs.value = openDialogs.value.filter(openedId => openedId !== id);
}

export default function useDialogs() {
    return {
        newId,
        topmostDialog,
        onOpen,
        onClose,
    }
}
