import type { Ref } from 'vue'
import { createPopper } from '@popperjs/core'
import { MaybeRef, unrefElement } from '@vueuse/core'

/**
 * @TODO try to utilize unrefElement
 * @see https://vueuse.org/core/unrefElement/
 */

export const usePopper = function (
    reference: MaybeRef<HTMLElement>,
    popper: MaybeRef<HTMLElement>,
    options?: Partial<Parameters<typeof createPopper>['2']>
) {
    const instance = ref<ReturnType<typeof createPopper>>()

    onMounted(() => {
        instance.value = createPopper(unrefElement(reference), unrefElement(popper), options)
    })

    onUnmounted(() => {
        instance.value?.destroy()
    })

    return instance
}
