import { useConfirmDialog, type UseConfirmDialogReturn } from '@vueuse/core'
import { useRoute } from 'vue-router'

export type manageResourcesDialogType = {
    manageResourcesDialog: UseConfirmDialogReturn<any, any, any>
    openManageResources: () => Promise<void>
    closeManageResources: () => Promise<void>
    goToManageResources: (event: ApiModel<'Event'>) => Promise<void>
}

export function useResourceManageDialog() {

    const route = useRoute()
    const router = useRouter()

    const manageResourcesDialog = useConfirmDialog()

    manageResourcesDialog.onReveal(() => {
        route.meta.focusMode = true
    })

    manageResourcesDialog.onCancel(() => {
        route.meta.focusMode = false
    })

    async function openManageResources() {
        await manageResourcesDialog.reveal()
    }

    async function closeManageResources() {
        await manageResourcesDialog.cancel()
    }

    async function goToManageResources(event: ApiModel<'Event'>) {
        await router.push({ name: 'events-id-planning', params: { id: event.id } })
        nextTick(() => openManageResources())
    }

    return {
        manageResourcesDialog,
        openManageResources,
        closeManageResources,
        goToManageResources
    }
}
