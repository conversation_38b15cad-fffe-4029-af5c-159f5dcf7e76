import { CodeEntryWithCategory } from './operation-qualifications'
import * as codeEntriesMap from '~~/enums/code-entries-with-category'
import ListType from '~/enums/listType'
import { communitiesMapInverted } from '~/composables/select-community'


/**
 * This hook provides code entires with categories
 * and allows to assign to a model code entry without category
 */
export function useCodeEntriesWithCategoryFacade(
    model: Ref<ApiModel<'InternalPublication'>>,
    codeEntries: Ref<ApiModel<'CodeEntry'>[]>,
    codeEntriesMapName: codeEntriesMap.CodeEntriesNames,
    event?: Ref<ApiModel<'Event'>>
) {

    const codeEntryWithCategory = computed<CodeEntryWithCategory[]>(() => {
        return (
            codeEntries.value
                .filter((item) => {
                    if (item.listId !== ListType.QualificationType) return true
                    return false
                })
                .map((item) => {
                    return {
                        ...item,
                        category: codeEntriesMap[codeEntriesMapName][item.listId]
                    } as CodeEntryWithCategory
                }) || []
        )
    })

    const selected = ref<CodeEntryWithCategory[]>([])

    const selectedCodeEntry = computed<CodeEntryWithCategory[]>({
        get() {
            return (
                model.value[codeEntriesMapName]?.map((item) => {
                    return {
                        ...item,
                        category: codeEntriesMap[codeEntriesMapName][item.listId]
                    } as CodeEntryWithCategory
                }) || selected.value
            )
        },
        set(value: CodeEntryWithCategory[]) {
            const codeEntry = value.map((item) => {
                const { category, ...rest } = item
                return rest as ApiModel<'CodeEntry'>
            })
            unref(model)[codeEntriesMapName] = codeEntry
            selected.value = value
        }
    })

    return {
        codeEntryWithCategory,
        selectedCodeEntry
    }
}

export function useInternalPublications(internalPublications: Ref<ApiModel<'InternalPublication'>[]>) {
    const arePublicationsAvailable = computed(() => internalPublications.value?.length > 0 || false)

    const allOrganisationsInvited = computed(() => {
        return (
            internalPublications.value?.some(
                (publication) =>
                    publication.statusAtDrk.length === 0 &&
                    publication.typesOfMembership.length === 0 &&
                    publication.membershipEntries.length === 0 &&
                    publication.functions.length === 0 &&
                    publication.membershipGroups.length === 0 &&
                    publication.membershipGroupDescriptions.length === 0 &&
                    publication.operations.length === 0 &&
                    publication.operationDescriptions.length === 0 &&
                    publication.operationQualifications.length === 0
            ) ?? false
        )
    })

    const oneOrganisationInvited = computed(() => {
        return (internalPublications.value?.length === 1 && !allOrganisationsInvited.value) || false
    })

    const twoOrganisationInvited = computed(() => {
        return (internalPublications.value?.length === 2 && !allOrganisationsInvited.value) || false
    })

    function getDescription(internalPublication: ApiModel<'InternalPublication'>): string {
        const valuesArray = []

        for (const property in internalPublication) {
            const isArray = Array.isArray(internalPublication[property])
            if (isArray && property === codeEntriesMap.CodeEntriesNames.MembershipEntries) {
                valuesArray.push(
                    ...internalPublication[property].map((publication) => {
                        if (publication.listIdentifier === 'VALUELIST_MEMBERSHIPENTRY_E') return communitiesMapInverted[publication.value1]
                        else return publication.value2
                    })
                )
            } else if (isArray) valuesArray.push(...internalPublication[property].map((publication) => publication.value2))
        }
        return valuesArray.join(', ')
    }

    const oneOrganisationInvitationDescription = computed(() => {
        if (!oneOrganisationInvited.value) return ''
        return internalPublications.value?.flatMap((publication) => [getDescription(publication)]).join(', ') || ''
    })

    return {
        arePublicationsAvailable,
        allOrganisationsInvited,
        oneOrganisationInvited,
        twoOrganisationInvited,
        getDescription,
        oneOrganisationInvitationDescription
    }
}
