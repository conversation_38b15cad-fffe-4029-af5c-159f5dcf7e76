import { MaybeRef } from '@vueuse/core'
import { DateTime } from 'luxon'

type Options = {
    type: 'date' | 'datetime'
    min?: MaybeRef<Date>
    max?: MaybeRef<Date>
    step?: MaybeRef<number>
    isEndDate?: MaybeRef<boolean>
}

export function useDatetimeInput(modelValue: Ref<Date>, { type, min = null, max = null, isEndDate = false }: Options) {
    const compatibleStringPattern = computed(() => {
        return type === 'date' ? 'yyyy-MM-dd' : "yyyy-MM-dd'T'HH:mm"
    })

    /**
     * Format date in a way that can be handled by input fields
     */
    function stringify(value: Date | DateTime) {
        if (!value) {
            return null
        }

        if (value instanceof Date) {
            return DateTime.fromJSDate(value).toFormat(compatibleStringPattern.value)
        }

        return value.toFormat(compatibleStringPattern.value)
    }

    /**
     * Parse the date string back to a date
     */
    function parseDate(value: string) {
        if (!value) {
            return null
        }

        return normalize(DateTime.fromFormat(value, compatibleStringPattern.value))
    }

    /**
     * Normalize end dates
     *
     * For date inputs we usually want to include the selected day
     * Disabled for now: ...for datetime inputs we maybe want to select the millisecond before our selecttion.
     */
    function normalize(input: Date | DateTime) {
        if (!input) {
            return null
        }

        const date = input instanceof Date ? DateTime.fromJSDate(input) : input

        if (type === 'datetime') {
            // if (!!unref(isEndDate)) {
            //     return date.startOf('minute').minus(1)
            // }

            return date.startOf('minute')
        }

        if (type === 'date') {
            if (!!unref(isEndDate)) {
                return date.endOf('day')
            }

            return date.startOf('day')
        }
    }

    /**
     * Add a millisecond to the date if we are handling end dates
     */
    function denormalize(input: Date) {
        if (!input) {
            return null
        }

        const date = DateTime.fromJSDate(input)

        if (type === 'datetime') {
            // if (!!unref(isEndDate)) {
            //     return date.plus(1)
            // }
            return date.startOf('minute')
        }

        if (type === 'date') {
            return date.startOf('day')
        }
    }

    function isValid(date: DateTime) {
        // E.g. 2022-02-30 is not a valid date
        if (!date?.isValid) {
            return false
        }

        // Min check
        if (unref(min) && unref(min) > date.toJSDate()) {
            return false
        }

        // Max check
        if (unref(max) && unref(max) < date.toJSDate()) {
            return false
        }

        return true
    }

    /**
     * The model wrapper
     */
    const dateProxy = computed({
        get: () => {
            return stringify(denormalize(modelValue.value))
        },

        set: (value) => {
            const parsedDate = parseDate(value)

            // Value can be empty; so the parsed date can also be null
            if (!value.length) {
                modelValue.value = null
            }
            // Otherwise we still have to check if the date is valid.
            else if (isValid(parsedDate)) {
                modelValue.value = parsedDate.toJSDate()
            }
        }
    })

    const inputAttrs = computed(() => {
        return {
            min: stringify(unref(min)),
            max: stringify(unref(max)),
            value: dateProxy.value
        }
    })

    const inputListeners = {
        change: (event: InputEvent) => (dateProxy.value = (event.target as HTMLInputElement).value)
    }

    return {
        inputAttrs,
        inputListeners,
        isValid
    }
}
