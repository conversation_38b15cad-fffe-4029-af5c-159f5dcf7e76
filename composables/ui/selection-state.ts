// To delete ?

import { WritableComputedRef } from 'vue'

export function useMultiSelect<T extends { id: number }>(input?: Ref<T[]> | WritableComputedRef<T[]>) {
    const selected = input ?? ref([])

    const pointer = ref(null)

    function add(value: T) {
        pointer.value = null
        if (!!value) {
            selected.value = [...selected.value.filter((item) => item.id !== value.id), value]
        }
    }

    function remove(value: T) {
        selected.value = [...selected.value.filter((item) => item.id !== value.id)]
    }

    return {
        add,
        remove,
        pointer,
        selectedItems: selected
    }
}
