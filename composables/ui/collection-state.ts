import { MaybeRef } from '@vueuse/core'

import { uniqBy } from 'lodash-es'

type Selection = 'all' | Set<ItemKey>

// type SelectionMode = 'single' | 'multiple'

type ItemKey = number | string

type DisplayData = object | string | number

type CollectionStateOptions<T, K> = {
    getKey: (item: T) => ItemKey
    getDisplayValue: (item: T) => K
    disabledCondition?: (item: T) => boolean
    selectedKeys?: Selection
}

export const useCollectionState = function <T, K extends DisplayData>(items: MaybeRef<T[]>, options: CollectionStateOptions<T, K>) {
    const selectedKeys = ref<Selection>(options.selectedKeys || new Set<ItemKey>())

    function toggleItem(key: ItemKey) {
        if (selectedKeys.value === 'all') {
            const allKeys = new Set(unref(items).map(options.getKey))
            if (allKeys.has(key)) {
                allKeys.delete(key)
                if (allKeys.size === 0) {
                    selectedKeys.value = new Set()
                } else {
                    selectedKeys.value = allKeys
                }
            } else {
                selectedKeys.value = 'all'
            }
        } else {
            const keys = selectedKeys.value as Set<ItemKey>
            if (keys.has(key)) {
                keys.delete(key)
            } else {
                keys.add(key)
            }
        }
    }

    function isItemSelected(item: T) {
        if (isItemDisabled(item)) {
            return false
        } else {
            const itemKey = options.getKey(item)
            return selectedKeys.value === 'all' || selectedKeys.value.has(itemKey)
        }
    }

    function isItemDisabled(item: T) {
        return !!options.disabledCondition && options.disabledCondition(item)
    }

    function unselectItem(key: ItemKey) {
        if (selectedKeys.value === 'all') {
            const allKeys = new Set(unref(items).map(options.getKey))
            allKeys.delete(key)
            if (allKeys.size === 0) {
                selectedKeys.value = new Set()
            } else {
                selectedKeys.value = allKeys
            }
        } else {
            const keys = selectedKeys.value as Set<ItemKey>
            keys.delete(key)
        }
    }


    const collection = computed(() => {
        return unref(items).map((item) => {
            const itemKey = options.getKey(item)
            return {
                key: options.getKey(item),
                data: item,
                value: options.getDisplayValue(item),
                isSelected: computed(() => isItemSelected(item)),
                isDisabled: computed(() => isItemDisabled(item)),
                toggle: () => toggleItem(itemKey),
                unselect: () => unselectItem(itemKey)
            }
        })
    })

    const selected = computed(() => {
        return uniqBy(
            unref(items).filter((item) => {
                return isItemSelected(item)
            }),
            (item) => options.getKey(item)
        )
    })

    return {
        collection,
        selected
    }
}
