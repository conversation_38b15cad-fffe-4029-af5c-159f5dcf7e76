import { add, differenceInCalendarMonths, format, getMonth, getYear, isAfter, isBefore, isValid, parse, startOfDay } from 'date-fns'
import { de } from 'date-fns/locale'

export function useDateInput() {
    const dateInputValue = ref<string | null>('')
    const isTyping = ref(false)
    const isInternalUpdate = ref(false)

    const DATE_FORMAT = 'dd.MM.yyyy'
    const DATE_INPUT_MAX_LENGTH = 10

    const formatDateForInput = (date: Date) => format(date, DATE_FORMAT, { locale: de })

    const parseInputDate = (value: string): Date | null => {
        if (!value) return null

        const formats = [DATE_FORMAT, 'd.M.yyyy', 'dd.MM.yy', 'd.M.yy']

        for (const formatStr of formats) {
            try {
                const parsed = parse(value, formatStr, new Date(), { locale: de })
                if (isValid(parsed)) return parsed
            } catch {
                continue
            }
        }

        return null
    }

    const formatInputAsUserTypes = (value: string): string => {
        const digits = value.replace(/\D/g, '').slice(0, 8)
        let formattedValue = ''

        if (digits.length > 0) formattedValue += digits.slice(0, 2)
        if (digits.length > 2) formattedValue += '.' + digits.slice(2, 4)
        if (digits.length > 4) formattedValue += '.' + digits.slice(4, 8)

        return formattedValue
    }

    const handleInputChange = (event: Event, onValidDate?: (date: Date | null) => void) => {

        const target = event.target as HTMLInputElement
        const value = target.value

        isTyping.value = true
        isInternalUpdate.value = true

        const formattedValue = formatInputAsUserTypes(value)
        dateInputValue.value = formattedValue

        if (!value.trim()) {
            dateInputValue.value = null
            onValidDate?.(null)
            isInternalUpdate.value = false
            return
        }

        const parsedDate = parseInputDate(formattedValue)
        if (parsedDate && isValid(parsedDate) && formattedValue.length === DATE_INPUT_MAX_LENGTH) {
            onValidDate?.(parsedDate)
        } else if (!isValid(parsedDate) && formattedValue.length === 0) {
            onValidDate?.(null)
        }

        isInternalUpdate.value = false
    }

    const handleInputBlur = (currentSelectedDate: Date | null, onValidDate?: (date: Date) => void) => {
        isTyping.value = false

        const formattedValue = parseInputDate(dateInputValue.value)
        if (!isValid(formattedValue)) {
            dateInputValue.value = null
            onValidDate?.(null)
            isInternalUpdate.value = false
        }
        const parsedDate = dateInputValue.value ? parseInputDate(dateInputValue.value) : parseInputDate('')
        if (parsedDate && isValid(parsedDate)) {
            isInternalUpdate.value = true
            onValidDate?.(parsedDate)
            dateInputValue.value = formatDateForInput(parsedDate)
            isInternalUpdate.value = false
        }
    }

    const syncInputWithDate = (date: Date | null) => {
        if (date && !isInternalUpdate.value && !isTyping.value) {
            dateInputValue.value = formatDateForInput(date)
        }
    }

    return {
        dateInputValue,
        isTyping,
        isInternalUpdate,
        DATE_INPUT_MAX_LENGTH,
        parseInputDate,
        handleInputChange,
        handleInputBlur,
        syncInputWithDate
    }
}

export function useCalendarNavigation(initialDate: Date) {
    const calendarMonth = ref<number>(getMonth(initialDate))
    const calendarYear = ref<number>(getYear(initialDate))
    const currentMonthDate = ref<Date>(initialDate)

    const YEAR_RANGE_SIZE = 20
    const MONTHS_COUNT = 12

    const monthDisplayName = computed(() => de.localize.month(calendarMonth.value, { width: 'wide' }))

    const availableYears = computed(() => {
        const centerYear = calendarYear.value || getYear(new Date())
        const halfRange = Math.floor(YEAR_RANGE_SIZE / 2)
        const startYear = centerYear - halfRange
        return Array.from({ length: YEAR_RANGE_SIZE }, (_, i) => startYear + i)
    })

    const getMonthName = (monthIndex: number) => de.localize.month(monthIndex, { width: 'wide' })

    const updateCalendarNavigation = (targetDate: Date) => {
        calendarMonth.value = getMonth(targetDate)
        calendarYear.value = getYear(targetDate)
        currentMonthDate.value = targetDate
    }

    const navigateMonth = (direction: number) => {
        const newDate = add(currentMonthDate.value, { months: direction })
        updateCalendarNavigation(newDate)
        return newDate
    }

    const navigateToMonthYear = (month: number, year: number) => {
        const targetDate = new Date(year, month, 1)
        const monthDiff = differenceInCalendarMonths(targetDate, currentMonthDate.value)
        if (monthDiff !== 0) {
            return navigateMonth(monthDiff)
        }
        return currentMonthDate.value
    }

    const handleMonthSelect = (newMonth: number) => {
        return navigateToMonthYear(newMonth, calendarYear.value)
    }

    const handleYearSelect = (newYear: number) => {
        return navigateToMonthYear(calendarMonth.value, newYear)
    }

    return {
        calendarMonth,
        calendarYear,
        currentMonthDate,
        monthDisplayName,
        availableYears,
        MONTHS_COUNT,
        getMonthName,
        updateCalendarNavigation,
        navigateMonth,
        handleMonthSelect,
        handleYearSelect
    }
}
