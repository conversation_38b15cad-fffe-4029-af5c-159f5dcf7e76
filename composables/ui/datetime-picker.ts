import { format, parseISO, isValid } from 'date-fns'

type PickerRef = {
    [key: string]: HTMLInputElement
}

export function useDateTimePicker(modelValue: Ref<Date | null>) {
    const isKeyboardInteraction = ref(false)
    const isMouseInteraction = ref(false)
    const isExternalUpdate = ref(false)

    const date = ref<string | null>(modelValue.value ? format(modelValue.value, 'yyyy-MM-dd') : null)
    const time = ref<string | null>(modelValue.value ? format(modelValue.value, 'HH:mm') : null)

    const timeRef = ref<PickerRef | null>(null)

    watch(modelValue, (newValue) => {
        if (isExternalUpdate.value) {
            isExternalUpdate.value = false

            if (!newValue && date.value) {
                modelValue.value = null
                time.value = null
            } else if (!newValue && time.value) {
                modelValue.value = null
                date.value = null
            } else if (newValue) {
                date.value = format(newValue, 'yyyy-MM-dd')
                time.value = format(newValue, 'HH:mm')
            }
            return
        }

        if (!newValue || !isValid(newValue)) {
            date.value = null
            time.value = null
            return
        }

        date.value = format(newValue, 'yyyy-MM-dd')
        time.value = format(newValue, 'HH:mm')
    })

    watch(date, (newDate) => {
        if (!newDate || !time.value) {
            isExternalUpdate.value = true
            modelValue.value = null
            return
        }

        const dateTimeString = `${newDate}T${time.value}`
        const parsedDate = parseISO(dateTimeString)

        if (!isValid(parsedDate)) {
            isExternalUpdate.value = true
            modelValue.value = null
            return
        }

        isExternalUpdate.value = true
        modelValue.value = parsedDate
    })

    watch(time, (newTime) => {
        if (!newTime || !date.value) {
            isExternalUpdate.value = true
            modelValue.value = null
            return
        }

        const dateTimeString = `${date.value}T${newTime}`
        const parsedDate = parseISO(dateTimeString)

        if (!isValid(parsedDate)) {
            isExternalUpdate.value = true
            modelValue.value = null
            return
        }

        isExternalUpdate.value = true
        modelValue.value = parsedDate
    })

    const onKeyboardDown = () => {
        isKeyboardInteraction.value = true
    }

    const onMouseDown = () => {
        isKeyboardInteraction.value = false
        isMouseInteraction.value = true
    }

    const onDateChange = () => {
        if (isKeyboardInteraction.value) return

        if (timeRef.value?.timeInputRef) {
            timeRef.value.timeInputRef.focus()
        }

        isKeyboardInteraction.value = false
        isMouseInteraction.value = false
    }

    return {
        date,
        time,
        timeRef,
        onDateChange,
        onKeyboardDown,
        onMouseDown,
    }
}
