import { MaybeRef, reactiveComputed } from '@vueuse/core'
import { DateTime, Interval } from 'luxon'
import { differenceInCalendarDays, differenceInDays, addDays, getHours, getMinutes, setHours, setMinutes, isValid, isAfter } from 'date-fns'

type Options = {
    boundaries?: { start: Date; end: Date }
    minDuration?: number
    maxDuration?: number
    useMinMax?: boolean
}

export function useIntervalInput(start: Ref<Date>, end: Ref<Date>, options: MaybeRef<Options> = {}) {
    const updateTimeAndAddDay = (date: Date, diffInDays: number, hours: number, minutes: number) => {
        if (!date || !isValid(date)) return null

        let newDate = addDays(date, diffInDays)
        newDate = setHours(newDate, hours)
        newDate = setMinutes(newDate, minutes)

        return newDate
    }

    function initializeDates() {
        const currentDateTime = new Date()
        currentDateTime.setSeconds(0)

        start.value = currentDateTime
        end.value = currentDateTime

        // We need this to render correct values instead of null, otherwise code execution is not synced with DOM rendering
        return nextTick()
    }

    function syncEndDate(newStartValue: Date | null, oldStartValue: Date | null) {
        if (!newStartValue || !oldStartValue || !end.value) return

        const endDateHours = getHours(end.value)
        const endDateMinutes = getMinutes(end.value)

        const currentDiffInDays = differenceInDays(end.value, oldStartValue)
        const currentDiffInCalendarDays = differenceInCalendarDays(end.value, oldStartValue)

        const hasLessThenOneDayDiff = currentDiffInCalendarDays - currentDiffInDays === 1

        const updatedDate = computed(() =>
            hasLessThenOneDayDiff
                ? updateTimeAndAddDay(newStartValue, currentDiffInCalendarDays, endDateHours, endDateMinutes)
                : updateTimeAndAddDay(newStartValue, currentDiffInDays, endDateHours, endDateMinutes)
        )

        if (!!unref(options)?.boundaries?.end && isAfter(updatedDate.value, unref(options)?.boundaries?.end)) {
            end.value = unref(options).boundaries.end
            return
        }

        end.value = updatedDate.value
    }

    // We need this to render correct values instead of null, otherwise code execution is not synced with DOM rendering
    onMounted(async () => {
        if (start.value === null) {
            await initializeDates()
        }
    })

    watch(start, (newValue, oldValue) => {
        syncEndDate(newValue, oldValue)
    })

    const maxDate = new Date('9999-12-24T00:00')

    /**
     * ! This is _not_ a Luxon.Interval
     */
    const interval = computed(() => {
        return {
            start: start.value ? DateTime.fromJSDate(start.value) : null,
            end: end.value ? DateTime.fromJSDate(end.value) : null
        }
    })

    const boundaries = computed(() => {
        if (!unref(options).boundaries) {
            return null
        }

        const boundaries = Interval.fromDateTimes(
            //..
            unref(options).boundaries.start,
            unref(options).boundaries.end
        )

        if (!boundaries.isValid) {
            return null
        }

        return boundaries
    })

    const startAttrs = reactiveComputed(() => {
        const min = boundaries.value?.start
        const max = DateTime.min(
            //...
            ...[interval.value.end, boundaries.value?.end].filter((date) => !!date)
        )

        const attributes = {
            'modelValue': start.value,
            'onUpdate:modelValue': (value: Date) => (start.value = value),
            'min': min?.toJSDate(),
            'max': max?.toJSDate() || maxDate,
            'required': true
        }

        if (!unref(options).useMinMax || typeof unref(options).boundaries === 'undefined') {
            attributes['max'] = maxDate
        }

        if (!unref(options).useMinMax) {
            attributes['min'] = null
        }

        return attributes
    })

    const endAttrs = reactiveComputed(() => {
        const min = DateTime.max(
            //...
            ...[interval.value.start, boundaries.value?.start].filter((date) => !!date)
        )

        const max = boundaries.value?.end

        const attributes = {
            'modelValue': end.value,
            'onUpdate:modelValue': (value: Date) => (end.value = value),
            'min': min?.toJSDate(),
            'max': max?.toJSDate() || maxDate,
            'disabled': !start.value,
            'isEndDate': true
        }

        if (!unref(options).useMinMax) {
            attributes['min'] = null
            attributes['max'] = maxDate
        }

        return attributes
    })

    return {
        startAttrs,
        endAttrs
    }
}

type PickerRef = {
    [key: string]: HTMLInputElement
}

export function useNextPickerFocus() {
    const nextPickerRef = ref<{ focus: () => void } | null>(null)

    function completePicker() {
        nextTick(() => {
            nextPickerRef.value?.focus()
        })
    }

    return {
        nextPickerRef,
        completePicker
    }
}
