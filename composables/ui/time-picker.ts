export function useTimePicker(modelValue: Ref<string>) {
    const hourOptions = Array.from({ length: 24 }).map((_, i) => i.toString().padStart(2, '0'))
    const minutesOptions = Array.from({ length: 12 }).map((_, i) => (i * 5).toString().padStart(2, '0'))

    const selectedHour = computed(() => modelValue.value?.split(':')[0])
    const selectedMinutes = computed(() => modelValue.value?.split(':')[1])

    const selectHour = (hour: string) => {
        modelValue.value = selectedMinutes.value ? `${hour}:${selectedMinutes.value}` : `${hour}:00`
    }

    const selectMinutes = (minute: string) => {
        modelValue.value = selectedHour.value ? `${selectedHour.value}:${minute}` : `00:${minute}`
    }

    return {
        hourOptions,
        minutesOptions,
        selectedHour,
        selectedMinutes,
        selectHour,
        selectMinutes
    }
}
