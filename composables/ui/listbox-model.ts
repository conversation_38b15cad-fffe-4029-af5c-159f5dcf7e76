import { MaybeRef } from '@vueuse/core'
import { WritableComputedRef } from 'nuxt/dist/app/compat/capi'

export function useListboxModel<T extends { id: number | string }>(
    modelValue: Ref<T | Array<T>>,
    collection: MaybeRef<T[]>,
    multiple: boolean
): WritableComputedRef<T['id'] | T['id'][]> {
    function isMultipleModelValue(modelValue: Ref<any>): modelValue is Ref<Array<T>> {
        return !!multiple
    }

    function isSingleModelValue(modelValue: Ref<any>): modelValue is Ref<T> {
        return !multiple
    }

    if (isMultipleModelValue(modelValue)) {
        return computed({
            get: () => {
                return modelValue.value?.map(({ id }) => id) || []
            },
            set: (value) => {
                modelValue.value = unref(collection).filter(({ id }) => value.includes(id)) ?? []
            }
        })
    }

    if (isSingleModelValue(modelValue)) {
        return computed({
            get: () => {
                return modelValue.value?.id || null
            },
            set: (value) => {
                modelValue.value = unref(collection)?.find(({ id }) => id === value) ?? null
            }
        })
    }
}
