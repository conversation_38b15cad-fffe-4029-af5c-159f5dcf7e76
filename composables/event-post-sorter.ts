import { type MaybeRef, reactiveComputed } from '@vueuse/core'
import orderBy from 'just-order-by'

const sortingOptions = {
    'a-z': { label: 'Planstellen A-Z', direction: 'asc' },
    'z-a': { label: 'Planstellen Z-A', direction: 'desc' },
    'date-from': { label: 'Beginn', direction: 'asc' }
} as const

type SortKey = keyof typeof sortingOptions

export function useEventPostSorter(input: MaybeRef<ApiModel<'EventPost'>[]>) {
    // Default sorting is "by date"
    const selectedKey = ref<SortKey>('date-from')

    const selectedOption = computed(() => {
        return sortingOptions[selectedKey.value]
    })

    const alternatives = computed(() => {
        const keys = Object.keys(sortingOptions).filter((key) => {
            return key !== selectedKey.value
        }) as SortKey[]

        return keys.map((key) => {
            return {
                key,
                label: sortingOptions[key].label,
                set: () => (selectedKey.value = key)
            }
        })
    })

    const sortedEventPosts = computed(() => {
        switch (selectedKey.value) {
            case 'date-from': {
                return orderBy(unref(input), [
                    {
                        property(eventPost) {
                            return eventPost.dateFrom
                        },
                        order: 'asc'
                    },
                    {
                        property(eventPost) {
                            return eventPost.description.toLocaleLowerCase()
                        },
                        order: 'asc'
                    }
                ])
            }

            case 'z-a': {
                return orderBy(unref(input), [
                    {
                        property(eventPost) {
                            return eventPost.description.toLocaleLowerCase()
                        },
                        order: 'desc'
                    },
                    {
                        property: 'id',
                        order: 'asc'
                    }
                ])
            }

            case 'a-z':
            default: {
                return orderBy(unref(input), [
                    {
                        property(eventPost) {
                            return eventPost.description.toLocaleLowerCase()
                        },
                        order: 'asc'
                    },
                    {
                        property: 'id',
                        order: 'asc'
                    }
                ])
            }
        }
    })

    const eventPostSorter = reactiveComputed(() => {
        return {
            icon: selectedOption.value.direction === 'asc' ? ('sort-ascending' as const) : ('sort-descending' as const),
            label: selectedOption.value.label,
            alternatives: alternatives.value
        }
    })

    return {
        eventPostSorter,
        sortedEventPosts
    }
}
