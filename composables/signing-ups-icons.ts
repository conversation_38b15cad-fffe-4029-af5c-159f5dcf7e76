export const useSigningUpsIcons = () => {

    const getIcon = (resource: string) => {
        switch (resource) {
            case 'Fahrzeuge':
                return 'delivery-truck';
            case 'Pers. Ausrüstung':
                return 'work-clothes';
            case 'Mengenartikel':
                return 'layers';
            case 'Funktechnik':
                return 'wifi';
            case 'Einzelgeräte/Satz':
                return 'briefcase';
            case 'Rettungshunde':
                return 'service-dog';
            case 'Dienstpferde':
                return 'service-horse';
            default:
                return 'wrench';
        }
    }

    const getSigningUpResourceType = (signingUp: ApiModel<'SigningUp'>): string=> {
        const resourceType: string = signingUp.resource.external ? signingUp.resource.externalArticle.type?.name : signingUp.resource.article.type?.name
        return resourceType;
    }

    return { getIcon, getSigningUpResourceType }
}