import { useQueryClient } from '@tanstack/vue-query'
import { <PERSON><PERSON><PERSON> } from 'buffer'
import { useNotifications } from './notifications'

export type ReportOption = {
    fileType: string
    fileOption: string
    fileExtension?: string
}

const MIME_TYPES: Record<string, string> = {
    'pdf': 'application/pdf',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'xls': 'application/vnd.ms-excel',
    'csv': 'text/csv'
}

export const useCreateReport = function () {
    const queryClient = useQueryClient()
    const { schedule } = useNotifications()
    const message = schedule()

    const getDocument = async (eventId: number, fileData: ReportOption) => {

        let queryKey = [`print-${eventId}-${fileData.fileType}`]
        let url = `/api/events/${eventId}/print?type=${fileData.fileOption}`
        let method = 'GET'
        let body: any = null

        if (fileData.fileType === 'COMMUNICATION_LIST' || fileData.fileType === 'NAME_LIST' || fileData.fileType === 'PARTICIPANTS_LIST') {
            queryKey = [`report-${eventId}-${fileData.fileType}`]
            url = `/api/events/${eventId}/report`
            method = 'POST'
            body = {
                reportType: fileData.fileType,
                fileType: fileData.fileExtension,
                dataBasis: fileData.fileOption
            }
        }

        if (fileData.fileType === 'SORTED_EXPORT') {
            queryKey = [`ordered-export-${eventId}-${fileData.fileType}`]
            url = `/api/events/${eventId}/registrations/ordered-export?sortOrder=${fileData.fileOption}`
        }

        message.show('Deine Datei wird heruntergeladen.', 'waiting')

        const fetchOptions: any = {
            method,
            responseType: 'blob'
        }

        if (method === 'POST' && body) {
            fetchOptions.body = body
        }

        const response: Blob = await queryClient.fetchQuery({
            queryKey,
            queryFn: async () => {
                return await $fetch(url, fetchOptions)
            }
        })

        if (response.size === 0) {
            message.hide()
            message.show('Das hat nicht geklappt: Zu der Liste, die du ausgewählt hast, gibt es keine Daten in diesem Ereignis.')
            setTimeout(() => {
                message.hide()
            }, 5000)
            return
        }

        if (response instanceof Blob) {
            message.makeSuccess('Das hat geklappt: Du hast die Datei heruntergeladen.')
            const fileExtension = fileData.fileExtension?.toLowerCase() || 'pdf'
            const mimeType = MIME_TYPES[fileExtension] || 'application/pdf'

            if (mimeType === 'text/csv') {
                const arrayBuffer = await response.arrayBuffer()
                const buffer = Buffer.from(arrayBuffer).toString('base64')
                return 'data:' + mimeType + ';base64,' + buffer
            }
            const buffer = Buffer.from(await response.text(), 'base64')
            return 'data:' + mimeType + ';base64,' + buffer.toString('base64')

        }

        message.makeError('Das hat nicht geklappt: Beim Erstellen der Datei hat es einen Fehler gegeben.')
    }

    return {
        getDocument
    }
}
