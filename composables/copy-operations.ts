
export interface CopyOperation {
    id: string
}

/**
 * Global composable to manage copy operations state
 */
export const useCopyOperations = () => {
    const operations = useState<CopyOperation[]>('copy-operations-state', () => [])

    const isAnyCopyOperationRunning = computed(() => {
        return operations.value.length > 0
    })

    const startCopyOperation = () => {
        const operationId = crypto.randomUUID()
        const operation: CopyOperation = {
            id: operationId,
        }

        operations.value.push(operation)
        return operationId
    }

    const completeCopyOperation = (operationId: string) => {
        operations.value = operations.value.filter(op => op.id !== operationId)
    }

    return {
        operations: readonly(operations),
        isAnyCopyOperationRunning,
        startCopyOperation,
        completeCopyOperation
    }
}
