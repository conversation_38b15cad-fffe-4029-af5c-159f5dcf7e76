import { MaybeRef, useConfirmDialog, type UseConfirmDialogReturn } from '@vueuse/core'

export type SigningUpRequest = [type: 'TECHNIC', resource: ApiModel<'TechnicArticle'>] | [type: 'PERSONAL', resource: ApiModel<'MemberData'>]

type EventPlanningDialogControllers = {
    // eventPostDialog: UseConfirmDialogReturn<{ eventPost: ApiModel<'EventPost'>; defaultTabIndex?: 0 | 1 }, null, null>

    // createEventPostsDialog: UseConfirmDialogReturn<ApiModel<'SigningUp'> | null, ApiModel<'EventPost'>[], null>

    // editEventPostDialog: UseConfirmDialogReturn<ApiModel<'EventPost'>, ApiModel<'EventPost'>, null>

    // removeEventPostDialog: UseConfirmDialogReturn<null, null, null>

    // manageResourcesDialog: UseConfirmDialogReturn<ApiModel<'EventPost'> | null, null, null>

    // editIntervalSigningUpDialog: UseConfirmDialogReturn<ApiModel<'ResourceSetting'>, Interval[], null>

    // createExternalMaterialDialog: UseConfirmDialogReturn<null, ApiModel<'ExternalTechnicArticle'>, null>

    // createExternalPersonDialog: UseConfirmDialogReturn<null, ApiModel<'ExternalPerson'>, null>

    selectEventPostDialog: UseConfirmDialogReturn<ApiModel<'SigningUp'>, ApiModel<'EventPost'>, null>

    // maurerSchemeDialog: UseConfirmDialogReturn<null, ApiModel<'EventPost'>[], null>
}

type EventPlanningDialogNames = keyof EventPlanningDialogControllers

export type EventPlanningDialogTypes<T extends EventPlanningDialogNames> = EventPlanningDialogControllers[T]

export function useEventPlanningDialogController<T extends EventPlanningDialogNames>(dialog: T) {
    return useConfirmDialog() as EventPlanningDialogTypes<T>
}

export const EventPlanningDialogs = Symbol() as InjectionKey<EventPlanningDialogControllers>

type EventPlanningActionsDefinitions = {
    createEventPosts: (eventPosts: ApiModel<'EventPost'>[]) => Promise<any>

    removeEventPost: (eventPost: ApiModel<'EventPost'>) => Promise<any>

    duplicateEventPost: (eventPost: ApiModel<'EventPost'>) => Promise<any>

    editEventPost: (eventPost: ApiModel<'EventPost'>) => Promise<any>

    createResourceSetting: (eventPost: ApiModel<'EventPost'>, signingUp: ApiModel<'SigningUp'>) => Promise<any>

    editResourceSetting: (resourceSetting: ApiModel<'ResourceSetting'>) => Promise<any>

    removeResourceSetting: (resourceSetting: ApiModel<'ResourceSetting'>) => Promise<any>

    /**
     * Signature for "Remove this signing up from all event posts"
     *
     * @param signingUp
     */
    removeFromResourceSettings: (signingUp: ApiModel<'SigningUp'>) => Promise<any>

    /**
     * Signature for "Select a signing up for the given event post"
     *
     * @param signingUp
     */
    selectSigningUp: (signingUp: ApiModel<'EventPost'>) => Promise<any>
}

export const EventPlanningActions = Symbol() as InjectionKey<EventPlanningActionsDefinitions>

import SigningUpStatus from '~~/enums/event-signing-up-status'

type EventManageResourcesActionsDefinitions = {
    /**
     * Create an event post for the given signing up AND create a resource setting afterwards
     *
     */
    createEventPost: (signingUp: ApiModel<'SigningUp'>) => Promise<ApiModel<'ResourceSetting'>>

    selectEventPost: (signingUp: ApiModel<'SigningUp'>) => Promise<any>

    createSigningUp: (resource: ApiModel<'Resource'>) => Promise<any>

    removeSigningUp: (signingUp: ApiModel<'SigningUp'>) => Promise<any>

    changeSigningUpStatus: (signingUp: ApiModel<'SigningUp'>, status: SigningUpStatus) => Promise<any>

    editExternalMaterial: (signingUp: ApiModel<'SigningUp'>) => Promise<any>
}

export const EventManageResourcesActions = Symbol() as InjectionKey<EventManageResourcesActionsDefinitions>

/**
 * Provide all functionality that's needed for the planning of an event
 *
 * @param input
 */
export const useEventPlanning = function (input: MaybeRef<ApiModel<'Event'>>) {
    /**x
     * Unref event and make it available as read only computed ref
     */
    const event = computed(() => {
        return unref(input)
    })

    /**
     * This is the reference to event queries. We need:
     *
     * - siginingUps
     * - resourceSettings
     * - conflicts?
     * - eventPosts
     */
    const eventQueries = useEventQueries(event.value.id)

    /**
     * Fetch and store event posts as `eventPosts`
     */
    const { data: eventPosts } = eventQueries.eventPosts()

    /**
     * Fetch and store signing ups as `resourceSettings`
     */
    const { data: resourceSettings, isFetched, isLoading, isFetching } = eventQueries.resourceSettings()

    /**
     * Fetch and store signing ups as `signingUps`
     */
    const { data: signingUps } = eventQueries.signingUps()
    /**
     *  Event Post conflicts
     */
    const { data: eventPostsConflicts } = useEventQueries(event.value.id).conflicts()

    function signingUpsByResourceType(type: MaybeRef<ApiModel<'Resource'>['type']>) {
        return computed(() => {
            return signingUps.value?.filter(({ resource }) => resource.type === unref(type)) || []
        })
    }

    function signingUpsByEventPostId(id: ApiModel<'EventPost'>['id']) {
        return computed(() => {
            return (
                signingUps.value?.filter((signingUp) =>
                    signingUp.assignedResourceSettings?.some((resourceSetting) => resourceSetting.eventPost.id === id)
                ) || []
            )
        })
    }

    function resourceSettingsByEventPostId(id: ApiModel<'EventPost'>['id']) {
        return computed(() => {
            return (
                resourceSettings.value?.filter(({ eventPost }) => {
                    return eventPost.id === id
                }) || []
            )
        })
    }

    function eventPostsForResourceType(type: MaybeRef<ApiModel<'EventPost'>['eventPostResourceType']>) {
        return computed(() => {
            return eventPosts.value.filter(({ eventPostResourceType }) => eventPostResourceType === unref(type))
        })
    }

    function conflictsByEventPostId(id: ApiModel<'EventPost'>['id']) {
        return computed(() => {
            return (
                eventPostsConflicts.value
                    // Find this event post in conflicts collection and
                    // return the array of conflicts grouped by resource id.
                    ?.find((conflict) => conflict.eventPostId === id)?.eventPostConflicts || []
            )
        })
    }

    /**
     * Generate and "import" all mutation queries
     */
    const mutations = useEventMutations(event)

    /**
     * Provide a convenient way to call event mutations without the hassle of naming conflicts
     *
     * @param mutation
     * @param payload
     * @param options
     */
    function commit<T extends keyof typeof mutations, P extends Parameters<(typeof mutations)[T]>[0], O extends Parameters<(typeof mutations)[T]>[1]>(
        mutation: T,
        payload?: P,
        options?: O
    ): Promise<ReturnType<(typeof mutations)[T]>> {
        return mutations[mutation](payload as any, options as any)
    }

    return {
        isFetched,
        isLoading,
        isFetching,
        event,
        signingUps,
        eventPosts,
        resourceSettings,
        resourceSettingsByEventPostId,
        signingUpsByResourceType,
        signingUpsByEventPostId,
        eventPostsForResourceType,
        conflictsByEventPostId,

        commit
    }
}

/**
 * The key that can be used for injections
 */
export const EventPlanningKey = Symbol() as InjectionKey<ReturnType<typeof useEventPlanning>>
