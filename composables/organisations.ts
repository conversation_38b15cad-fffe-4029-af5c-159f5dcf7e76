import { useMutation } from '@tanstack/vue-query'

type SearchListResponse = {
    items: ApiModel<'OrganizationWithHierarchy'>[]
}

export const useOrganisationSearch = function () {
    const {
        mutate: organisationSearch,
        isLoading,
        isError,
        data
    } = useMutation(async ({ organisationIds, query, limit, organisationIdsWithChilds }: { organisationIds: number[]; query?: string; limit?: number, organisationIdsWithChilds?: string }) => {
        return await $fetch<SearchListResponse>(`/api/organisations`, {
            body: {
                offset: 0,
                limit: limit ? limit : 10,
                sortBy: [
                    {
                        field: 'CHAIN_OF_COMMAND_ENABLED',
                        directionAsc: true
                    }
                ],
                types: ["F", "M", "LD", "B"],
                organisationIdsWithChilds: organisationIdsWithChilds ? organisationIdsWithChilds : "ALL",
                searchTerm: query,
                organisationIds
            },
            method: 'post'
        })
    })

    return {
        organisationSearch,
        isLoading,
        isError,
        data
    }
}
