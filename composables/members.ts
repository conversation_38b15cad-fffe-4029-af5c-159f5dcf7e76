import { useMutation } from '@tanstack/vue-query'

type MemberQueryResult = {
    totalItems: number
    offset: number
    limit: number
    items: ApiModel<'MemberData'>[]
}

export const useMemberSearch = function () {
    return useMutation(async ({ query, limit }: QueryVariablesType) => {
        const payload = await $fetch<MemberQueryResult>(`/api/members/search`, {
            body: {
                searchScope: 'MY_MEMBERS',
                offset: 0,
                limit,
                sortBy: [
                    {
                        field: 'FIRSTNAME',
                        directionAsc: true
                    }
                ],
                searchTerm: query,
                organisationIds: [],
                leadingOrganisationIds: [],
                withAvatar: false
            },
            method: 'post'
        })

        return payload.items
    })
}

export const useGetMemberProfileImage = function () {
    const file = ref<string | null>(null)
    const {
        mutate: getMemberProfileImage,
        isLoading,
        isError,
        data
    } = useMutation(async (fileId: number) => $fetch<string>(`api/members/${fileId}/profile-image`), {
        onSuccess: async (_data) => {
            file.value = _data === '' ? null : `data:image/png;base64,${_data}`
        }
    })

    return {
        getMemberProfileImage,
        isLoading,
        isError,
        data: file || data
    }
}

export interface Contact {
    contact: string,
    priority: 1 | 2 | 3
}

// Collect all values, remove duplicates, sort by priority ascending, then by contact ascending
export const useFilterAndSortMemberContacts = (validTypes: string[], communications: ApiModel<'Communications'>[]): Contact[] => {
    const uniqueContacts = new Map<string, Contact>();

    communications
        .filter(item => validTypes.includes(item.communicationType.value1))
        .forEach(item => {
            if (!uniqueContacts.has(item.contact)) {
                uniqueContacts.set(item.contact, {
                    contact: item.contact,
                    priority: item.priority
                });
            }
        });

    return Array.from(uniqueContacts.values()).sort((a, b) => {
        if (a.priority !== b.priority) {
            return a.priority - b.priority;
        }
        return a.contact.localeCompare(b.contact);
    });
};

export type memberDataDialog = {
    memberData: ApiModel<'MemberData'> | ApiModel<'ExternalPerson'>,
}

export interface ViewMemberData {
   openMemberDataDialog: (memberData: ApiModel<'MemberData'> | ApiModel<'ExternalPerson'>) => void;
}

// Open dialog with member masterdata
export function useMemberDataDialog()  {

    const dialog = {
        viewMemberData: useDialogController('viewMemberData'),
    }

    async function reveal(memberData: ApiModel<'MemberData'> | ApiModel<'ExternalPerson'>) {
        await dialog.viewMemberData.reveal(memberData)
    }

    return {
        reveal,
        dialog
    }
}

// telephone or email priiority
export const useGetPriorityText = (priority: number) => {
    switch (priority) {
        case 1:
            return 'Kann generell verwendet werden'
        case 2:
            return 'Kann in wichtigen Angelegenheiten verwendet werden'
        case 3:
            return 'Kann nur in Einsatzfällen notfalls verwendet werden'
        default:
            return ''
    }
}
