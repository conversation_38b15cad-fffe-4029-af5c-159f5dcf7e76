import {
    differenceInMilliseconds, addDays, differenceInDays, startOfDay, parseISO,
    differenceInCalendarWeeks, startOfWeek, format, addWeeks, addHours,
    addMilliseconds, differenceInCalendarMonths, getDaysInMonth,
    startOfMonth, lastDayOfMonth, getDay,
    addMonths, setDay, setDate,
    isSameMonth
} from 'date-fns'
import { de, enUS } from 'date-fns/locale'

export const useCopyEvents = (events: Ref<ApiModel<'Event'>[]>, oryginalEvent: ApiModel<'Event'>) => {

    const originalEventDuration = useEventDuration(oryginalEvent)

    function addEvent() {
        const newEvent = useModelFactory('Event').create({
            ...oryginalEvent,
            number: null,
            id: Math.random(),
        })

        events.value = [...events.value, newEvent]
    }

    function removeEvent(id?: number) {
        events.value = events.value.filter(event => event.id !== id)
    }

    const getWeekDays = (date = new Date(), locale = de) => {
        const start = startOfWeek(date)
        return Object.values(Array.from({ length: 7 }, (_, i) => ({
            id: i,
            label: format(addDays(start, i), 'EEEE', { locale })
        })))
    }

    function getNthWeekdayOfMonth(year: number, month: number, weekday: number, n: number | 'last') {
        const firstOfMonth = startOfMonth(new Date(year, month))

        if (n === 'last') {
            const lastOfMonth = lastDayOfMonth(firstOfMonth)
            const dayOfWeek = getDay(lastOfMonth)
            const diff = (dayOfWeek - weekday + 7) % 7
            return addDays(lastOfMonth, -diff)
        }

        const firstTarget = setDay(firstOfMonth, weekday, { weekStartsOn: 1 })
        if (getDay(firstOfMonth) > weekday) {
            return addDays(firstTarget, 7 * n)
        }
        return addDays(firstTarget, 7 * (n - 1))
    }

    const weekDays = ref<{ id: number, label: string }[]>(getWeekDays())

    function getEventsByInterval(
        interval: number, options: {
            typeOfInterval: string, weekInterval: { firstDay: number, lastDay: number },
            monthInterval: { type: string, specificDayInMonth: number, everyDayInMonth: { type: string, day: { id: number, label: string } } },
        }, endDate: Date) {

        if (!endDate) {
            return
        }

        const oryginalStartDate = oryginalEvent.dateFrom
        const dateFrom = startOfDay(oryginalEvent.dateFrom)
        const dateUpTo = startOfDay(parseISO(endDate.toString()))
        const numberOfDays = differenceInDays(dateUpTo, dateFrom)
        const startHour = oryginalEvent.dateFrom.getHours()

        const newEvents: ApiModel<'Event'>[] = []

        if (options.typeOfInterval === 'day') {
            for (let i = 0; i <= numberOfDays; i = i + interval) {
                const newEvent = useModelFactory('Event').create({
                    ...oryginalEvent,
                    number: null,
                    id: Math.random(),
                    dateFrom: addDays(oryginalStartDate, i),
                    dateUpTo: addDays(oryginalEvent.dateUpTo, i),
                })
                newEvents.push(newEvent)
            }
            events.value = newEvents
        }

        if (options.typeOfInterval === 'week' && options.weekInterval.firstDay !== null) {
            const numberOfWeeks = differenceInCalendarWeeks(dateUpTo, dateFrom, { weekStartsOn: 1 })
            const firstDayOfWeek = startOfWeek(oryginalEvent.dateFrom, { weekStartsOn: 1 })

            for (let i = 0; i <= numberOfWeeks; i = i + interval) {
                const week = addWeeks(firstDayOfWeek, i)
                if (options.weekInterval.lastDay !== null) {
                    for (let j = options.weekInterval.firstDay; j <= options.weekInterval.lastDay; j++) {
                        const newEvent = useModelFactory('Event').create({
                            ...oryginalEvent,
                            number: null,
                            id: Math.random(),
                            dateFrom: addHours(addDays(week, j), startHour),
                            dateUpTo: addMilliseconds(addHours(addDays(week, j), startHour), originalEventDuration),
                        })
                        newEvents.push(newEvent)
                    }
                } else {
                    const newEvent = useModelFactory('Event').create({
                        ...oryginalEvent,
                        number: null,
                        id: Math.random(),
                        dateFrom: addHours(addDays(week, options.weekInterval.firstDay), startHour),
                        dateUpTo: addMilliseconds(addHours(addDays(week, options.weekInterval.firstDay), startHour), originalEventDuration),
                    })
                    newEvents.push(newEvent)
                }
            }
            events.value = newEvents
        }

        if (options.typeOfInterval === 'month' && options.monthInterval.type) {
            const numberOfMonths = differenceInCalendarMonths(dateUpTo, dateFrom)
            const type = options.monthInterval.type
            let dayOfTheMonthByNumber = options.monthInterval.specificDayInMonth
            const dayOfTheMonthByDescription = options.monthInterval.everyDayInMonth.day
            const dayOfTheMonthType = options.monthInterval.everyDayInMonth.type

            for (let i = 0; i <= numberOfMonths; i = i + interval) {
                const currentMonth = addMonths(oryginalStartDate, i)
                const numberOfDaysInAMonth = getDaysInMonth(currentMonth)

                let startDate = null
                let endDate = null

                if (type === 'specificDay') {
                    if (dayOfTheMonthByNumber > numberOfDaysInAMonth) {
                        dayOfTheMonthByNumber = numberOfDaysInAMonth
                    }
                    startDate = setDate(addMonths(oryginalStartDate, i), dayOfTheMonthByNumber)
                    endDate = addMilliseconds(startDate, originalEventDuration)
                }

                if (type === 'dayOfWeek') {
                    let whichRepeatedDayOfMonth: number | 'last' = 1
                    switch (dayOfTheMonthType) {
                        case 'first': {
                            whichRepeatedDayOfMonth = 1
                            break
                        }
                        case 'second': {
                            whichRepeatedDayOfMonth = 2
                            break
                        }
                        case 'third': {
                            whichRepeatedDayOfMonth = 3
                            break
                        }
                        case 'fourth': {
                            whichRepeatedDayOfMonth = 4
                            break
                        }
                        case 'last': {
                            whichRepeatedDayOfMonth = 'last'
                            break
                        }
                    }
                    startDate = addHours(getNthWeekdayOfMonth(currentMonth.getFullYear(), currentMonth.getMonth(), weekDays.value[dayOfTheMonthByDescription.id].id, whichRepeatedDayOfMonth), startHour)
                    endDate = addMilliseconds(startDate, originalEventDuration)
                }
                if (isSameMonth(startDate, currentMonth)) {
                    const newEvent = useModelFactory('Event').create({
                        ...oryginalEvent,
                        number: null,
                        id: Math.random(),
                        dateFrom: startDate,
                        dateUpTo: endDate,
                    })
                    newEvents.push(newEvent)
                }
            }
            events.value = newEvents
        }
    }

    return {
        originalEventDuration,
        addEvent,
        removeEvent,
        getEventsByInterval,
        getWeekDays,
        weekDays
    }
}

export const useEventDuration = (event: ApiModel<'Event'>) => {
    const originalEventDuration = differenceInMilliseconds(event.dateUpTo, event.dateFrom)
    return originalEventDuration
}
