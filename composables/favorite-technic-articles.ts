import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { createQueryKeys } from '@lukemorales/query-key-factory'

const favoriteQueries = createQueryKeys('favorites', {
    getAll: () => ({
        queryKey: ['favorite-technic-articles'],
        queryFn: async () => {
            const result = await $fetch<ApiModelProps<'FavoriteTechnicArticle'>[]>(`/api/events/favorite-technic-articles`)
            return useModelFactory('FavoriteTechnicArticle').fromJson(result)
        },
        staleTime: Infinity,
        cacheTime: Infinity
    })
})

export function useFavoriteTechnicArticles() {
    const queryClient = useQueryClient()

    const queryKey = favoriteQueries.getAll().queryKey

    const { data, refetch, ...queryResult } = useQuery(favoriteQueries.getAll())

    const { mutateAsync: addFavorite } = useMutation({
        mutationFn: async (technicArticle: ApiModel<'TechnicArticle'>) => {
            return $fetch(`/api/events/favorite-technic-articles`, {
                body: { technicArticle },
                method: 'post'
            })
        },

        onMutate: async (technicArticle) => {
            // Cancel any outgoing refetches
            // (so they don't overwrite our optimistic update)
            await queryClient.cancelQueries({ queryKey })

            // Snapshot the previous value
            const previousFavorites = queryClient.getQueryData<ApiModel<'FavoriteTechnicArticle'>[]>(queryKey)

            // Optimistically update to the new value
            queryClient.setQueryData(queryKey, (favorites: ApiModel<'FavoriteTechnicArticle'>[]) => {
                return [
                    ...favorites,
                    useModelFactory('FavoriteTechnicArticle').create({
                        technicArticle
                    })
                ]
            })

            // Return a context object with the snapshotted value
            return { previousFavorites }
        },

        // If the mutation fails,
        // use the context returned from onMutate to roll back
        onError: (_err, _technicArticle, { previousFavorites }) => {
            queryClient.setQueryData(queryKey, previousFavorites)
        },

        // Always refetch after error or success:
        onSettled: () => {
            queryClient.invalidateQueries({ queryKey })
        }
    })

    const { mutateAsync: removeFavorite } = useMutation({
        mutationFn: async (favoriteTechnicArticle: ApiModel<'FavoriteTechnicArticle'>) => {
            return $fetch(`/api/events/favorite-technic-articles/${favoriteTechnicArticle.id}`, {
                method: 'delete'
            })
        },

        onMutate: async (favoriteTechnicArticle) => {
            await queryClient.cancelQueries({ queryKey })

            const previousFavorites = queryClient.getQueryData<ApiModel<'FavoriteTechnicArticle'>[]>(queryKey)

            queryClient.setQueryData(queryKey, (favorites: ApiModel<'FavoriteTechnicArticle'>[]) => {
                return [...favorites.filter(({ id }) => id !== favoriteTechnicArticle.id)]
            })

            return { previousFavorites }
        },

        onError: (_err, _technicArticle, { previousFavorites }) => {
            queryClient.setQueryData(queryKey, previousFavorites)
        },

        onSettled: () => {
            queryClient.invalidateQueries({ queryKey })
        }
    })

    function getFavoriteResource(resource: ApiModel<'Resource'>) {
        return data.value?.find((favorite) => {
            return [resource.article?.id, resource.externalArticle?.id].includes(favorite.technicArticle.id)
        })
    }

    function isFavoriteResource(resource: ApiModel<'Resource'>) {
        return !!getFavoriteResource(resource)
    }

    function isFavorableResource(resource: ApiModel<'Resource'>) {
        return resource.article?.id
    }

    return {
        data,
        addFavorite,
        removeFavorite,
        isFavoriteResource,
        getFavoriteResource,
        isFavorableResource,
        ...queryResult
    }
}

export const FavoriteTechnicArticles = Symbol() as InjectionKey<ReturnType<typeof useFavoriteTechnicArticles>>
