import { MaybeRef, useConfirmDialog, type UseConfirmDialogReturn } from '@vueuse/core'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import EventShareRequestStatus from '~~/enums/event-share-request-status'
import { InjectionKey } from 'vue'
import { ExtractedOrganization } from '~/components/event/sharing/community-search/types'

type EventSharingDialogControllers = {
    shareEventDialog: UseConfirmDialogReturn<null, ApiModel<'EventShareRequest'>[], null>
}

type EventSharingDialogNames = keyof EventSharingDialogControllers

export type EventSharingDialogTypes<T extends EventSharingDialogNames> = EventSharingDialogControllers[T]

export function useEventSharingDialogController<T extends EventSharingDialogNames>(dialog: T) {
    return useConfirmDialog() as EventSharingDialogTypes<T>
}

export const EventSharingDialogs = Symbol() as InjectionKey<EventSharingDialogControllers>

export const useShareEventMutations = function (event: MaybeRef<ApiModel<'Event'>>) {
    const queryClient = useQueryClient()

    const { mutateAsync: sendEventShareRequestDecision } = useMutation({
        mutationFn: ([shareRequestId, action]: [number, EventShareRequestStatus.ACCEPTED | EventShareRequestStatus.REJECTED]) => {
            return $fetch(`/api/events/${unref(event).id}/share-requests/${shareRequestId}/action`, {
                body: action,
                method: 'post'
            })
        },
        onSettled: async () => {
            await queryClient.refetchQueries(['events', unref(event).id])
        }
    })

    const { mutateAsync: createEventShareRequest } = useMutation({
        mutationFn: (EventShareRequests: ApiModel<'EventShareRequest'>[]) => {
            return $fetch(`/api/events/${unref(event).id}/share-requests`, {
                body: EventShareRequests,
                method: 'post'
            })
        },
        onSettled: async () => {
            await queryClient.refetchQueries(['events', unref(event).id])
        }
    })

    const { mutateAsync: resendEventShareRequest } = useMutation({
        mutationFn: (EventShareRequests: ApiModel<'EventShareRequest'>[]) => {
            return $fetch(`/api/events/${unref(event).id}/resend-share-requests`, {
                body: EventShareRequests,
                method: 'post'
            })
        }
    })

    const { mutateAsync: deleteEventShareRequest } = useMutation({
        mutationFn: (EventShareRequest: ApiModel<'EventShareRequest'>) => {
            return $fetch(`/api/events/${unref(event).id}/share-requests/${EventShareRequest.id}`, {
                method: 'delete'
            })
        },
        onSettled: async () => {
            await queryClient.refetchQueries(['events', unref(event).id])
        }
    })

    return {
        sendEventShareRequestDecision,
        createEventShareRequest,
        deleteEventShareRequest,
        resendEventShareRequest
    }
}

/**
 * Provide all functionality that's needed for the sharing of an event
 *
 * @param input
 */
export const useEventSharing = function (input: MaybeRef<ApiModel<'Event'>>) {
    const event = computed(() => {
        return unref(input)
    })

    const shareRequestsReceived = computed(() => {
        return unref(input)?.shareRequestsReceived || []
    })

    const shareRequests = computed(() => {
        return unref(input)?.shareRequests || []
    })

    /**
     * Generate and "import" all mutation queries
     */
    const mutations = useShareEventMutations(event)

    async function commit<
        T extends keyof typeof mutations,
        P extends Parameters<(typeof mutations)[T]>[0],
        O extends Parameters<(typeof mutations)[T]>[1]
    >(mutation: T, payload?: P, options?: O): Promise<ReturnType<(typeof mutations)[T]>> {
        return await mutations[mutation](payload as any, options as any)
    }

    return {
        event,
        commit,
        shareRequestsReceived,
        shareRequests
    }
}

/**
 * The key that can be used for injections
 */
export const EventSharingKey = Symbol() as InjectionKey<ReturnType<typeof useEventSharing>>


export const useEventSharingExtractedOrganisation = function () {
    const { data, organisationSearch, isError, isLoading } = useOrganisationSearch()

    const extractedOrganizations: Ref<ExtractedOrganization[]> = ref([])

    watch(
        () => ({ string: JSON.stringify(data) }),
        () => {
            const duplicated =
                Boolean(data.value) && data.value.items.length > 0
                    ? data.value.items.reduce<ExtractedOrganization[]>((prev, current) => {
                        return [
                            ...prev,
                            {
                                ...current,
                                active: true
                            },
                            ...current.parents.map((parent, index) => ({
                                ...parent,
                                active: false,
                                parents: current.parents.slice(index + 1)
                            }))
                        ]
                    }, [])
                    : []

            const ids = new Set(duplicated.map((item) => item.id))

            extractedOrganizations.value = Array.from(ids).map((id) => {
                const organizations = duplicated.filter((org) => org.id === id)
                const activeOrganizations = organizations.filter((org) => org.active)
                return activeOrganizations.length > 0 ? activeOrganizations[0] : organizations[0]
            })
        },
        { deep: true, immediate: true }
    )

    type ExecutedBy = { id: number; label: string }

    const processedData = ref<(ExtractedOrganization | ExecutedBy)[]>([])

    function extractId(item: ExtractedOrganization) {
        return item.id
    }

    function extractParentId(item: ExtractedOrganization) {
        return item.parents[0]?.id
    }

    watch(
        extractedOrganizations,
        () => {
            processedData.value = useForest(extractedOrganizations.value, extractId, extractParentId).flattened
        },
        { deep: true, immediate: true }
    )

    const organizationsMutation = () => ({
        mutate: organisationSearch,
        isError,
        isLoading,
        data: processedData
    })

    return {
        organizationsMutation,

    }

}
