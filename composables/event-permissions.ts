import type { ComputedRef, InjectionKey } from 'vue'
import type { MaybeRef } from '@vueuse/core'
import { camelCase } from 'lodash-es'

// Event Status WAITING_FOR_APPROVAL, APPROVED, CANCELED, FINISHED
// -- der Status WAITING_FOR_APPROVAL wird nicht behandelt

/**
 * General permissions
 *
 * - Drucken und Auswertungen, wenn das Wvent im Zustand "CANCELED" oder "FINISHED" ist.
 * -- Das soll wirklich verboten sein?
 */
const generalPermissions = ['print', 'create-report', 'export-to-calendar', 'duplicate'] as const

/**
 * Permission regarding master data and documents
 */
const dataPermissions = ['read-data', 'manage-data', 'read-documents', 'manage-documents'] as const

/**
 * Permissions regarding my signing up
 */
const signUpPermissions = ['modify-signing-up'] as const

/**
 * Lifecycle permissions
 *
 * - <PERSON><PERSON><PERSON><PERSON> (cancel) und Löschen (delete)?
 *  -- <PERSON> dü<PERSON> wirklich alle?
 *
 * - Publish
 * -- Gibt es dafür Regeln?
 */
const lifecyclePermissions = ['publish', 'cancel', 'reactivate', 'close', 'reopen', 'delete', 'edit'] as const

/**
 * Planning permmissions
 */
const planningPermissions = ['see-planning', 'read-event-posts', 'manage-event-posts', 'read-resources', 'manage-resources'] as const

/**
 * Sharing permmissions
 */
const sharingPermissions = ['share-event'] as const

/**
 * Permissions for registrations and management of registrars
 */
const registrationPermissions = ['read-registrators', 'read-registrations', 'manage-registrations', 'manage-registrators'] as const

/**
 * Combined set of all Registrations
 */
const permissions = [
    ...dataPermissions,
    ...lifecyclePermissions,
    ...generalPermissions,
    ...planningPermissions,
    ...registrationPermissions,
    ...signUpPermissions,
    ...sharingPermissions
]

export type EventPermissionType = (typeof permissions)[number]

type DashesToCamelCase<Str extends string> = Str extends `${infer Start}-${infer Rest}`
    ? `${Capitalize<Start>}${DashesToCamelCase<Rest>}` // Recursive calll
    : `${Capitalize<Str>}`

type Accessor = `can${DashesToCamelCase<EventPermissionType>}`

/**
 * This gives us the permissions of a user for a specific event object
 *
 * @param input
 */
export const useEventPermissions = function (input?: MaybeRef<ApiModel<'Event'>>) {
    const eventLocked = computed(() => {
        const event = unref(input)
        return event && ['FINISHED', 'CANCELED'].includes(event.status)
    })

    const canEdit = computed(() => {
        const event = unref(input)

        return !!event?.detailedPermission?.canEdit && !eventLocked.value
    })

    const canShare = computed(() => {
        const event = unref(input)
        return event?.status !== 'WAITING_FOR_APPROVAL' && canEdit.value
    })

    const canManageRegistrations = computed(() => {
        const event = unref(input)

        return !!event?.detailedPermission?.canRegister && !eventLocked.value
    })

    const canSeeRegistrations = computed(() => {
        const event = unref(input)
        return !!event?.detailedPermission?.canRegister
    })

    const canClose = computed(() => {
        const event = unref(input)

        if (event?.status === 'WAITING_FOR_APPROVAL') {
            return false
        }

        if (!event?.detailedPermission?.canClose) {
            return false
        }

        return event?.status === 'APPROVED'
    })

    const canDelete = computed(() => {
        const event = unref(input)
        return !!event?.detailedPermission?.canEdit && event?.status !== 'FINISHED'
    })

    const canReactivate = computed(() => {
        const event = unref(input)
        return !!event?.detailedPermission?.canOpen && event?.status === 'CANCELED'
    })

    const canReopen = computed(() => {
        const event = unref(input)
        return !!event?.detailedPermission?.canOpen && event?.status === 'FINISHED'
    })

    const canModifySigningUp = computed(() => {
        const event = unref(input)

        if (event?.status === 'WAITING_FOR_APPROVAL') {
            return false
        }

        return canEdit.value
    })

    const canPublish = computed(() => {
        const event = unref(input)

        if (event?.status === 'WAITING_FOR_APPROVAL') {
            return false
        }

        return canEdit.value
    })

    const canManageRegistrators = computed(() => {
        return canEdit.value
    })

    const canManageResources = computed(() => {
        const event = unref(input)

        if (event?.status === 'WAITING_FOR_APPROVAL') {
            return false
        }

        return canEdit.value
    })

    const noopYes = computed(() => {
        return true
    })

    const noopNo = computed(() => {
        return false
    })

    /**
     * Everything has to go through this function!
     *
     * @param permission
     */
    const hasPermission = function (permission: EventPermissionType): ComputedRef<boolean> {
        switch (permission) {
            case 'export-to-calendar':
                return noopYes

            case 'print':
                return canEdit

            case 'edit':
                return canEdit

            case 'create-report':
                return noopYes

            case 'see-planning':
            case 'read-data':
            case 'read-event-posts':
            case 'read-resources':
            case 'read-documents':
                return noopYes

            case 'manage-data':
            case 'manage-documents':
            case 'manage-event-posts':
                return canEdit

            case 'publish':
                return canPublish

            case 'share-event':
                return canShare

            case 'cancel':
                return canEdit

            case 'reactivate':
                return canReactivate

            case 'close':
                return canClose

            case 'reopen':
                return canReopen

            case 'delete':
                return canDelete

            case 'read-registrations':
                return canSeeRegistrations

            case 'manage-registrations':
                return canManageRegistrations

            case 'read-registrators':
                return canSeeRegistrations

            case 'manage-registrators':
                return canManageRegistrators

            case 'manage-resources':
                return canManageResources

            case 'modify-signing-up':
                return canModifySigningUp

            case 'duplicate':
            default:
                return noopNo
        }
    }

    /**
     * Build an accessor for every permission.
     *
     * The accessor is the camelcased variant of 'can-permission'
     *
     * canPermission: hasPermission(permission)
     */
    const accessors = permissions.reduce(function (accumulator, permission) {
        const accessor = camelCase(`can-${permission}`)
        accumulator[accessor as Accessor] = hasPermission(permission)
        return accumulator
    }, {} as Record<Accessor, ReturnType<typeof hasPermission>>)

    /**
     * We provide the `hasPermission` function together with the generated accessors
     */
    return {
        hasPermission,
        ...accessors
    }
}

export const EventPermissionsKey = Symbol() as InjectionKey<ReturnType<typeof useEventPermissions>>
