import Community from '~/enums/community'
import { invert } from 'lodash-es'

export const communitiesMap = {
    JRK: 'J',
    BER: 'B',
    BW: 'BW',
    WW: 'W',
    WUS: 'WuS'
}

export const communitiesMapInverted = invert(communitiesMap)

/**
 * This hook provides communities array
 *
 * It extracts all selected communities into the variable `selectedCommunities`
 *
 */
export function useCommunities(membershipEntries?: ApiModel<'CodeEntry'>[], communities?: Community[]) {
    const definedCommunities: Community[] = Object.values(Community)

    const selectedCommunities = ref<Community[]>(
        membershipEntries?.length > 0
            ? membershipEntries?.map((entry) => {
                return communitiesMapInverted[entry.value1] as Community
            })
            : communities ?
                communities
                :
                [Community.ALL]
    )

    function selectCommunity(currentSelection: Community[], selectedItem: Community): Community[] {
        if (selectedItem === Community.ALL) {
            return [Community.ALL]
        }

        if (currentSelection.includes(Community.ALL)) {
            return [selectedItem]
        }

        const selection = currentSelection.includes(selectedItem)
            ? currentSelection.filter((c) => c !== selectedItem)
            : [...currentSelection, selectedItem]

        return selection.length === 0 ? [selectedItem] : selection
    }

    return {
        definedCommunities,
        selectedCommunities,
        selectCommunity
    }
}
