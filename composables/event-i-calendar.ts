import { MaybeRef } from '@vueuse/core'
import { format } from 'date-fns'

export function useICalendar(input: MaybeRef<ApiModel<'Event'>[]>) {
    const calendarData = computed(() => {
        return unref(input).map((event) => {
            const { title, location, description } = useEventDescription(event)

            return {
                id: event.id,
                start: event.dateFrom,
                end: event.dateUpTo,
                summary: title.value,
                description: description.value,
                // we dont have organizer email, without it the ics doesnt load (tested on google cal)
                // organizer: { name: props.event.organisation.name, email: '' },
                location: location.value
            }
        })
    })

    const name = computed(() => {
        const events = unref(input)

        if (events.length === 1) {
            const { title } = useEventDescription(events[0])
            return `${title.value} – ${format(events[0].dateFrom, 'd-M-yy')}.ics`
        } else {
            return 'DRK-Ereignisse.ics'
        }
    })

    const fetch = useAsyncData(
        'ical-events',
        () =>
            $fetch<any>('/api/events.ics', {
                method: 'post',
                body: calendarData.value
            }),
        {
            immediate: false,
            transform(res) {
                return new Blob([res as string], { type: 'text/icalendar' })
            }
        }
    )

    return {
        name,
        fetch
    }
}
