import { useConfirmDialog, type UseConfirmDialogReturn } from '@vueuse/core'
import { type ComponentPublicInstance } from 'vue'
import { uniqueId } from 'lodash-es'

interface UiComposerInstance extends ComponentPublicInstance {}

type UiComposerConstructor = new (...args: any) => UiComposerInstance

// This gets the props that were defined with defineProps macro
type UiComposerProps<T extends UiComposerConstructor> = InstanceType<T>['$props']

// This gets the argunt for the function that's privied within a specific property
type UiComposerListenerArgs<
    T extends UiComposerConstructor,
    K extends string,
    Props extends Record<string, any> = UiComposerProps<T>
> = Props[K] extends (...args: any) => any ? Parameters<Props[K]>[0] : any

// Since vue creates "onEmit" props automatically we can retrieve arguments for the emitted "confirm" event.
type UiComposerConfirmData<T extends UiComposerConstructor, A extends Record<string, any> = UiComposerListenerArgs<T, 'onConfirm'>> = A //A['value'] extends undefined ? any : A['value'];

// Since vue creates "onEmit" props automatically we can retrieve arguments for the emitted "cancel" event.
type UiComposerCancelData<T extends UiComposerConstructor, A extends Record<string, any> = UiComposerListenerArgs<T, 'onCancel'>> = A // A['value'] extends undefined ? any : A['value'];

export type UiComposer<T extends UiComposerConstructor = UiComposerConstructor> = {
    id: string
    component: T
    props: UiComposerProps<T>
    controls: UseConfirmDialogReturn<any, UiComposerConfirmData<T>, UiComposerCancelData<T>>
}

/**
 * Get the current stack of composers
 *
 * – Should normally not be used from within components
 *
 * @internal
 */
export const useComposerStack = function () {
    return useState<UiComposer[]>('composers-state', () => [])
}

/**
 * Open a composer
 *
 * Lets users update or create data within a composer view and returns the modified data to the caller
 *
 * Within `components/composers.vue` that composer will be called that way:
 *
 *  <component
 *      :is="composer.component"
 *      v-bind="composer.props"
 *      :key="composer.id"
 *      @confirm="(data: any) => composer.controls.confirm(data)"
 *      @cancel="(data: any) => composer.controls.cancel(data)" />
 *
 * @param component
 * @param _props
 *
 */
export const useComposer = function <T extends UiComposerConstructor>(component: T, _props: UiComposerProps<T>) {
    const stack = useComposerStack()
    const controls = useConfirmDialog<UiComposerProps<T>, UiComposerConfirmData<T>, UiComposerCancelData<T>>()

    const props = ref(_props)
    controls.onReveal((data) => {
        if (!!data) {
            props.value = { ...data }
        }
    })

    stack.value.push({
        id: uniqueId('composer-'),
        component: markRaw(component),
        props,
        controls
    })

    return controls.reveal
}

/**
 * Retrieve the list of all active/visible composers
 *
 * @internal
 */
export const useVisibleComposers = function () {
    const stack = useComposerStack()
    return computed(() => {
        return stack.value.filter(({ controls }) => controls.isRevealed)
    })
}
