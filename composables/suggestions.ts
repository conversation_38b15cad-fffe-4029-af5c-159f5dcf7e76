import { useInfiniteQuery } from '@tanstack/vue-query'
import { refThrottled, debouncedRef } from '@vueuse/core'
import { useSearchParams } from '~/composables/search-params'

type ListResponse<T = any> = {
    totalItems: number
    offset: number
    limit: number
    items: T[]
}

/**
 * Hooks that provide autocomplete suggestions
 */

/**
 * Find technic articles based on a user input
 */
export function useInfiniteTechnicArticleSuggestions(useDebounce?: boolean) {
    const query = ref<string>(null)
    const limit = ref(10)

    // Initialize input with either debounce or throttle based on the flag
    const throttledOrDebouncedInput = useDebounce
        ? debouncedRef(query, 500)
        : refThrottled(query, 350, true, true)

    const isQueryEnabled = computed(() => {
        return !!throttledOrDebouncedInput.value
    })

    const { data, ...queryResult } = useInfiniteQuery({
        queryKey: ['search', 'technic-articles', throttledOrDebouncedInput, limit],

        queryFn: ({ pageParam = 0 }) => {
            return $fetch<ListResponse>('/api/technic/search', {
                query: {
                    searchTerm: throttledOrDebouncedInput.value,
                    offset: pageParam,
                    limit: limit.value
                }
            })
        },

        getNextPageParam: (lastPage, _pages) => {
            const offset = lastPage.offset + limit.value
            return offset < lastPage.totalItems ? offset : undefined
        },

        staleTime: Infinity,
        keepPreviousData: false,
        enabled: isQueryEnabled
    })

    const technicArticles = computed(() => {
        const options = []
        data.value?.pages.forEach(({ items }, index) => {
            options.push(...items)
        })

        return useModelFactory('TechnicArticle').fromJson(options)
    })

    return {
        query,
        technicArticles,
        ...queryResult,
        limit
    }
}

/**
 * Find members based on a user input
 */
export function useInfiniteMemberSuggestions(useDebounce?: boolean) {
    const query = ref<string>(null)
    const scope = ref<'GLOBAL_MEMBERS' | 'MY_MEMBERS'>('GLOBAL_MEMBERS')
    const limit = ref(10)

    // Initialize input with either debounce or throttle based on the flag
    const throttledOrDebouncedInput = useDebounce
        ? debouncedRef(query, 500)
        : refThrottled(query, 350, true, true)


    const isQueryEnabled = computed(() => {
        return !!throttledOrDebouncedInput.value
    })


    const { data, ...queryResult } = useInfiniteQuery({
        queryKey: ['search', 'members', throttledOrDebouncedInput, scope, limit],

        queryFn: ({ pageParam = 0 }) => {
            return $fetch<ListResponse>('/api/members/search', {
                query: {
                    searchTerm: throttledOrDebouncedInput.value,
                    searchScope: scope.value,
                    offset: pageParam,
                    limit: limit.value
                }
            })
        },

        getNextPageParam: (lastPage, _pages) => {
            const offset = lastPage.offset + limit.value
            return offset < lastPage.totalItems ? offset : undefined
        },

        staleTime: Infinity,
        keepPreviousData: false,
        enabled: isQueryEnabled
    })

    const members = computed(() => {

        const options = []
        data.value?.pages.forEach(({ items }, index) => {
            options.push(...items)
        })

        return useModelFactory('MemberData').fromJson(options)
    })



    return {
        scope,
        query,
        members,
        throttledOrDebouncedInput,
        ...queryResult,
        limit
    }
}

/**
 * Find events based on a user input
 */
export function useInfiniteEventsSuggestions(useDebounce?: boolean) {
    const query = ref<string>(null)
    const limit = ref(10)

    // Initialize input with either debounce or throttle based on the flag
    const throttledOrDebouncedInput = useDebounce
        ? debouncedRef(query, 500)
        : refThrottled(query, 350, true, true)

    const isQueryEnabled = computed(() => {
        return !!throttledOrDebouncedInput.value
    })

    const sortBy = useSearchParams({
        field: 'DATE_FROM',
        directionAsc: false
    }).serializeObject()

    const { data, ...queryResult } = useInfiniteQuery({
        queryKey: ['search', 'events', throttledOrDebouncedInput, limit],

        queryFn: ({ pageParam = 0 }) => {
            return $fetch<ListResponse>('/api/events', {
                query: {
                    searchTerm: throttledOrDebouncedInput.value,
                    offset: pageParam,
                    limit: limit.value,
                    sortBy
                }
            })
        },

        getNextPageParam: (lastPage, _pages) => {
            const offset = lastPage.offset + limit.value
            return offset < lastPage.totalItems ? offset : undefined
        },

        staleTime: Infinity,
        keepPreviousData: false,
        enabled: isQueryEnabled
    })

    const events = computed(() => {
        const options = []
        data.value?.pages.forEach(({ items }, index) => {
            options.push(...items)
        })

        return useModelFactory('Event').fromJson(options)
    })

    return {
        query,
        events,
        ...queryResult,
        limit
    }
}

/**
 * Find adress based on a user input
 */

export function useInfiniteAdressSuggestions(useDebounce?: boolean) {
    const query = ref<string>(null)
    const limit = ref(5)


    // Initialize input with either debounce or throttle based on the flag
    const throttledOrDebouncedInput = useDebounce
        ? debouncedRef(query, 500)
        : refThrottled(query, 350, true, true)

    const isQueryEnabled = computed(() => {
        return !!throttledOrDebouncedInput.value
    })

    const { data, ...queryResult } = useInfiniteQuery({
        queryKey: ['search', 'address', throttledOrDebouncedInput, limit],

        queryFn: ({ pageParam = 0 }) => {
            return $fetch<ListResponse>('/api/address/search', {
                query: {
                    searchTerm: throttledOrDebouncedInput.value,
                    offset: pageParam,
                    limit: limit.value
                }
            })
        },

        getNextPageParam: (lastPage, _pages) => {
            const offset = lastPage.offset + limit.value
            return offset < lastPage.totalItems ? offset : undefined
        },

        staleTime: Infinity,
        keepPreviousData: false,
        enabled: isQueryEnabled
    })

    const addressContacts = computed(() => {
        const options = []
        data.value?.pages.forEach(({ items }, index) => {
            options.push(...items)
        })

        return useModelFactory('AddressContact').fromJson(options)
    })

    return {
        query,
        addressContacts,
        ...queryResult,
        limit
    }
}
