import { Ref } from 'vue'

function generateState(ids: string[], value: boolean): Record<string, boolean> {
    return ids.reduce<Record<string, boolean>>((previousValue, currentValue) => ({ ...previousValue, [currentValue]: value }), {})
}

export function useSelectableList(ids: Ref<string[]>) {
    const selectableItems = ref(generateState(ids.value, false))

    watch(ids, (newValue) => {
        selectableItems.value = generateState(newValue, false)
    })

    const selectedItems = computed(() =>
        Object.entries(selectableItems.value)
            .filter(([, value]) => value)
            .map(([key]) => key)
    )

    const isAnyItemSelected = computed(() => selectedItems.value.length > 0)

    const toggleAllItems = () => {
        const allSelected = selectedItems.value.length === ids.value.length
        selectableItems.value = generateState(ids.value, !allSelected)
    }

    return {
        selectableItems,
        selectedItems,
        isAnyItemSelected,
        toggleAllItems
    }
}
