export const useLoader = function () {
    const isLoading = ref(false)

    function showLoader() {
        isLoading.value = true
    }

    function hideLoader() {
        isLoading.value = false
    }

    return {
        isLoading,
        showLoader,
        hideLoader
    }
}

/**
 * Event planning loader (calendar)
 */
export const EventPlanningLoader = Symbol() as InjectionKey<ReturnType<typeof useLoader>>

/**
 * Event manage resources loader
 */
export const ManageResourcesLoader = Symbol() as InjectionKey<ReturnType<typeof useLoader>>
