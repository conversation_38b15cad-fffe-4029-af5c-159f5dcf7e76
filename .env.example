# BASE_URL=http://localhost:3000

KEYCLOAK_CLIENT_ID=portal-vue-client
KEYCLOAK_REALM=drkdemo
KEYCLOAK_URL=https://login.drkserver.org/auth
KEYCLOAK_CLIENT_SECRET=super-secret

# Session Storage
#
# Sessions are stored within Redis.
#
# It's possible to connect to Red<PERSON> in three different modes:
# - Redis (Standalone)
# - Redis-Sentinel
# - Redis-Cluster
#

# -- Standalone mode --
#
# This must be set when you want to run redis in default (standalone) mode
REDIS_URL=127.0.0.1:6379


# -- Sentinel-Mode --
#
# This configuration takes precedence over standalone mode.
#
# If you want to run redis in sentinel mode you have to fill this variable;
# It expect a list of URLS (<host>:<port>) separated by whitespaces
# REDIS_SENTINELS=127.0.0.1:6379 127.0.0.1:6378

# When in sentinel mode: These varaibles can be taken into account for `master-name` and `sentinel-credentials`:
#
# REDIS_SENTINEL_NAME=
# REDIS_SENTINEL_USERNAME=
# REDIS_SENTINEL_PASSWORD=

# -- Cluster Mode --
#
#  This configuration takes precedence over sentinel mode.
#
# If you want to run redis in sentinel mode you have to fill this variable.
# A list of URLS (<host>:<port>) separated by whitespaces is expected
# REDIS_NODES=127.0.0.1:6379 127.0.0.1:6378

# Common options for all redis modes

REDIS_KEY_PREFIX=__nuxt
#REDIS_USERNAME=
#REDIS_PASSWORD=

API_APP_NAME='Local Development'
API_BASE_URL=https://demo-api.drkserver.org

# SENTRY_DSN=https://<EMAIL>/41

# If set, then will sent sentry data first to this url and then to sentry.mogic.com/41
# SENTRY_TUNNEL=

# That URL is used for generation of legacy URLs and calls to "device detection"
PORTAL_URL=https://demo.drkserver.org


# SENTRY runtime configuration
SENTRY_DSN=
SENTRY_ENVIRONMENT=testing
SENTRY_TRACE_PROPAGATION_TARGET=


# This is needed for uploading source maps to SENTRY
SENTRY_AUTH_TOKEN=
SENTRY_ORG=mogic
SENTRY_PROJECT=em-basis-client
SENTRY_URL=https://sentry.mogic.com
